-- Navigation table for autobooks application
-- This table stores the navigation menu structure and routing information

CREATE TABLE IF NOT EXISTS `autobooks_navigation` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `parent_path` varchar(255) NOT NULL DEFAULT '' COMMENT 'Parent path for hierarchical navigation (e.g., "root", "admin/users")',
  `route_key` varchar(100) NOT NULL COMMENT 'Unique route identifier within parent path',
  `name` varchar(100) NOT NULL COMMENT 'Display name for the navigation item',
  `icon` varchar(50) DEFAULT NULL COMMENT 'Icon identifier for the navigation item',
  `required_roles` json DEFAULT NULL COMMENT 'JSON array of roles required to access this route',
  `sort_order` int(11) DEFAULT 0 COMMENT 'Sort order for navigation items within the same parent',
  `show_navbar` tinyint(1) DEFAULT 1 COMMENT 'Whether to show this item in the navigation bar',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_route` (`parent_path`, `route_key`) COMMENT 'Ensure unique route within parent path',
  KEY `idx_parent_path` (`parent_path`),
  KEY `idx_route_key` (`route_key`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Navigation menu structure and routing';

-- Sample data for testing (optional)
-- Uncomment the following lines if you want to insert sample navigation items

/*
INSERT INTO `autobooks_navigation` 
(`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `sort_order`, `show_navbar`) 
VALUES 
('root', 'dashboard', 'Dashboard', 'dashboard', '["user", "admin"]', 1, 1),
('root', 'subscriptions', 'Subscriptions', 'subscription', '["user", "admin"]', 2, 1),
('root', 'customers', 'Customers', 'users', '["admin"]', 3, 1),
('root', 'reports', 'Reports', 'chart-bar', '["admin"]', 4, 1),
('root', 'settings', 'Settings', 'cog', '["admin"]', 5, 1);
*/
