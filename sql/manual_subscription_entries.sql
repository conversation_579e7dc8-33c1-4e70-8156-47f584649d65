-- Manual Subscription Entries Table
-- This table stores subscription information that is manually entered
-- and can be matched with Autodesk API data

CREATE TABLE IF NOT EXISTS `manual_subscription_entries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_name` varchar(255) NOT NULL,
  `email_address` varchar(255) DEFAULT NULL,
  `contact_email` varchar(255) DEFAULT NULL,
  `admin_email` varchar(255) DEFAULT NULL,
  `contact_name` varchar(255) DEFAULT NULL,
  `phone_number` varchar(50) DEFAULT NULL,
  `subscription_reference` varchar(100) DEFAULT NULL,
  `subscription_number` varchar(100) DEFAULT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `product_name` varchar(255) DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `address_line_1` varchar(255) DEFAULT NULL,
  `address_line_2` varchar(255) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state_province` varchar(100) DEFAULT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_company_name` (`company_name`),
  KEY `idx_email_address` (`email_address`),
  KEY `idx_contact_email` (`contact_email`),
  KEY `idx_admin_email` (`admin_email`),
  KEY `idx_subscription_reference` (`subscription_reference`),
  KEY `idx_subscription_number` (`subscription_number`),
  KEY `idx_reference_number` (`reference_number`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_end_date` (`end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Subscription matching log table
-- This table tracks automatic matches between Autodesk API data and manual entries
CREATE TABLE IF NOT EXISTS `subscription_matches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `autodesk_subscription_id` varchar(100) NOT NULL,
  `autodesk_subscription_reference` varchar(100) DEFAULT NULL,
  `manual_entry_id` int(11) NOT NULL,
  `match_type` enum('email','company','reference','manual') NOT NULL,
  `confidence_score` decimal(3,2) NOT NULL DEFAULT 0.00,
  `is_confirmed` tinyint(1) NOT NULL DEFAULT 0,
  `is_rejected` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_match` (`autodesk_subscription_id`, `manual_entry_id`),
  KEY `idx_autodesk_subscription_id` (`autodesk_subscription_id`),
  KEY `idx_autodesk_subscription_reference` (`autodesk_subscription_reference`),
  KEY `idx_manual_entry_id` (`manual_entry_id`),
  KEY `idx_match_type` (`match_type`),
  KEY `idx_confidence_score` (`confidence_score`),
  KEY `idx_is_confirmed` (`is_confirmed`),
  CONSTRAINT `fk_manual_entry` FOREIGN KEY (`manual_entry_id`) REFERENCES `manual_subscription_entries` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some sample data for testing
INSERT INTO `manual_subscription_entries` 
(`company_name`, `email_address`, `contact_name`, `subscription_reference`, `product_name`, `quantity`, `start_date`, `end_date`, `status`, `notes`) 
VALUES 
('Test Company Ltd', '<EMAIL>', 'John Smith', 'TEST-12345-67890', 'AutoCAD LT', 5, '2024-01-01', '2025-01-01', 'Active', 'Manually entered test subscription'),
('Sample Corp', '<EMAIL>', 'Jane Doe', 'SAMPLE-98765-43210', 'AutoCAD', 10, '2024-02-01', '2025-02-01', 'Active', 'Another test entry'),
('Demo Industries', '<EMAIL>', 'Bob Johnson', 'DEMO-11111-22222', 'Inventor Professional', 3, '2024-03-01', '2025-03-01', 'Expiring Soon', 'Demo subscription entry')
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;
