[database_schema] [2025-08-11 13:05:52] [database.class.php:1428] Array\n(\n    [query] => CREATE TABLE autobooks_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VA<PERSON><PERSON><PERSON>(255) NULL, `sold_to_number` INT NULL, `vendor_name` VA<PERSON>HAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VA<PERSON>HAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARC<PERSON><PERSON>(255) NULL, `end_customer_contact_name` VA<PERSON>HAR(255) NULL, `end_customer_contact_email` VA<PERSON><PERSON>R(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 13:05:52\n)\n
[database_schema] [2025-08-11 13:06:41] [database.class.php:1428] Array\n(\n    [query] => CREATE TABLE autobooks_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 13:06:41\n)\n
[database_schema] [2025-08-11 13:16:15] [database.class.php:1428] Array\n(\n    [query] => CREATE TABLE autobooks_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 13:16:15\n)\n
[database_schema] [2025-08-11 13:20:37] [database.class.php:1428] Array\n(\n    [query] => CREATE TABLE autobooks_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 13:20:37\n)\n
[database_schema] [2025-08-11 13:29:54] [database.class.php:1428] Array\n(\n    [query] => CREATE TABLE autobooks_Sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_Sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 13:29:54\n)\n
[database_schema] [2025-08-11 13:35:57] [database.class.php:1501] Array\n(\n    [query] => CREATE TABLE autobooks_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 13:35:57\n)\n
[database_schema] [2025-08-11 13:47:33] [database.class.php:1501] Array\n(\n    [query] => CREATE TABLE autobooks_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 13:47:33\n)\n
[database_schema] [2025-08-11 13:49:44] [database.class.php:1501] Array\n(\n    [query] => CREATE TABLE autobooks_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 13:49:44\n)\n
[database_schema] [2025-08-11 14:22:09] [database.class.php:1501] Array\n(\n    [query] => CREATE TABLE autobooks_import_sketchup_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `sold_to_name` VARCHAR(255) NULL, `sold_to_number` INT NULL, `vendor_name` VARCHAR(255) NULL, `reseller_number` INT NULL, `reseller_vendor_id` VARCHAR(255) NULL, `end_customer_vendor_id` INT NULL, `end_customer_name` VARCHAR(255) NULL, `end_customer_address_1` VARCHAR(255) NULL, `end_customer_address_2` VARCHAR(255) NULL, `end_customer_address_3` VARCHAR(255) NULL, `end_customer_city` VARCHAR(255) NULL, `end_customer_state` VARCHAR(255) NULL, `end_customer_zip_code` VARCHAR(255) NULL, `end_customer_country` VARCHAR(255) NULL, `end_customer_account_type` VARCHAR(255) NULL, `end_customer_contact_name` VARCHAR(255) NULL, `end_customer_contact_email` VARCHAR(255) NULL, `end_customer_contact_phone` VARCHAR(255) NULL, `end_customer_industry_segment` VARCHAR(255) NULL, `agreement_program_name` VARCHAR(255) NULL, `agreement_number` INT NULL, `agreement_start_date` VARCHAR(255) NULL, `agreement_end_date` VARCHAR(255) NULL, `agreement_terms` VARCHAR(255) NULL, `agreement_type` VARCHAR(255) NULL, `agreement_status` VARCHAR(255) NULL, `agreement_support_level` VARCHAR(255) NULL, `agreement_days_due` INT NULL, `agreement_autorenew` INT NULL, `product_name` VARCHAR(255) NULL, `product_family` VARCHAR(255) NULL, `product_market_segment` VARCHAR(255) NULL, `product_release` VARCHAR(255) NULL, `product_type` VARCHAR(255) NULL, `product_deployment` VARCHAR(255) NULL, `product_sku` VARCHAR(255) NULL, `product_sku_description` VARCHAR(255) NULL, `product_part` VARCHAR(255) NULL, `product_list_price` INT NULL, `product_list_price_currency` VARCHAR(255) NULL, `subscription_id` VARCHAR(255) NULL, `subscription_serial_number` VARCHAR(255) NULL, `subscription_status` VARCHAR(255) NULL, `subscription_quantity` INT NULL, `subscription_start_date` VARCHAR(255) NULL, `subscription_end_date` VARCHAR(255) NULL, `subscription_contact_name` VARCHAR(255) NULL, `subscription_contact_email` VARCHAR(255) NULL, `subscription_level` VARCHAR(255) NULL, `subscription_days_due` INT NULL, `quotation_id` VARCHAR(255) NULL, `quotation_type` VARCHAR(255) NULL, `quotation_vendor_id` INT NULL, `quotation_deal_registration_number` VARCHAR(255) NULL, `quotation_status` VARCHAR(255) NULL, `quotation_resellerpo_previous` VARCHAR(255) NULL, `quotation_due_date` VARCHAR(255) NULL, `flaer_phase` VARCHAR(255) NULL, `updated` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_sketchup_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 14:22:09\n)\n
[database_schema] [2025-08-11 14:22:52] [database.class.php:1501] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 14:22:52\n)\n
[database_schema] [2025-08-11 21:50:31] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 21:50:31\n)\n
[database_schema] [2025-08-11 21:59:29] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-11 21:59:29\n)\n
[database_schema] [2025-08-12 08:27:27] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-12 08:27:27\n)\n
[database_schema] [2025-08-12 08:33:09] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-12 08:33:09\n)\n
[database_schema] [2025-08-12 08:39:04] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-12 08:39:04\n)\n
[database_schema] [2025-08-12 08:45:38] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-12 08:45:38\n)\n
[database_schema] [2025-08-12 08:54:39] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-12 08:54:39\n)\n
[database_schema] [2025-08-12 09:01:01] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-12 09:01:01\n)\n
[database_schema] [2025-08-12 09:09:08] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-12 09:09:08\n)\n
[database_schema] [2025-08-12 09:19:57] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-12 09:19:57\n)\n
[database_schema] [2025-08-12 09:42:21] [database.class.php:1577] Array\n(\n    [query] => CREATE TABLE autobooks_import_bluebeam_data (`id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY, `serial_number` VARCHAR(255) NULL, `name` VARCHAR(255) NULL, `contract` VARCHAR(255) NULL, `product_name` VARCHAR(255) NULL, `quantity` VARCHAR(255) NULL, `end_date` VARCHAR(255) NULL, `account_primary_reseller_name` VARCHAR(255) NULL, `order_po_number` VARCHAR(255) NULL, `order_shipping_address` VARCHAR(255) NULL, `order_shipping_city` VARCHAR(255) NULL, `order_shipping_state_province` VARCHAR(255) NULL, `order_shipping_country` VARCHAR(255) NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP)\n    [table] => autobooks_import_bluebeam_data\n    [type] => CREATE\n    [timestamp] => 2025-08-12 09:42:21\n)\n
