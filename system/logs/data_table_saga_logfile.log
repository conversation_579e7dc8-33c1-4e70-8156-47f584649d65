[data_table_saga] [2025-08-11 08:41:41] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:41:41] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:13] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:13] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:14] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:14] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:15] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:15] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:16] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:16] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:16] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:16] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:32] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:32] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:35] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:35] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:36] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:36] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:36] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:36] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:40] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 08:42:40] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:04:08] [data-table.edge.php:146] Data table template: table=system_logs, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:04:13] [data-table.edge.php:146] Data table template: table=system_logs, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:15:49] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:15:49] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:18:11] [data-table.edge.php:146] Data table template: table=system_logs, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:18:24] [data-table.edge.php:146] Data table template: table=system_logs, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:18:27] [data-table.edge.php:146] Data table template: table=system_logs, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:19:37] [data-table.edge.php:146] Data table template: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:19:37] [data-table-column-manager.edge.php:46]  Column manager props: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:23:37] [data-table.edge.php:146] Data table template: table=system_logs, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:23:40] [data-table.edge.php:146] Data table template: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:23:40] [data-table-column-manager.edge.php:46]  Column manager props: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:34:08] [data-table.edge.php:146] Data table template: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:34:08] [data-table-column-manager.edge.php:46]  Column manager props: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:34:58] [data-table.edge.php:146] Data table template: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:34:58] [data-table-column-manager.edge.php:46]  Column manager props: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:36:08] [data-table.edge.php:146] Data table template: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:36:08] [data-table-column-manager.edge.php:46]  Column manager props: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:36:11] [data-table.edge.php:146] Data table template: table=system_logs_logs, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:36:17] [data-table.edge.php:146] Data table template: table=system_logs_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:36:30] [data-table.edge.php:146] Data table template: table=system_logs, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:36:35] [data-table.edge.php:146] Data table template: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:36:35] [data-table-column-manager.edge.php:46]  Column manager props: table=system_logs_view, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:37:24] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:37:24] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:57:20] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 09:57:20] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:02:21] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:02:21] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:02:22] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:02:22] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:02:24] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:02:24] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:02:40] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:02:40] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:03:12] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:03:12] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:06:03] [data-table.edge.php:146] Data table template: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:06:03] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:14:00] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:14:00] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:14:48] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:14:48] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:16:16] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-11 13:16:16\n            [updated_at] => 2025-08-11 13:16:16\n        )\n\n    [table_name] => autobooks_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-11 13:16:27] [data-table.edge.php:146] Data table template: table=autobooks_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:16:27] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:20:38] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_855a49c592df215a4abe977e4b4ebe1e\n                            [label] => Sold To Name\n                            [field] => sold_to_name\n                            [fields] => Array\n                                (\n                                    [0] => sold_to_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_1a78581b905a2b8b3819cda087a681df\n                            [label] => Sold To Number\n                            [field] => sold_to_number\n                            [fields] => Array\n                                (\n                                    [0] => sold_to_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_0ec1da7f1dc2f8abef07b11a3193ef57\n                            [label] => Vendor Name\n                            [field] => vendor_name\n                            [fields] => Array\n                                (\n                                    [0] => vendor_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_2824711b2f4f6060078d068bf40f1e05\n                            [label] => Reseller Number\n                            [field] => reseller_number\n                            [fields] => Array\n                                (\n                                    [0] => reseller_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_46140d0548ed0b78e29e04793ef2af1b\n                            [label] => Reseller Vendor\n                            [field] => reseller_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => reseller_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_c706df21763a2714816b0a44cfe646c7\n                            [label] => End Customer Vendor\n                            [field] => end_customer_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_033954cce10564766238dfac726c2258\n                            [label] => End Customer Name\n                            [field] => end_customer_name\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_6e034352d34c13782d8d0bc5d65011bd\n                            [label] => End Customer Address 1\n                            [field] => end_customer_address_1\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_1\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_ea92edd1525cbbe624e2a16347e81e08\n                            [label] => End Customer Address 2\n                            [field] => end_customer_address_2\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_2\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_fd89e44f39c29b2e6e5ee8afcf84fe01\n                            [label] => End Customer Address 3\n                            [field] => end_customer_address_3\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_3\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_58125853b8960d245a3b5005f35a258c\n                            [label] => End Customer City\n                            [field] => end_customer_city\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_1f0774740183acc085318ae5debb1a62\n                            [label] => End Customer State\n                            [field] => end_customer_state\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_state\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_3efae453b93cd454ce02cf110d95f12d\n                            [label] => End Customer Zip Code\n                            [field] => end_customer_zip_code\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_zip_code\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_20d02fa8403a932b1721c8d870ae0495\n                            [label] => End Customer Country\n                            [field] => end_customer_country\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [15] => Array\n                        (\n                            [id] => col_15_8915939119a19af6a75ea0edf6663bba\n                            [label] => End Customer Account Type\n                            [field] => end_customer_account_type\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_account_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [16] => Array\n                        (\n                            [id] => col_16_5fdcafa9911363ca87918c99782fea0c\n                            [label] => End Customer Contact Name\n                            [field] => end_customer_contact_name\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [17] => Array\n                        (\n                            [id] => col_17_6d1e35008cf647b5c049c591cd4d3b7e\n                            [label] => End Customer Contact Email\n                            [field] => end_customer_contact_email\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_email\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [18] => Array\n                        (\n                            [id] => col_18_948bf27ad83a985d151cfdedc2fd4adf\n                            [label] => End Customer Contact Phone\n                            [field] => end_customer_contact_phone\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_phone\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [19] => Array\n                        (\n                            [id] => col_19_c4aaf1d01f58e409021771db8b5bcc31\n                            [label] => End Customer Industry Segment\n                            [field] => end_customer_industry_segment\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_industry_segment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [20] => Array\n                        (\n                            [id] => col_20_54215071b2e666430d1f2ef806d223e6\n                            [label] => Agreement Program Name\n                            [field] => agreement_program_name\n                            [fields] => Array\n                                (\n                                    [0] => agreement_program_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [21] => Array\n                        (\n                            [id] => col_21_881bfc8855aa6d118a647f50872f9c37\n                            [label] => Agreement Number\n                            [field] => agreement_number\n                            [fields] => Array\n                                (\n                                    [0] => agreement_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [22] => Array\n                        (\n                            [id] => col_22_f67fe48b64d22349cd8a3679e72358ad\n                            [label] => Agreement Start Date\n                            [field] => agreement_start_date\n                            [fields] => Array\n                                (\n                                    [0] => agreement_start_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [23] => Array\n                        (\n                            [id] => col_23_ee6ffb2ed1207134c2506d8e95f6f68a\n                            [label] => Agreement End Date\n                            [field] => agreement_end_date\n                            [fields] => Array\n                                (\n                                    [0] => agreement_end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [24] => Array\n                        (\n                            [id] => col_24_2d017dd473b0b2349dceb1d7f33e3e44\n                            [label] => Agreement Terms\n                            [field] => agreement_terms\n                            [fields] => Array\n                                (\n                                    [0] => agreement_terms\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [25] => Array\n                        (\n                            [id] => col_25_8af78a35c6e73e79bf51fc8563b871fe\n                            [label] => Agreement Type\n                            [field] => agreement_type\n                            [fields] => Array\n                                (\n                                    [0] => agreement_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [26] => Array\n                        (\n                            [id] => col_26_adb05f2097fd47dcf014a7b073b34987\n                            [label] => Agreement Status\n                            [field] => agreement_status\n                            [fields] => Array\n                                (\n                                    [0] => agreement_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [27] => Array\n                        (\n                            [id] => col_27_b4d565e162b8e7bc13cb12729aae502a\n                            [label] => Agreement Support Level\n                            [field] => agreement_support_level\n                            [fields] => Array\n                                (\n                                    [0] => agreement_support_level\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [28] => Array\n                        (\n                            [id] => col_28_52c6a8653a43c2ab289ef6e42d0dcc08\n                            [label] => Agreement Days Due\n                            [field] => agreement_days_due\n                            [fields] => Array\n                                (\n                                    [0] => agreement_days_due\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [29] => Array\n                        (\n                            [id] => col_29_a6a4ccca71939f4261a20b0006726122\n                            [label] => Agreement Autorenew\n                            [field] => agreement_autorenew\n                            [fields] => Array\n                                (\n                                    [0] => agreement_autorenew\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [30] => Array\n                        (\n                            [id] => col_30_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [31] => Array\n                        (\n                            [id] => col_31_31981304965b1a7ee49c64d093e02e94\n                            [label] => Product Family\n                            [field] => product_family\n                            [fields] => Array\n                                (\n                                    [0] => product_family\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [32] => Array\n                        (\n                            [id] => col_32_7d11591eed252e3b1e06c3c8b170dd38\n                            [label] => Product Market Segment\n                            [field] => product_market_segment\n                            [fields] => Array\n                                (\n                                    [0] => product_market_segment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [33] => Array\n                        (\n                            [id] => col_33_b647350956b1adeea9069ef1306de753\n                            [label] => Product Release\n                            [field] => product_release\n                            [fields] => Array\n                                (\n                                    [0] => product_release\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [34] => Array\n                        (\n                            [id] => col_34_b1d9df66b969ebda1ccfb1be323f165b\n                            [label] => Product Type\n                            [field] => product_type\n                            [fields] => Array\n                                (\n                                    [0] => product_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [35] => Array\n                        (\n                            [id] => col_35_a4ec132ba158d45a164578a02f6ef646\n                            [label] => Product Deployment\n                            [field] => product_deployment\n                            [fields] => Array\n                                (\n                                    [0] => product_deployment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [36] => Array\n                        (\n                            [id] => col_36_197d90cc45a4687eb1ef153ac86bc05f\n                            [label] => Product Sku\n                            [field] => product_sku\n                            [fields] => Array\n                                (\n                                    [0] => product_sku\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [37] => Array\n                        (\n                            [id] => col_37_b487a1edb0f07fa54d48393bbfea8139\n                            [label] => Product Sku Description\n                            [field] => product_sku_description\n                            [fields] => Array\n                                (\n                                    [0] => product_sku_description\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [38] => Array\n                        (\n                            [id] => col_38_fdff8146fc34b8e8e8ee5abe920d4806\n                            [label] => Product Part\n                            [field] => product_part\n                            [fields] => Array\n                                (\n                                    [0] => product_part\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [39] => Array\n                        (\n                            [id] => col_39_ceefc2cd7f84cf31ee379912ce351ad3\n                            [label] => Product List Price\n                            [field] => product_list_price\n                            [fields] => Array\n                                (\n                                    [0] => product_list_price\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [40] => Array\n                        (\n                            [id] => col_40_a851a28a6e219e3ec67e534c626aeaf3\n                            [label] => Product List Price Currency\n                            [field] => product_list_price_currency\n                            [fields] => Array\n                                (\n                                    [0] => product_list_price_currency\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [41] => Array\n                        (\n                            [id] => col_41_ef42673f634b68005996f2c64d6a72e3\n                            [label] => Subscription\n                            [field] => subscription_id\n                            [fields] => Array\n                                (\n                                    [0] => subscription_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [42] => Array\n                        (\n                            [id] => col_42_8c20c72b0daef191013b97458e02bb25\n                            [label] => Subscription Serial Number\n                            [field] => subscription_serial_number\n                            [fields] => Array\n                                (\n                                    [0] => subscription_serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [43] => Array\n                        (\n                            [id] => col_43_2ec2483de1ecdf1c9ccb681489ffa517\n                            [label] => Subscription Status\n                            [field] => subscription_status\n                            [fields] => Array\n                                (\n                                    [0] => subscription_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [44] => Array\n                        (\n                            [id] => col_44_5401f206636abe557cb9b60eca0ca28f\n                            [label] => Subscription Quantity\n                            [field] => subscription_quantity\n                            [fields] => Array\n                                (\n                                    [0] => subscription_quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [45] => Array\n                        (\n                            [id] => col_45_9c01b07be7b67284c181d0855827c151\n                            [label] => Subscription Start Date\n                            [field] => subscription_start_date\n                            [fields] => Array\n                                (\n                                    [0] => subscription_start_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [46] => Array\n                        (\n                            [id] => col_46_ab5851cb73c308ad66653249acbaefe3\n                            [label] => Subscription End Date\n                            [field] => subscription_end_date\n                            [fields] => Array\n                                (\n                                    [0] => subscription_end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [47] => Array\n                        (\n                            [id] => col_47_e9775f536b7ca1a1fcc63bbce1a54d62\n                            [label] => Subscription Contact Name\n                            [field] => subscription_contact_name\n                            [fields] => Array\n                                (\n                                    [0] => subscription_contact_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [48] => Array\n                        (\n                            [id] => col_48_e1e0a62a0901372172e3a54b62492a89\n                            [label] => Subscription Contact Email\n                            [field] => subscription_contact_email\n                            [fields] => Array\n                                (\n                                    [0] => subscription_contact_email\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [49] => Array\n                        (\n                            [id] => col_49_aa1d9b48f2e7b9108127d34afdb14da8\n                            [label] => Subscription Level\n                            [field] => subscription_level\n                            [fields] => Array\n                                (\n                                    [0] => subscription_level\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [50] => Array\n                        (\n                            [id] => col_50_adafdf8e0515f1788d271d09e44dd3f2\n                            [label] => Subscription Days Due\n                            [field] => subscription_days_due\n                            [fields] => Array\n                                (\n                                    [0] => subscription_days_due\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [51] => Array\n                        (\n                            [id] => col_51_2b6213d56e0299b7b5a352f7337328d8\n                            [label] => Quotation\n                            [field] => quotation_id\n                            [fields] => Array\n                                (\n                                    [0] => quotation_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [52] => Array\n                        (\n                            [id] => col_52_737c3c888ee0cc2600a2dc0d7f55ed05\n                            [label] => Quotation Type\n                            [field] => quotation_type\n                            [fields] => Array\n                                (\n                                    [0] => quotation_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [53] => Array\n                        (\n                            [id] => col_53_eb42bc3afe7c7f333989b1a3ab57bac0\n                            [label] => Quotation Vendor\n                            [field] => quotation_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => quotation_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [54] => Array\n                        (\n                            [id] => col_54_f83eab3e651df57f2cfbdaedec949a82\n                            [label] => Quotation Deal Registration Number\n                            [field] => quotation_deal_registration_number\n                            [fields] => Array\n                                (\n                                    [0] => quotation_deal_registration_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [55] => Array\n                        (\n                            [id] => col_55_a52a77131c467520565de2fb0c065189\n                            [label] => Quotation Status\n                            [field] => quotation_status\n                            [fields] => Array\n                                (\n                                    [0] => quotation_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [56] => Array\n                        (\n                            [id] => col_56_df1b0dfe471a20da3523bf2c525c3f39\n                            [label] => Quotation Resellerpo Previous\n                            [field] => quotation_resellerpo_previous\n                            [fields] => Array\n                                (\n                                    [0] => quotation_resellerpo_previous\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [57] => Array\n                        (\n                            [id] => col_57_5add4b7dce2f0f862189499e578133ea\n                            [label] => Quotation Due Date\n                            [field] => quotation_due_date\n                            [fields] => Array\n                                (\n                                    [0] => quotation_due_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [58] => Array\n                        (\n                            [id] => col_58_1180a372ed0e4f6d0f6d1babd14e28fb\n                            [label] => Flaer Phase\n                            [field] => flaer_phase\n                            [fields] => Array\n                                (\n                                    [0] => flaer_phase\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [59] => Array\n                        (\n                            [id] => col_59_0f81d52e06caaa4860887488d18271c7\n                            [label] => Updated\n                            [field] => updated\n                            [fields] => Array\n                                (\n                                    [0] => updated\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [60] => Array\n                        (\n                            [id] => col_60_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [61] => Array\n                        (\n                            [id] => col_61_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => sold_to_name\n                    [2] => sold_to_number\n                    [3] => vendor_name\n                    [4] => reseller_number\n                    [5] => reseller_vendor_id\n                    [6] => end_customer_vendor_id\n                    [7] => end_customer_name\n                    [8] => end_customer_address_1\n                    [9] => end_customer_address_2\n                    [10] => end_customer_address_3\n                    [11] => end_customer_city\n                    [12] => end_customer_state\n                    [13] => end_customer_zip_code\n                    [14] => end_customer_country\n                    [15] => end_customer_account_type\n                    [16] => end_customer_contact_name\n                    [17] => end_customer_contact_email\n                    [18] => end_customer_contact_phone\n                    [19] => end_customer_industry_segment\n                    [20] => agreement_program_name\n                    [21] => agreement_number\n                    [22] => agreement_start_date\n                    [23] => agreement_end_date\n                    [24] => agreement_terms\n                    [25] => agreement_type\n                    [26] => agreement_status\n                    [27] => agreement_support_level\n                    [28] => agreement_days_due\n                    [29] => agreement_autorenew\n                    [30] => product_name\n                    [31] => product_family\n                    [32] => product_market_segment\n                    [33] => product_release\n                    [34] => product_type\n                    [35] => product_deployment\n                    [36] => product_sku\n                    [37] => product_sku_description\n                    [38] => product_part\n                    [39] => product_list_price\n                    [40] => product_list_price_currency\n                    [41] => subscription_id\n                    [42] => subscription_serial_number\n                    [43] => subscription_status\n                    [44] => subscription_quantity\n                    [45] => subscription_start_date\n                    [46] => subscription_end_date\n                    [47] => subscription_contact_name\n                    [48] => subscription_contact_email\n                    [49] => subscription_level\n                    [50] => subscription_days_due\n                    [51] => quotation_id\n                    [52] => quotation_type\n                    [53] => quotation_vendor_id\n                    [54] => quotation_deal_registration_number\n                    [55] => quotation_status\n                    [56] => quotation_resellerpo_previous\n                    [57] => quotation_due_date\n                    [58] => flaer_phase\n                    [59] => updated\n                    [60] => created_at\n                    [61] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_sketchup_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [sold_to_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [sold_to_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [vendor_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [reseller_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [reseller_vendor_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_vendor_id] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_1] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_2] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_3] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_state] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_zip_code] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_account_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_email] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_phone] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_industry_segment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_program_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [agreement_start_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_terms] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_support_level] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_days_due] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [agreement_autorenew] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_family] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_market_segment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_release] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_deployment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_sku] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_sku_description] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_part] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_list_price] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [product_list_price_currency] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_quantity] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [subscription_start_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_contact_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_contact_email] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_level] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_days_due] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [quotation_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_vendor_id] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [quotation_deal_registration_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_resellerpo_previous] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_due_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [flaer_phase] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [updated] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_sketchup_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [sold_to_name] => sold_to_name\n                                            [sold_to_number] => sold_to_number\n                                            [vendor_name] => vendor_name\n                                            [reseller_number] => reseller_number\n                                            [reseller_vendor_id] => reseller_vendor_id\n                                            [end_customer_vendor_id] => end_customer_vendor_id\n                                            [end_customer_name] => end_customer_name\n                                            [end_customer_address_1] => end_customer_address_1\n                                            [end_customer_address_2] => end_customer_address_2\n                                            [end_customer_address_3] => end_customer_address_3\n                                            [end_customer_city] => end_customer_city\n                                            [end_customer_state] => end_customer_state\n                                            [end_customer_zip_code] => end_customer_zip_code\n                                            [end_customer_country] => end_customer_country\n                                            [end_customer_account_type] => end_customer_account_type\n                                            [end_customer_contact_name] => end_customer_contact_name\n                                            [end_customer_contact_email] => end_customer_contact_email\n                                            [end_customer_contact_phone] => end_customer_contact_phone\n                                            [end_customer_industry_segment] => end_customer_industry_segment\n                                            [agreement_program_name] => agreement_program_name\n                                            [agreement_number] => agreement_number\n                                            [agreement_start_date] => agreement_start_date\n                                            [agreement_end_date] => agreement_end_date\n                                            [agreement_terms] => agreement_terms\n                                            [agreement_type] => agreement_type\n                                            [agreement_status] => agreement_status\n                                            [agreement_support_level] => agreement_support_level\n                                            [agreement_days_due] => agreement_days_due\n                                            [agreement_autorenew] => agreement_autorenew\n                                            [product_name] => product_name\n                                            [product_family] => product_family\n                                            [product_market_segment] => product_market_segment\n                                            [product_release] => product_release\n                                            [product_type] => product_type\n                                            [product_deployment] => product_deployment\n                                            [product_sku] => product_sku\n                                            [product_sku_description] => product_sku_description\n                                            [product_part] => product_part\n                                            [product_list_price] => product_list_price\n                                            [product_list_price_currency] => product_list_price_currency\n                                            [subscription_id] => subscription_id\n                                            [subscription_serial_number] => subscription_serial_number\n                                            [subscription_status] => subscription_status\n                                            [subscription_quantity] => subscription_quantity\n                                            [subscription_start_date] => subscription_start_date\n                                            [subscription_end_date] => subscription_end_date\n                                            [subscription_contact_name] => subscription_contact_name\n                                            [subscription_contact_email] => subscription_contact_email\n                                            [subscription_level] => subscription_level\n                                            [subscription_days_due] => subscription_days_due\n                                            [quotation_id] => quotation_id\n                                            [quotation_type] => quotation_type\n                                            [quotation_vendor_id] => quotation_vendor_id\n                                            [quotation_deal_registration_number] => quotation_deal_registration_number\n                                            [quotation_status] => quotation_status\n                                            [quotation_resellerpo_previous] => quotation_resellerpo_previous\n                                            [quotation_due_date] => quotation_due_date\n                                            [flaer_phase] => flaer_phase\n                                            [updated] => updated\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [sold_to_name] => string\n                                            [sold_to_number] => integer\n                                            [vendor_name] => string\n                                            [reseller_number] => integer\n                                            [reseller_vendor_id] => string\n                                            [end_customer_vendor_id] => integer\n                                            [end_customer_name] => string\n                                            [end_customer_address_1] => string\n                                            [end_customer_address_2] => string\n                                            [end_customer_address_3] => string\n                                            [end_customer_city] => string\n                                            [end_customer_state] => string\n                                            [end_customer_zip_code] => string\n                                            [end_customer_country] => string\n                                            [end_customer_account_type] => string\n                                            [end_customer_contact_name] => string\n                                            [end_customer_contact_email] => string\n                                            [end_customer_contact_phone] => string\n                                            [end_customer_industry_segment] => string\n                                            [agreement_program_name] => string\n                                            [agreement_number] => integer\n                                            [agreement_start_date] => string\n                                            [agreement_end_date] => string\n                                            [agreement_terms] => string\n                                            [agreement_type] => string\n                                            [agreement_status] => string\n                                            [agreement_support_level] => string\n                                            [agreement_days_due] => integer\n                                            [agreement_autorenew] => integer\n                                            [product_name] => string\n                                            [product_family] => string\n                                            [product_market_segment] => string\n                                            [product_release] => string\n                                            [product_type] => string\n                                            [product_deployment] => string\n                                            [product_sku] => string\n                                            [product_sku_description] => string\n                                            [product_part] => string\n                                            [product_list_price] => integer\n                                            [product_list_price_currency] => string\n                                            [subscription_id] => string\n                                            [subscription_serial_number] => string\n                                            [subscription_status] => string\n                                            [subscription_quantity] => integer\n                                            [subscription_start_date] => string\n                                            [subscription_end_date] => string\n                                            [subscription_contact_name] => string\n                                            [subscription_contact_email] => string\n                                            [subscription_level] => string\n                                            [subscription_days_due] => integer\n                                            [quotation_id] => string\n                                            [quotation_type] => string\n                                            [quotation_vendor_id] => integer\n                                            [quotation_deal_registration_number] => string\n                                            [quotation_status] => string\n                                            [quotation_resellerpo_previous] => string\n                                            [quotation_due_date] => string\n                                            [flaer_phase] => string\n                                            [updated] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (79 rows, 79 analyzed)\n            [created_at] => 2025-08-11 13:20:38\n            [updated_at] => 2025-08-11 13:20:38\n        )\n\n    [table_name] => autobooks_sketchup_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-11 13:28:45] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:28:45] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:29:00] [data-table.edge.php:146] Data table template: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:29:00] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:29:01] [data-table.edge.php:146] Data table template: table=autobooks_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:29:01] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:29:03] [data-table.edge.php:146] Data table template: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:29:03] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:29:54] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_855a49c592df215a4abe977e4b4ebe1e\n                            [label] => Sold To Name\n                            [field] => sold_to_name\n                            [fields] => Array\n                                (\n                                    [0] => sold_to_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_1a78581b905a2b8b3819cda087a681df\n                            [label] => Sold To Number\n                            [field] => sold_to_number\n                            [fields] => Array\n                                (\n                                    [0] => sold_to_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_0ec1da7f1dc2f8abef07b11a3193ef57\n                            [label] => Vendor Name\n                            [field] => vendor_name\n                            [fields] => Array\n                                (\n                                    [0] => vendor_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_2824711b2f4f6060078d068bf40f1e05\n                            [label] => Reseller Number\n                            [field] => reseller_number\n                            [fields] => Array\n                                (\n                                    [0] => reseller_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_46140d0548ed0b78e29e04793ef2af1b\n                            [label] => Reseller Vendor\n                            [field] => reseller_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => reseller_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_c706df21763a2714816b0a44cfe646c7\n                            [label] => End Customer Vendor\n                            [field] => end_customer_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_033954cce10564766238dfac726c2258\n                            [label] => End Customer Name\n                            [field] => end_customer_name\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_6e034352d34c13782d8d0bc5d65011bd\n                            [label] => End Customer Address 1\n                            [field] => end_customer_address_1\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_1\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_ea92edd1525cbbe624e2a16347e81e08\n                            [label] => End Customer Address 2\n                            [field] => end_customer_address_2\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_2\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_fd89e44f39c29b2e6e5ee8afcf84fe01\n                            [label] => End Customer Address 3\n                            [field] => end_customer_address_3\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_3\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_58125853b8960d245a3b5005f35a258c\n                            [label] => End Customer City\n                            [field] => end_customer_city\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_1f0774740183acc085318ae5debb1a62\n                            [label] => End Customer State\n                            [field] => end_customer_state\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_state\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_3efae453b93cd454ce02cf110d95f12d\n                            [label] => End Customer Zip Code\n                            [field] => end_customer_zip_code\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_zip_code\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_20d02fa8403a932b1721c8d870ae0495\n                            [label] => End Customer Country\n                            [field] => end_customer_country\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [15] => Array\n                        (\n                            [id] => col_15_8915939119a19af6a75ea0edf6663bba\n                            [label] => End Customer Account Type\n                            [field] => end_customer_account_type\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_account_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [16] => Array\n                        (\n                            [id] => col_16_5fdcafa9911363ca87918c99782fea0c\n                            [label] => End Customer Contact Name\n                            [field] => end_customer_contact_name\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [17] => Array\n                        (\n                            [id] => col_17_6d1e35008cf647b5c049c591cd4d3b7e\n                            [label] => End Customer Contact Email\n                            [field] => end_customer_contact_email\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_email\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [18] => Array\n                        (\n                            [id] => col_18_948bf27ad83a985d151cfdedc2fd4adf\n                            [label] => End Customer Contact Phone\n                            [field] => end_customer_contact_phone\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_phone\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [19] => Array\n                        (\n                            [id] => col_19_c4aaf1d01f58e409021771db8b5bcc31\n                            [label] => End Customer Industry Segment\n                            [field] => end_customer_industry_segment\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_industry_segment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [20] => Array\n                        (\n                            [id] => col_20_54215071b2e666430d1f2ef806d223e6\n                            [label] => Agreement Program Name\n                            [field] => agreement_program_name\n                            [fields] => Array\n                                (\n                                    [0] => agreement_program_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [21] => Array\n                        (\n                            [id] => col_21_881bfc8855aa6d118a647f50872f9c37\n                            [label] => Agreement Number\n                            [field] => agreement_number\n                            [fields] => Array\n                                (\n                                    [0] => agreement_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [22] => Array\n                        (\n                            [id] => col_22_f67fe48b64d22349cd8a3679e72358ad\n                            [label] => Agreement Start Date\n                            [field] => agreement_start_date\n                            [fields] => Array\n                                (\n                                    [0] => agreement_start_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [23] => Array\n                        (\n                            [id] => col_23_ee6ffb2ed1207134c2506d8e95f6f68a\n                            [label] => Agreement End Date\n                            [field] => agreement_end_date\n                            [fields] => Array\n                                (\n                                    [0] => agreement_end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [24] => Array\n                        (\n                            [id] => col_24_2d017dd473b0b2349dceb1d7f33e3e44\n                            [label] => Agreement Terms\n                            [field] => agreement_terms\n                            [fields] => Array\n                                (\n                                    [0] => agreement_terms\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [25] => Array\n                        (\n                            [id] => col_25_8af78a35c6e73e79bf51fc8563b871fe\n                            [label] => Agreement Type\n                            [field] => agreement_type\n                            [fields] => Array\n                                (\n                                    [0] => agreement_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [26] => Array\n                        (\n                            [id] => col_26_adb05f2097fd47dcf014a7b073b34987\n                            [label] => Agreement Status\n                            [field] => agreement_status\n                            [fields] => Array\n                                (\n                                    [0] => agreement_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [27] => Array\n                        (\n                            [id] => col_27_b4d565e162b8e7bc13cb12729aae502a\n                            [label] => Agreement Support Level\n                            [field] => agreement_support_level\n                            [fields] => Array\n                                (\n                                    [0] => agreement_support_level\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [28] => Array\n                        (\n                            [id] => col_28_52c6a8653a43c2ab289ef6e42d0dcc08\n                            [label] => Agreement Days Due\n                            [field] => agreement_days_due\n                            [fields] => Array\n                                (\n                                    [0] => agreement_days_due\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [29] => Array\n                        (\n                            [id] => col_29_a6a4ccca71939f4261a20b0006726122\n                            [label] => Agreement Autorenew\n                            [field] => agreement_autorenew\n                            [fields] => Array\n                                (\n                                    [0] => agreement_autorenew\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [30] => Array\n                        (\n                            [id] => col_30_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [31] => Array\n                        (\n                            [id] => col_31_31981304965b1a7ee49c64d093e02e94\n                            [label] => Product Family\n                            [field] => product_family\n                            [fields] => Array\n                                (\n                                    [0] => product_family\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [32] => Array\n                        (\n                            [id] => col_32_7d11591eed252e3b1e06c3c8b170dd38\n                            [label] => Product Market Segment\n                            [field] => product_market_segment\n                            [fields] => Array\n                                (\n                                    [0] => product_market_segment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [33] => Array\n                        (\n                            [id] => col_33_b647350956b1adeea9069ef1306de753\n                            [label] => Product Release\n                            [field] => product_release\n                            [fields] => Array\n                                (\n                                    [0] => product_release\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [34] => Array\n                        (\n                            [id] => col_34_b1d9df66b969ebda1ccfb1be323f165b\n                            [label] => Product Type\n                            [field] => product_type\n                            [fields] => Array\n                                (\n                                    [0] => product_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [35] => Array\n                        (\n                            [id] => col_35_a4ec132ba158d45a164578a02f6ef646\n                            [label] => Product Deployment\n                            [field] => product_deployment\n                            [fields] => Array\n                                (\n                                    [0] => product_deployment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [36] => Array\n                        (\n                            [id] => col_36_197d90cc45a4687eb1ef153ac86bc05f\n                            [label] => Product Sku\n                            [field] => product_sku\n                            [fields] => Array\n                                (\n                                    [0] => product_sku\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [37] => Array\n                        (\n                            [id] => col_37_b487a1edb0f07fa54d48393bbfea8139\n                            [label] => Product Sku Description\n                            [field] => product_sku_description\n                            [fields] => Array\n                                (\n                                    [0] => product_sku_description\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [38] => Array\n                        (\n                            [id] => col_38_fdff8146fc34b8e8e8ee5abe920d4806\n                            [label] => Product Part\n                            [field] => product_part\n                            [fields] => Array\n                                (\n                                    [0] => product_part\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [39] => Array\n                        (\n                            [id] => col_39_ceefc2cd7f84cf31ee379912ce351ad3\n                            [label] => Product List Price\n                            [field] => product_list_price\n                            [fields] => Array\n                                (\n                                    [0] => product_list_price\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [40] => Array\n                        (\n                            [id] => col_40_a851a28a6e219e3ec67e534c626aeaf3\n                            [label] => Product List Price Currency\n                            [field] => product_list_price_currency\n                            [fields] => Array\n                                (\n                                    [0] => product_list_price_currency\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [41] => Array\n                        (\n                            [id] => col_41_ef42673f634b68005996f2c64d6a72e3\n                            [label] => Subscription\n                            [field] => subscription_id\n                            [fields] => Array\n                                (\n                                    [0] => subscription_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [42] => Array\n                        (\n                            [id] => col_42_8c20c72b0daef191013b97458e02bb25\n                            [label] => Subscription Serial Number\n                            [field] => subscription_serial_number\n                            [fields] => Array\n                                (\n                                    [0] => subscription_serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [43] => Array\n                        (\n                            [id] => col_43_2ec2483de1ecdf1c9ccb681489ffa517\n                            [label] => Subscription Status\n                            [field] => subscription_status\n                            [fields] => Array\n                                (\n                                    [0] => subscription_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [44] => Array\n                        (\n                            [id] => col_44_5401f206636abe557cb9b60eca0ca28f\n                            [label] => Subscription Quantity\n                            [field] => subscription_quantity\n                            [fields] => Array\n                                (\n                                    [0] => subscription_quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [45] => Array\n                        (\n                            [id] => col_45_9c01b07be7b67284c181d0855827c151\n                            [label] => Subscription Start Date\n                            [field] => subscription_start_date\n                            [fields] => Array\n                                (\n                                    [0] => subscription_start_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [46] => Array\n                        (\n                            [id] => col_46_ab5851cb73c308ad66653249acbaefe3\n                            [label] => Subscription End Date\n                            [field] => subscription_end_date\n                            [fields] => Array\n                                (\n                                    [0] => subscription_end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [47] => Array\n                        (\n                            [id] => col_47_e9775f536b7ca1a1fcc63bbce1a54d62\n                            [label] => Subscription Contact Name\n                            [field] => subscription_contact_name\n                            [fields] => Array\n                                (\n                                    [0] => subscription_contact_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [48] => Array\n                        (\n                            [id] => col_48_e1e0a62a0901372172e3a54b62492a89\n                            [label] => Subscription Contact Email\n                            [field] => subscription_contact_email\n                            [fields] => Array\n                                (\n                                    [0] => subscription_contact_email\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [49] => Array\n                        (\n                            [id] => col_49_aa1d9b48f2e7b9108127d34afdb14da8\n                            [label] => Subscription Level\n                            [field] => subscription_level\n                            [fields] => Array\n                                (\n                                    [0] => subscription_level\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [50] => Array\n                        (\n                            [id] => col_50_adafdf8e0515f1788d271d09e44dd3f2\n                            [label] => Subscription Days Due\n                            [field] => subscription_days_due\n                            [fields] => Array\n                                (\n                                    [0] => subscription_days_due\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [51] => Array\n                        (\n                            [id] => col_51_2b6213d56e0299b7b5a352f7337328d8\n                            [label] => Quotation\n                            [field] => quotation_id\n                            [fields] => Array\n                                (\n                                    [0] => quotation_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [52] => Array\n                        (\n                            [id] => col_52_737c3c888ee0cc2600a2dc0d7f55ed05\n                            [label] => Quotation Type\n                            [field] => quotation_type\n                            [fields] => Array\n                                (\n                                    [0] => quotation_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [53] => Array\n                        (\n                            [id] => col_53_eb42bc3afe7c7f333989b1a3ab57bac0\n                            [label] => Quotation Vendor\n                            [field] => quotation_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => quotation_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [54] => Array\n                        (\n                            [id] => col_54_f83eab3e651df57f2cfbdaedec949a82\n                            [label] => Quotation Deal Registration Number\n                            [field] => quotation_deal_registration_number\n                            [fields] => Array\n                                (\n                                    [0] => quotation_deal_registration_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [55] => Array\n                        (\n                            [id] => col_55_a52a77131c467520565de2fb0c065189\n                            [label] => Quotation Status\n                            [field] => quotation_status\n                            [fields] => Array\n                                (\n                                    [0] => quotation_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [56] => Array\n                        (\n                            [id] => col_56_df1b0dfe471a20da3523bf2c525c3f39\n                            [label] => Quotation Resellerpo Previous\n                            [field] => quotation_resellerpo_previous\n                            [fields] => Array\n                                (\n                                    [0] => quotation_resellerpo_previous\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [57] => Array\n                        (\n                            [id] => col_57_5add4b7dce2f0f862189499e578133ea\n                            [label] => Quotation Due Date\n                            [field] => quotation_due_date\n                            [fields] => Array\n                                (\n                                    [0] => quotation_due_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [58] => Array\n                        (\n                            [id] => col_58_1180a372ed0e4f6d0f6d1babd14e28fb\n                            [label] => Flaer Phase\n                            [field] => flaer_phase\n                            [fields] => Array\n                                (\n                                    [0] => flaer_phase\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [59] => Array\n                        (\n                            [id] => col_59_0f81d52e06caaa4860887488d18271c7\n                            [label] => Updated\n                            [field] => updated\n                            [fields] => Array\n                                (\n                                    [0] => updated\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [60] => Array\n                        (\n                            [id] => col_60_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [61] => Array\n                        (\n                            [id] => col_61_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => sold_to_name\n                    [2] => sold_to_number\n                    [3] => vendor_name\n                    [4] => reseller_number\n                    [5] => reseller_vendor_id\n                    [6] => end_customer_vendor_id\n                    [7] => end_customer_name\n                    [8] => end_customer_address_1\n                    [9] => end_customer_address_2\n                    [10] => end_customer_address_3\n                    [11] => end_customer_city\n                    [12] => end_customer_state\n                    [13] => end_customer_zip_code\n                    [14] => end_customer_country\n                    [15] => end_customer_account_type\n                    [16] => end_customer_contact_name\n                    [17] => end_customer_contact_email\n                    [18] => end_customer_contact_phone\n                    [19] => end_customer_industry_segment\n                    [20] => agreement_program_name\n                    [21] => agreement_number\n                    [22] => agreement_start_date\n                    [23] => agreement_end_date\n                    [24] => agreement_terms\n                    [25] => agreement_type\n                    [26] => agreement_status\n                    [27] => agreement_support_level\n                    [28] => agreement_days_due\n                    [29] => agreement_autorenew\n                    [30] => product_name\n                    [31] => product_family\n                    [32] => product_market_segment\n                    [33] => product_release\n                    [34] => product_type\n                    [35] => product_deployment\n                    [36] => product_sku\n                    [37] => product_sku_description\n                    [38] => product_part\n                    [39] => product_list_price\n                    [40] => product_list_price_currency\n                    [41] => subscription_id\n                    [42] => subscription_serial_number\n                    [43] => subscription_status\n                    [44] => subscription_quantity\n                    [45] => subscription_start_date\n                    [46] => subscription_end_date\n                    [47] => subscription_contact_name\n                    [48] => subscription_contact_email\n                    [49] => subscription_level\n                    [50] => subscription_days_due\n                    [51] => quotation_id\n                    [52] => quotation_type\n                    [53] => quotation_vendor_id\n                    [54] => quotation_deal_registration_number\n                    [55] => quotation_status\n                    [56] => quotation_resellerpo_previous\n                    [57] => quotation_due_date\n                    [58] => flaer_phase\n                    [59] => updated\n                    [60] => created_at\n                    [61] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_Sketchup_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [sold_to_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [sold_to_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [vendor_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [reseller_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [reseller_vendor_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_vendor_id] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_1] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_2] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_3] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_state] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_zip_code] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_account_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_email] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_phone] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_industry_segment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_program_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [agreement_start_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_terms] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_support_level] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_days_due] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [agreement_autorenew] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_family] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_market_segment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_release] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_deployment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_sku] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_sku_description] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_part] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_list_price] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [product_list_price_currency] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_quantity] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [subscription_start_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_contact_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_contact_email] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_level] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_days_due] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [quotation_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_vendor_id] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [quotation_deal_registration_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_resellerpo_previous] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_due_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [flaer_phase] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [updated] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_Sketchup_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [sold_to_name] => sold_to_name\n                                            [sold_to_number] => sold_to_number\n                                            [vendor_name] => vendor_name\n                                            [reseller_number] => reseller_number\n                                            [reseller_vendor_id] => reseller_vendor_id\n                                            [end_customer_vendor_id] => end_customer_vendor_id\n                                            [end_customer_name] => end_customer_name\n                                            [end_customer_address_1] => end_customer_address_1\n                                            [end_customer_address_2] => end_customer_address_2\n                                            [end_customer_address_3] => end_customer_address_3\n                                            [end_customer_city] => end_customer_city\n                                            [end_customer_state] => end_customer_state\n                                            [end_customer_zip_code] => end_customer_zip_code\n                                            [end_customer_country] => end_customer_country\n                                            [end_customer_account_type] => end_customer_account_type\n                                            [end_customer_contact_name] => end_customer_contact_name\n                                            [end_customer_contact_email] => end_customer_contact_email\n                                            [end_customer_contact_phone] => end_customer_contact_phone\n                                            [end_customer_industry_segment] => end_customer_industry_segment\n                                            [agreement_program_name] => agreement_program_name\n                                            [agreement_number] => agreement_number\n                                            [agreement_start_date] => agreement_start_date\n                                            [agreement_end_date] => agreement_end_date\n                                            [agreement_terms] => agreement_terms\n                                            [agreement_type] => agreement_type\n                                            [agreement_status] => agreement_status\n                                            [agreement_support_level] => agreement_support_level\n                                            [agreement_days_due] => agreement_days_due\n                                            [agreement_autorenew] => agreement_autorenew\n                                            [product_name] => product_name\n                                            [product_family] => product_family\n                                            [product_market_segment] => product_market_segment\n                                            [product_release] => product_release\n                                            [product_type] => product_type\n                                            [product_deployment] => product_deployment\n                                            [product_sku] => product_sku\n                                            [product_sku_description] => product_sku_description\n                                            [product_part] => product_part\n                                            [product_list_price] => product_list_price\n                                            [product_list_price_currency] => product_list_price_currency\n                                            [subscription_id] => subscription_id\n                                            [subscription_serial_number] => subscription_serial_number\n                                            [subscription_status] => subscription_status\n                                            [subscription_quantity] => subscription_quantity\n                                            [subscription_start_date] => subscription_start_date\n                                            [subscription_end_date] => subscription_end_date\n                                            [subscription_contact_name] => subscription_contact_name\n                                            [subscription_contact_email] => subscription_contact_email\n                                            [subscription_level] => subscription_level\n                                            [subscription_days_due] => subscription_days_due\n                                            [quotation_id] => quotation_id\n                                            [quotation_type] => quotation_type\n                                            [quotation_vendor_id] => quotation_vendor_id\n                                            [quotation_deal_registration_number] => quotation_deal_registration_number\n                                            [quotation_status] => quotation_status\n                                            [quotation_resellerpo_previous] => quotation_resellerpo_previous\n                                            [quotation_due_date] => quotation_due_date\n                                            [flaer_phase] => flaer_phase\n                                            [updated] => updated\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [sold_to_name] => string\n                                            [sold_to_number] => integer\n                                            [vendor_name] => string\n                                            [reseller_number] => integer\n                                            [reseller_vendor_id] => string\n                                            [end_customer_vendor_id] => integer\n                                            [end_customer_name] => string\n                                            [end_customer_address_1] => string\n                                            [end_customer_address_2] => string\n                                            [end_customer_address_3] => string\n                                            [end_customer_city] => string\n                                            [end_customer_state] => string\n                                            [end_customer_zip_code] => string\n                                            [end_customer_country] => string\n                                            [end_customer_account_type] => string\n                                            [end_customer_contact_name] => string\n                                            [end_customer_contact_email] => string\n                                            [end_customer_contact_phone] => string\n                                            [end_customer_industry_segment] => string\n                                            [agreement_program_name] => string\n                                            [agreement_number] => integer\n                                            [agreement_start_date] => string\n                                            [agreement_end_date] => string\n                                            [agreement_terms] => string\n                                            [agreement_type] => string\n                                            [agreement_status] => string\n                                            [agreement_support_level] => string\n                                            [agreement_days_due] => integer\n                                            [agreement_autorenew] => integer\n                                            [product_name] => string\n                                            [product_family] => string\n                                            [product_market_segment] => string\n                                            [product_release] => string\n                                            [product_type] => string\n                                            [product_deployment] => string\n                                            [product_sku] => string\n                                            [product_sku_description] => string\n                                            [product_part] => string\n                                            [product_list_price] => integer\n                                            [product_list_price_currency] => string\n                                            [subscription_id] => string\n                                            [subscription_serial_number] => string\n                                            [subscription_status] => string\n                                            [subscription_quantity] => integer\n                                            [subscription_start_date] => string\n                                            [subscription_end_date] => string\n                                            [subscription_contact_name] => string\n                                            [subscription_contact_email] => string\n                                            [subscription_level] => string\n                                            [subscription_days_due] => integer\n                                            [quotation_id] => string\n                                            [quotation_type] => string\n                                            [quotation_vendor_id] => integer\n                                            [quotation_deal_registration_number] => string\n                                            [quotation_status] => string\n                                            [quotation_resellerpo_previous] => string\n                                            [quotation_due_date] => string\n                                            [flaer_phase] => string\n                                            [updated] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (79 rows, 79 analyzed)\n            [created_at] => 2025-08-11 13:29:54\n            [updated_at] => 2025-08-11 13:29:54\n        )\n\n    [table_name] => autobooks_Sketchup_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-11 13:35:58] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_855a49c592df215a4abe977e4b4ebe1e\n                            [label] => Sold To Name\n                            [field] => sold_to_name\n                            [fields] => Array\n                                (\n                                    [0] => sold_to_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_1a78581b905a2b8b3819cda087a681df\n                            [label] => Sold To Number\n                            [field] => sold_to_number\n                            [fields] => Array\n                                (\n                                    [0] => sold_to_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_0ec1da7f1dc2f8abef07b11a3193ef57\n                            [label] => Vendor Name\n                            [field] => vendor_name\n                            [fields] => Array\n                                (\n                                    [0] => vendor_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_2824711b2f4f6060078d068bf40f1e05\n                            [label] => Reseller Number\n                            [field] => reseller_number\n                            [fields] => Array\n                                (\n                                    [0] => reseller_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_46140d0548ed0b78e29e04793ef2af1b\n                            [label] => Reseller Vendor\n                            [field] => reseller_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => reseller_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_c706df21763a2714816b0a44cfe646c7\n                            [label] => End Customer Vendor\n                            [field] => end_customer_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_033954cce10564766238dfac726c2258\n                            [label] => End Customer Name\n                            [field] => end_customer_name\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_6e034352d34c13782d8d0bc5d65011bd\n                            [label] => End Customer Address 1\n                            [field] => end_customer_address_1\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_1\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_ea92edd1525cbbe624e2a16347e81e08\n                            [label] => End Customer Address 2\n                            [field] => end_customer_address_2\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_2\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_fd89e44f39c29b2e6e5ee8afcf84fe01\n                            [label] => End Customer Address 3\n                            [field] => end_customer_address_3\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_3\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_58125853b8960d245a3b5005f35a258c\n                            [label] => End Customer City\n                            [field] => end_customer_city\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_1f0774740183acc085318ae5debb1a62\n                            [label] => End Customer State\n                            [field] => end_customer_state\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_state\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_3efae453b93cd454ce02cf110d95f12d\n                            [label] => End Customer Zip Code\n                            [field] => end_customer_zip_code\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_zip_code\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_20d02fa8403a932b1721c8d870ae0495\n                            [label] => End Customer Country\n                            [field] => end_customer_country\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [15] => Array\n                        (\n                            [id] => col_15_8915939119a19af6a75ea0edf6663bba\n                            [label] => End Customer Account Type\n                            [field] => end_customer_account_type\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_account_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [16] => Array\n                        (\n                            [id] => col_16_5fdcafa9911363ca87918c99782fea0c\n                            [label] => End Customer Contact Name\n                            [field] => end_customer_contact_name\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [17] => Array\n                        (\n                            [id] => col_17_6d1e35008cf647b5c049c591cd4d3b7e\n                            [label] => End Customer Contact Email\n                            [field] => end_customer_contact_email\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_email\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [18] => Array\n                        (\n                            [id] => col_18_948bf27ad83a985d151cfdedc2fd4adf\n                            [label] => End Customer Contact Phone\n                            [field] => end_customer_contact_phone\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_phone\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [19] => Array\n                        (\n                            [id] => col_19_c4aaf1d01f58e409021771db8b5bcc31\n                            [label] => End Customer Industry Segment\n                            [field] => end_customer_industry_segment\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_industry_segment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [20] => Array\n                        (\n                            [id] => col_20_54215071b2e666430d1f2ef806d223e6\n                            [label] => Agreement Program Name\n                            [field] => agreement_program_name\n                            [fields] => Array\n                                (\n                                    [0] => agreement_program_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [21] => Array\n                        (\n                            [id] => col_21_881bfc8855aa6d118a647f50872f9c37\n                            [label] => Agreement Number\n                            [field] => agreement_number\n                            [fields] => Array\n                                (\n                                    [0] => agreement_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [22] => Array\n                        (\n                            [id] => col_22_f67fe48b64d22349cd8a3679e72358ad\n                            [label] => Agreement Start Date\n                            [field] => agreement_start_date\n                            [fields] => Array\n                                (\n                                    [0] => agreement_start_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [23] => Array\n                        (\n                            [id] => col_23_ee6ffb2ed1207134c2506d8e95f6f68a\n                            [label] => Agreement End Date\n                            [field] => agreement_end_date\n                            [fields] => Array\n                                (\n                                    [0] => agreement_end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [24] => Array\n                        (\n                            [id] => col_24_2d017dd473b0b2349dceb1d7f33e3e44\n                            [label] => Agreement Terms\n                            [field] => agreement_terms\n                            [fields] => Array\n                                (\n                                    [0] => agreement_terms\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [25] => Array\n                        (\n                            [id] => col_25_8af78a35c6e73e79bf51fc8563b871fe\n                            [label] => Agreement Type\n                            [field] => agreement_type\n                            [fields] => Array\n                                (\n                                    [0] => agreement_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [26] => Array\n                        (\n                            [id] => col_26_adb05f2097fd47dcf014a7b073b34987\n                            [label] => Agreement Status\n                            [field] => agreement_status\n                            [fields] => Array\n                                (\n                                    [0] => agreement_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [27] => Array\n                        (\n                            [id] => col_27_b4d565e162b8e7bc13cb12729aae502a\n                            [label] => Agreement Support Level\n                            [field] => agreement_support_level\n                            [fields] => Array\n                                (\n                                    [0] => agreement_support_level\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [28] => Array\n                        (\n                            [id] => col_28_52c6a8653a43c2ab289ef6e42d0dcc08\n                            [label] => Agreement Days Due\n                            [field] => agreement_days_due\n                            [fields] => Array\n                                (\n                                    [0] => agreement_days_due\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [29] => Array\n                        (\n                            [id] => col_29_a6a4ccca71939f4261a20b0006726122\n                            [label] => Agreement Autorenew\n                            [field] => agreement_autorenew\n                            [fields] => Array\n                                (\n                                    [0] => agreement_autorenew\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [30] => Array\n                        (\n                            [id] => col_30_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [31] => Array\n                        (\n                            [id] => col_31_31981304965b1a7ee49c64d093e02e94\n                            [label] => Product Family\n                            [field] => product_family\n                            [fields] => Array\n                                (\n                                    [0] => product_family\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [32] => Array\n                        (\n                            [id] => col_32_7d11591eed252e3b1e06c3c8b170dd38\n                            [label] => Product Market Segment\n                            [field] => product_market_segment\n                            [fields] => Array\n                                (\n                                    [0] => product_market_segment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [33] => Array\n                        (\n                            [id] => col_33_b647350956b1adeea9069ef1306de753\n                            [label] => Product Release\n                            [field] => product_release\n                            [fields] => Array\n                                (\n                                    [0] => product_release\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [34] => Array\n                        (\n                            [id] => col_34_b1d9df66b969ebda1ccfb1be323f165b\n                            [label] => Product Type\n                            [field] => product_type\n                            [fields] => Array\n                                (\n                                    [0] => product_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [35] => Array\n                        (\n                            [id] => col_35_a4ec132ba158d45a164578a02f6ef646\n                            [label] => Product Deployment\n                            [field] => product_deployment\n                            [fields] => Array\n                                (\n                                    [0] => product_deployment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [36] => Array\n                        (\n                            [id] => col_36_197d90cc45a4687eb1ef153ac86bc05f\n                            [label] => Product Sku\n                            [field] => product_sku\n                            [fields] => Array\n                                (\n                                    [0] => product_sku\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [37] => Array\n                        (\n                            [id] => col_37_b487a1edb0f07fa54d48393bbfea8139\n                            [label] => Product Sku Description\n                            [field] => product_sku_description\n                            [fields] => Array\n                                (\n                                    [0] => product_sku_description\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [38] => Array\n                        (\n                            [id] => col_38_fdff8146fc34b8e8e8ee5abe920d4806\n                            [label] => Product Part\n                            [field] => product_part\n                            [fields] => Array\n                                (\n                                    [0] => product_part\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [39] => Array\n                        (\n                            [id] => col_39_ceefc2cd7f84cf31ee379912ce351ad3\n                            [label] => Product List Price\n                            [field] => product_list_price\n                            [fields] => Array\n                                (\n                                    [0] => product_list_price\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [40] => Array\n                        (\n                            [id] => col_40_a851a28a6e219e3ec67e534c626aeaf3\n                            [label] => Product List Price Currency\n                            [field] => product_list_price_currency\n                            [fields] => Array\n                                (\n                                    [0] => product_list_price_currency\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [41] => Array\n                        (\n                            [id] => col_41_ef42673f634b68005996f2c64d6a72e3\n                            [label] => Subscription\n                            [field] => subscription_id\n                            [fields] => Array\n                                (\n                                    [0] => subscription_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [42] => Array\n                        (\n                            [id] => col_42_8c20c72b0daef191013b97458e02bb25\n                            [label] => Subscription Serial Number\n                            [field] => subscription_serial_number\n                            [fields] => Array\n                                (\n                                    [0] => subscription_serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [43] => Array\n                        (\n                            [id] => col_43_2ec2483de1ecdf1c9ccb681489ffa517\n                            [label] => Subscription Status\n                            [field] => subscription_status\n                            [fields] => Array\n                                (\n                                    [0] => subscription_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [44] => Array\n                        (\n                            [id] => col_44_5401f206636abe557cb9b60eca0ca28f\n                            [label] => Subscription Quantity\n                            [field] => subscription_quantity\n                            [fields] => Array\n                                (\n                                    [0] => subscription_quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [45] => Array\n                        (\n                            [id] => col_45_9c01b07be7b67284c181d0855827c151\n                            [label] => Subscription Start Date\n                            [field] => subscription_start_date\n                            [fields] => Array\n                                (\n                                    [0] => subscription_start_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [46] => Array\n                        (\n                            [id] => col_46_ab5851cb73c308ad66653249acbaefe3\n                            [label] => Subscription End Date\n                            [field] => subscription_end_date\n                            [fields] => Array\n                                (\n                                    [0] => subscription_end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [47] => Array\n                        (\n                            [id] => col_47_e9775f536b7ca1a1fcc63bbce1a54d62\n                            [label] => Subscription Contact Name\n                            [field] => subscription_contact_name\n                            [fields] => Array\n                                (\n                                    [0] => subscription_contact_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [48] => Array\n                        (\n                            [id] => col_48_e1e0a62a0901372172e3a54b62492a89\n                            [label] => Subscription Contact Email\n                            [field] => subscription_contact_email\n                            [fields] => Array\n                                (\n                                    [0] => subscription_contact_email\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [49] => Array\n                        (\n                            [id] => col_49_aa1d9b48f2e7b9108127d34afdb14da8\n                            [label] => Subscription Level\n                            [field] => subscription_level\n                            [fields] => Array\n                                (\n                                    [0] => subscription_level\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [50] => Array\n                        (\n                            [id] => col_50_adafdf8e0515f1788d271d09e44dd3f2\n                            [label] => Subscription Days Due\n                            [field] => subscription_days_due\n                            [fields] => Array\n                                (\n                                    [0] => subscription_days_due\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [51] => Array\n                        (\n                            [id] => col_51_2b6213d56e0299b7b5a352f7337328d8\n                            [label] => Quotation\n                            [field] => quotation_id\n                            [fields] => Array\n                                (\n                                    [0] => quotation_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [52] => Array\n                        (\n                            [id] => col_52_737c3c888ee0cc2600a2dc0d7f55ed05\n                            [label] => Quotation Type\n                            [field] => quotation_type\n                            [fields] => Array\n                                (\n                                    [0] => quotation_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [53] => Array\n                        (\n                            [id] => col_53_eb42bc3afe7c7f333989b1a3ab57bac0\n                            [label] => Quotation Vendor\n                            [field] => quotation_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => quotation_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [54] => Array\n                        (\n                            [id] => col_54_f83eab3e651df57f2cfbdaedec949a82\n                            [label] => Quotation Deal Registration Number\n                            [field] => quotation_deal_registration_number\n                            [fields] => Array\n                                (\n                                    [0] => quotation_deal_registration_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [55] => Array\n                        (\n                            [id] => col_55_a52a77131c467520565de2fb0c065189\n                            [label] => Quotation Status\n                            [field] => quotation_status\n                            [fields] => Array\n                                (\n                                    [0] => quotation_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [56] => Array\n                        (\n                            [id] => col_56_df1b0dfe471a20da3523bf2c525c3f39\n                            [label] => Quotation Resellerpo Previous\n                            [field] => quotation_resellerpo_previous\n                            [fields] => Array\n                                (\n                                    [0] => quotation_resellerpo_previous\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [57] => Array\n                        (\n                            [id] => col_57_5add4b7dce2f0f862189499e578133ea\n                            [label] => Quotation Due Date\n                            [field] => quotation_due_date\n                            [fields] => Array\n                                (\n                                    [0] => quotation_due_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [58] => Array\n                        (\n                            [id] => col_58_1180a372ed0e4f6d0f6d1babd14e28fb\n                            [label] => Flaer Phase\n                            [field] => flaer_phase\n                            [fields] => Array\n                                (\n                                    [0] => flaer_phase\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [59] => Array\n                        (\n                            [id] => col_59_0f81d52e06caaa4860887488d18271c7\n                            [label] => Updated\n                            [field] => updated\n                            [fields] => Array\n                                (\n                                    [0] => updated\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [60] => Array\n                        (\n                            [id] => col_60_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [61] => Array\n                        (\n                            [id] => col_61_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => sold_to_name\n                    [2] => sold_to_number\n                    [3] => vendor_name\n                    [4] => reseller_number\n                    [5] => reseller_vendor_id\n                    [6] => end_customer_vendor_id\n                    [7] => end_customer_name\n                    [8] => end_customer_address_1\n                    [9] => end_customer_address_2\n                    [10] => end_customer_address_3\n                    [11] => end_customer_city\n                    [12] => end_customer_state\n                    [13] => end_customer_zip_code\n                    [14] => end_customer_country\n                    [15] => end_customer_account_type\n                    [16] => end_customer_contact_name\n                    [17] => end_customer_contact_email\n                    [18] => end_customer_contact_phone\n                    [19] => end_customer_industry_segment\n                    [20] => agreement_program_name\n                    [21] => agreement_number\n                    [22] => agreement_start_date\n                    [23] => agreement_end_date\n                    [24] => agreement_terms\n                    [25] => agreement_type\n                    [26] => agreement_status\n                    [27] => agreement_support_level\n                    [28] => agreement_days_due\n                    [29] => agreement_autorenew\n                    [30] => product_name\n                    [31] => product_family\n                    [32] => product_market_segment\n                    [33] => product_release\n                    [34] => product_type\n                    [35] => product_deployment\n                    [36] => product_sku\n                    [37] => product_sku_description\n                    [38] => product_part\n                    [39] => product_list_price\n                    [40] => product_list_price_currency\n                    [41] => subscription_id\n                    [42] => subscription_serial_number\n                    [43] => subscription_status\n                    [44] => subscription_quantity\n                    [45] => subscription_start_date\n                    [46] => subscription_end_date\n                    [47] => subscription_contact_name\n                    [48] => subscription_contact_email\n                    [49] => subscription_level\n                    [50] => subscription_days_due\n                    [51] => quotation_id\n                    [52] => quotation_type\n                    [53] => quotation_vendor_id\n                    [54] => quotation_deal_registration_number\n                    [55] => quotation_status\n                    [56] => quotation_resellerpo_previous\n                    [57] => quotation_due_date\n                    [58] => flaer_phase\n                    [59] => updated\n                    [60] => created_at\n                    [61] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_sketchup_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [sold_to_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [sold_to_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [vendor_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [reseller_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [reseller_vendor_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_vendor_id] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_1] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_2] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_3] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_state] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_zip_code] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_account_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_email] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_phone] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_industry_segment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_program_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [agreement_start_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_terms] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_support_level] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_days_due] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [agreement_autorenew] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_family] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_market_segment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_release] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_deployment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_sku] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_sku_description] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_part] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_list_price] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [product_list_price_currency] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_quantity] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [subscription_start_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_contact_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_contact_email] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_level] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_days_due] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [quotation_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_vendor_id] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [quotation_deal_registration_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_resellerpo_previous] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_due_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [flaer_phase] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [updated] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_sketchup_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [sold_to_name] => sold_to_name\n                                            [sold_to_number] => sold_to_number\n                                            [vendor_name] => vendor_name\n                                            [reseller_number] => reseller_number\n                                            [reseller_vendor_id] => reseller_vendor_id\n                                            [end_customer_vendor_id] => end_customer_vendor_id\n                                            [end_customer_name] => end_customer_name\n                                            [end_customer_address_1] => end_customer_address_1\n                                            [end_customer_address_2] => end_customer_address_2\n                                            [end_customer_address_3] => end_customer_address_3\n                                            [end_customer_city] => end_customer_city\n                                            [end_customer_state] => end_customer_state\n                                            [end_customer_zip_code] => end_customer_zip_code\n                                            [end_customer_country] => end_customer_country\n                                            [end_customer_account_type] => end_customer_account_type\n                                            [end_customer_contact_name] => end_customer_contact_name\n                                            [end_customer_contact_email] => end_customer_contact_email\n                                            [end_customer_contact_phone] => end_customer_contact_phone\n                                            [end_customer_industry_segment] => end_customer_industry_segment\n                                            [agreement_program_name] => agreement_program_name\n                                            [agreement_number] => agreement_number\n                                            [agreement_start_date] => agreement_start_date\n                                            [agreement_end_date] => agreement_end_date\n                                            [agreement_terms] => agreement_terms\n                                            [agreement_type] => agreement_type\n                                            [agreement_status] => agreement_status\n                                            [agreement_support_level] => agreement_support_level\n                                            [agreement_days_due] => agreement_days_due\n                                            [agreement_autorenew] => agreement_autorenew\n                                            [product_name] => product_name\n                                            [product_family] => product_family\n                                            [product_market_segment] => product_market_segment\n                                            [product_release] => product_release\n                                            [product_type] => product_type\n                                            [product_deployment] => product_deployment\n                                            [product_sku] => product_sku\n                                            [product_sku_description] => product_sku_description\n                                            [product_part] => product_part\n                                            [product_list_price] => product_list_price\n                                            [product_list_price_currency] => product_list_price_currency\n                                            [subscription_id] => subscription_id\n                                            [subscription_serial_number] => subscription_serial_number\n                                            [subscription_status] => subscription_status\n                                            [subscription_quantity] => subscription_quantity\n                                            [subscription_start_date] => subscription_start_date\n                                            [subscription_end_date] => subscription_end_date\n                                            [subscription_contact_name] => subscription_contact_name\n                                            [subscription_contact_email] => subscription_contact_email\n                                            [subscription_level] => subscription_level\n                                            [subscription_days_due] => subscription_days_due\n                                            [quotation_id] => quotation_id\n                                            [quotation_type] => quotation_type\n                                            [quotation_vendor_id] => quotation_vendor_id\n                                            [quotation_deal_registration_number] => quotation_deal_registration_number\n                                            [quotation_status] => quotation_status\n                                            [quotation_resellerpo_previous] => quotation_resellerpo_previous\n                                            [quotation_due_date] => quotation_due_date\n                                            [flaer_phase] => flaer_phase\n                                            [updated] => updated\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [sold_to_name] => string\n                                            [sold_to_number] => integer\n                                            [vendor_name] => string\n                                            [reseller_number] => integer\n                                            [reseller_vendor_id] => string\n                                            [end_customer_vendor_id] => integer\n                                            [end_customer_name] => string\n                                            [end_customer_address_1] => string\n                                            [end_customer_address_2] => string\n                                            [end_customer_address_3] => string\n                                            [end_customer_city] => string\n                                            [end_customer_state] => string\n                                            [end_customer_zip_code] => string\n                                            [end_customer_country] => string\n                                            [end_customer_account_type] => string\n                                            [end_customer_contact_name] => string\n                                            [end_customer_contact_email] => string\n                                            [end_customer_contact_phone] => string\n                                            [end_customer_industry_segment] => string\n                                            [agreement_program_name] => string\n                                            [agreement_number] => integer\n                                            [agreement_start_date] => string\n                                            [agreement_end_date] => string\n                                            [agreement_terms] => string\n                                            [agreement_type] => string\n                                            [agreement_status] => string\n                                            [agreement_support_level] => string\n                                            [agreement_days_due] => integer\n                                            [agreement_autorenew] => integer\n                                            [product_name] => string\n                                            [product_family] => string\n                                            [product_market_segment] => string\n                                            [product_release] => string\n                                            [product_type] => string\n                                            [product_deployment] => string\n                                            [product_sku] => string\n                                            [product_sku_description] => string\n                                            [product_part] => string\n                                            [product_list_price] => integer\n                                            [product_list_price_currency] => string\n                                            [subscription_id] => string\n                                            [subscription_serial_number] => string\n                                            [subscription_status] => string\n                                            [subscription_quantity] => integer\n                                            [subscription_start_date] => string\n                                            [subscription_end_date] => string\n                                            [subscription_contact_name] => string\n                                            [subscription_contact_email] => string\n                                            [subscription_level] => string\n                                            [subscription_days_due] => integer\n                                            [quotation_id] => string\n                                            [quotation_type] => string\n                                            [quotation_vendor_id] => integer\n                                            [quotation_deal_registration_number] => string\n                                            [quotation_status] => string\n                                            [quotation_resellerpo_previous] => string\n                                            [quotation_due_date] => string\n                                            [flaer_phase] => string\n                                            [updated] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (79 rows, 79 analyzed)\n            [created_at] => 2025-08-11 13:35:58\n            [updated_at] => 2025-08-11 13:35:58\n        )\n\n    [table_name] => autobooks_sketchup_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-11 13:36:09] [data-table.edge.php:146] Data table template: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:36:09] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:36:12] [data-table.edge.php:146] Data table template: table=autobooks_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:36:12] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:36:13] [data-table.edge.php:146] Data table template: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:36:13] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:44:28] [data-table.edge.php:146] Data table template: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:44:28] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:47:35] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-11 13:47:35\n            [updated_at] => 2025-08-11 13:47:35\n        )\n\n    [table_name] => autobooks_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-11 13:48:15] [data-table.edge.php:146] Data table template: table=autobooks_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:48:15] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:49:45] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_855a49c592df215a4abe977e4b4ebe1e\n                            [label] => Sold To Name\n                            [field] => sold_to_name\n                            [fields] => Array\n                                (\n                                    [0] => sold_to_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_1a78581b905a2b8b3819cda087a681df\n                            [label] => Sold To Number\n                            [field] => sold_to_number\n                            [fields] => Array\n                                (\n                                    [0] => sold_to_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_0ec1da7f1dc2f8abef07b11a3193ef57\n                            [label] => Vendor Name\n                            [field] => vendor_name\n                            [fields] => Array\n                                (\n                                    [0] => vendor_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_2824711b2f4f6060078d068bf40f1e05\n                            [label] => Reseller Number\n                            [field] => reseller_number\n                            [fields] => Array\n                                (\n                                    [0] => reseller_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_46140d0548ed0b78e29e04793ef2af1b\n                            [label] => Reseller Vendor\n                            [field] => reseller_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => reseller_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_c706df21763a2714816b0a44cfe646c7\n                            [label] => End Customer Vendor\n                            [field] => end_customer_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_033954cce10564766238dfac726c2258\n                            [label] => End Customer Name\n                            [field] => end_customer_name\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_6e034352d34c13782d8d0bc5d65011bd\n                            [label] => End Customer Address 1\n                            [field] => end_customer_address_1\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_1\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_ea92edd1525cbbe624e2a16347e81e08\n                            [label] => End Customer Address 2\n                            [field] => end_customer_address_2\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_2\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_fd89e44f39c29b2e6e5ee8afcf84fe01\n                            [label] => End Customer Address 3\n                            [field] => end_customer_address_3\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_3\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_58125853b8960d245a3b5005f35a258c\n                            [label] => End Customer City\n                            [field] => end_customer_city\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_1f0774740183acc085318ae5debb1a62\n                            [label] => End Customer State\n                            [field] => end_customer_state\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_state\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_3efae453b93cd454ce02cf110d95f12d\n                            [label] => End Customer Zip Code\n                            [field] => end_customer_zip_code\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_zip_code\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_20d02fa8403a932b1721c8d870ae0495\n                            [label] => End Customer Country\n                            [field] => end_customer_country\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [15] => Array\n                        (\n                            [id] => col_15_8915939119a19af6a75ea0edf6663bba\n                            [label] => End Customer Account Type\n                            [field] => end_customer_account_type\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_account_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [16] => Array\n                        (\n                            [id] => col_16_5fdcafa9911363ca87918c99782fea0c\n                            [label] => End Customer Contact Name\n                            [field] => end_customer_contact_name\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [17] => Array\n                        (\n                            [id] => col_17_6d1e35008cf647b5c049c591cd4d3b7e\n                            [label] => End Customer Contact Email\n                            [field] => end_customer_contact_email\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_email\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [18] => Array\n                        (\n                            [id] => col_18_948bf27ad83a985d151cfdedc2fd4adf\n                            [label] => End Customer Contact Phone\n                            [field] => end_customer_contact_phone\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_phone\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [19] => Array\n                        (\n                            [id] => col_19_c4aaf1d01f58e409021771db8b5bcc31\n                            [label] => End Customer Industry Segment\n                            [field] => end_customer_industry_segment\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_industry_segment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [20] => Array\n                        (\n                            [id] => col_20_54215071b2e666430d1f2ef806d223e6\n                            [label] => Agreement Program Name\n                            [field] => agreement_program_name\n                            [fields] => Array\n                                (\n                                    [0] => agreement_program_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [21] => Array\n                        (\n                            [id] => col_21_881bfc8855aa6d118a647f50872f9c37\n                            [label] => Agreement Number\n                            [field] => agreement_number\n                            [fields] => Array\n                                (\n                                    [0] => agreement_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [22] => Array\n                        (\n                            [id] => col_22_f67fe48b64d22349cd8a3679e72358ad\n                            [label] => Agreement Start Date\n                            [field] => agreement_start_date\n                            [fields] => Array\n                                (\n                                    [0] => agreement_start_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [23] => Array\n                        (\n                            [id] => col_23_ee6ffb2ed1207134c2506d8e95f6f68a\n                            [label] => Agreement End Date\n                            [field] => agreement_end_date\n                            [fields] => Array\n                                (\n                                    [0] => agreement_end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [24] => Array\n                        (\n                            [id] => col_24_2d017dd473b0b2349dceb1d7f33e3e44\n                            [label] => Agreement Terms\n                            [field] => agreement_terms\n                            [fields] => Array\n                                (\n                                    [0] => agreement_terms\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [25] => Array\n                        (\n                            [id] => col_25_8af78a35c6e73e79bf51fc8563b871fe\n                            [label] => Agreement Type\n                            [field] => agreement_type\n                            [fields] => Array\n                                (\n                                    [0] => agreement_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [26] => Array\n                        (\n                            [id] => col_26_adb05f2097fd47dcf014a7b073b34987\n                            [label] => Agreement Status\n                            [field] => agreement_status\n                            [fields] => Array\n                                (\n                                    [0] => agreement_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [27] => Array\n                        (\n                            [id] => col_27_b4d565e162b8e7bc13cb12729aae502a\n                            [label] => Agreement Support Level\n                            [field] => agreement_support_level\n                            [fields] => Array\n                                (\n                                    [0] => agreement_support_level\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [28] => Array\n                        (\n                            [id] => col_28_52c6a8653a43c2ab289ef6e42d0dcc08\n                            [label] => Agreement Days Due\n                            [field] => agreement_days_due\n                            [fields] => Array\n                                (\n                                    [0] => agreement_days_due\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [29] => Array\n                        (\n                            [id] => col_29_a6a4ccca71939f4261a20b0006726122\n                            [label] => Agreement Autorenew\n                            [field] => agreement_autorenew\n                            [fields] => Array\n                                (\n                                    [0] => agreement_autorenew\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [30] => Array\n                        (\n                            [id] => col_30_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [31] => Array\n                        (\n                            [id] => col_31_31981304965b1a7ee49c64d093e02e94\n                            [label] => Product Family\n                            [field] => product_family\n                            [fields] => Array\n                                (\n                                    [0] => product_family\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [32] => Array\n                        (\n                            [id] => col_32_7d11591eed252e3b1e06c3c8b170dd38\n                            [label] => Product Market Segment\n                            [field] => product_market_segment\n                            [fields] => Array\n                                (\n                                    [0] => product_market_segment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [33] => Array\n                        (\n                            [id] => col_33_b647350956b1adeea9069ef1306de753\n                            [label] => Product Release\n                            [field] => product_release\n                            [fields] => Array\n                                (\n                                    [0] => product_release\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [34] => Array\n                        (\n                            [id] => col_34_b1d9df66b969ebda1ccfb1be323f165b\n                            [label] => Product Type\n                            [field] => product_type\n                            [fields] => Array\n                                (\n                                    [0] => product_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [35] => Array\n                        (\n                            [id] => col_35_a4ec132ba158d45a164578a02f6ef646\n                            [label] => Product Deployment\n                            [field] => product_deployment\n                            [fields] => Array\n                                (\n                                    [0] => product_deployment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [36] => Array\n                        (\n                            [id] => col_36_197d90cc45a4687eb1ef153ac86bc05f\n                            [label] => Product Sku\n                            [field] => product_sku\n                            [fields] => Array\n                                (\n                                    [0] => product_sku\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [37] => Array\n                        (\n                            [id] => col_37_b487a1edb0f07fa54d48393bbfea8139\n                            [label] => Product Sku Description\n                            [field] => product_sku_description\n                            [fields] => Array\n                                (\n                                    [0] => product_sku_description\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [38] => Array\n                        (\n                            [id] => col_38_fdff8146fc34b8e8e8ee5abe920d4806\n                            [label] => Product Part\n                            [field] => product_part\n                            [fields] => Array\n                                (\n                                    [0] => product_part\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [39] => Array\n                        (\n                            [id] => col_39_ceefc2cd7f84cf31ee379912ce351ad3\n                            [label] => Product List Price\n                            [field] => product_list_price\n                            [fields] => Array\n                                (\n                                    [0] => product_list_price\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [40] => Array\n                        (\n                            [id] => col_40_a851a28a6e219e3ec67e534c626aeaf3\n                            [label] => Product List Price Currency\n                            [field] => product_list_price_currency\n                            [fields] => Array\n                                (\n                                    [0] => product_list_price_currency\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [41] => Array\n                        (\n                            [id] => col_41_ef42673f634b68005996f2c64d6a72e3\n                            [label] => Subscription\n                            [field] => subscription_id\n                            [fields] => Array\n                                (\n                                    [0] => subscription_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [42] => Array\n                        (\n                            [id] => col_42_8c20c72b0daef191013b97458e02bb25\n                            [label] => Subscription Serial Number\n                            [field] => subscription_serial_number\n                            [fields] => Array\n                                (\n                                    [0] => subscription_serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [43] => Array\n                        (\n                            [id] => col_43_2ec2483de1ecdf1c9ccb681489ffa517\n                            [label] => Subscription Status\n                            [field] => subscription_status\n                            [fields] => Array\n                                (\n                                    [0] => subscription_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [44] => Array\n                        (\n                            [id] => col_44_5401f206636abe557cb9b60eca0ca28f\n                            [label] => Subscription Quantity\n                            [field] => subscription_quantity\n                            [fields] => Array\n                                (\n                                    [0] => subscription_quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [45] => Array\n                        (\n                            [id] => col_45_9c01b07be7b67284c181d0855827c151\n                            [label] => Subscription Start Date\n                            [field] => subscription_start_date\n                            [fields] => Array\n                                (\n                                    [0] => subscription_start_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [46] => Array\n                        (\n                            [id] => col_46_ab5851cb73c308ad66653249acbaefe3\n                            [label] => Subscription End Date\n                            [field] => subscription_end_date\n                            [fields] => Array\n                                (\n                                    [0] => subscription_end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [47] => Array\n                        (\n                            [id] => col_47_e9775f536b7ca1a1fcc63bbce1a54d62\n                            [label] => Subscription Contact Name\n                            [field] => subscription_contact_name\n                            [fields] => Array\n                                (\n                                    [0] => subscription_contact_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [48] => Array\n                        (\n                            [id] => col_48_e1e0a62a0901372172e3a54b62492a89\n                            [label] => Subscription Contact Email\n                            [field] => subscription_contact_email\n                            [fields] => Array\n                                (\n                                    [0] => subscription_contact_email\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [49] => Array\n                        (\n                            [id] => col_49_aa1d9b48f2e7b9108127d34afdb14da8\n                            [label] => Subscription Level\n                            [field] => subscription_level\n                            [fields] => Array\n                                (\n                                    [0] => subscription_level\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [50] => Array\n                        (\n                            [id] => col_50_adafdf8e0515f1788d271d09e44dd3f2\n                            [label] => Subscription Days Due\n                            [field] => subscription_days_due\n                            [fields] => Array\n                                (\n                                    [0] => subscription_days_due\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [51] => Array\n                        (\n                            [id] => col_51_2b6213d56e0299b7b5a352f7337328d8\n                            [label] => Quotation\n                            [field] => quotation_id\n                            [fields] => Array\n                                (\n                                    [0] => quotation_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [52] => Array\n                        (\n                            [id] => col_52_737c3c888ee0cc2600a2dc0d7f55ed05\n                            [label] => Quotation Type\n                            [field] => quotation_type\n                            [fields] => Array\n                                (\n                                    [0] => quotation_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [53] => Array\n                        (\n                            [id] => col_53_eb42bc3afe7c7f333989b1a3ab57bac0\n                            [label] => Quotation Vendor\n                            [field] => quotation_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => quotation_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [54] => Array\n                        (\n                            [id] => col_54_f83eab3e651df57f2cfbdaedec949a82\n                            [label] => Quotation Deal Registration Number\n                            [field] => quotation_deal_registration_number\n                            [fields] => Array\n                                (\n                                    [0] => quotation_deal_registration_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [55] => Array\n                        (\n                            [id] => col_55_a52a77131c467520565de2fb0c065189\n                            [label] => Quotation Status\n                            [field] => quotation_status\n                            [fields] => Array\n                                (\n                                    [0] => quotation_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [56] => Array\n                        (\n                            [id] => col_56_df1b0dfe471a20da3523bf2c525c3f39\n                            [label] => Quotation Resellerpo Previous\n                            [field] => quotation_resellerpo_previous\n                            [fields] => Array\n                                (\n                                    [0] => quotation_resellerpo_previous\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [57] => Array\n                        (\n                            [id] => col_57_5add4b7dce2f0f862189499e578133ea\n                            [label] => Quotation Due Date\n                            [field] => quotation_due_date\n                            [fields] => Array\n                                (\n                                    [0] => quotation_due_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [58] => Array\n                        (\n                            [id] => col_58_1180a372ed0e4f6d0f6d1babd14e28fb\n                            [label] => Flaer Phase\n                            [field] => flaer_phase\n                            [fields] => Array\n                                (\n                                    [0] => flaer_phase\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [59] => Array\n                        (\n                            [id] => col_59_0f81d52e06caaa4860887488d18271c7\n                            [label] => Updated\n                            [field] => updated\n                            [fields] => Array\n                                (\n                                    [0] => updated\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [60] => Array\n                        (\n                            [id] => col_60_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [61] => Array\n                        (\n                            [id] => col_61_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => sold_to_name\n                    [2] => sold_to_number\n                    [3] => vendor_name\n                    [4] => reseller_number\n                    [5] => reseller_vendor_id\n                    [6] => end_customer_vendor_id\n                    [7] => end_customer_name\n                    [8] => end_customer_address_1\n                    [9] => end_customer_address_2\n                    [10] => end_customer_address_3\n                    [11] => end_customer_city\n                    [12] => end_customer_state\n                    [13] => end_customer_zip_code\n                    [14] => end_customer_country\n                    [15] => end_customer_account_type\n                    [16] => end_customer_contact_name\n                    [17] => end_customer_contact_email\n                    [18] => end_customer_contact_phone\n                    [19] => end_customer_industry_segment\n                    [20] => agreement_program_name\n                    [21] => agreement_number\n                    [22] => agreement_start_date\n                    [23] => agreement_end_date\n                    [24] => agreement_terms\n                    [25] => agreement_type\n                    [26] => agreement_status\n                    [27] => agreement_support_level\n                    [28] => agreement_days_due\n                    [29] => agreement_autorenew\n                    [30] => product_name\n                    [31] => product_family\n                    [32] => product_market_segment\n                    [33] => product_release\n                    [34] => product_type\n                    [35] => product_deployment\n                    [36] => product_sku\n                    [37] => product_sku_description\n                    [38] => product_part\n                    [39] => product_list_price\n                    [40] => product_list_price_currency\n                    [41] => subscription_id\n                    [42] => subscription_serial_number\n                    [43] => subscription_status\n                    [44] => subscription_quantity\n                    [45] => subscription_start_date\n                    [46] => subscription_end_date\n                    [47] => subscription_contact_name\n                    [48] => subscription_contact_email\n                    [49] => subscription_level\n                    [50] => subscription_days_due\n                    [51] => quotation_id\n                    [52] => quotation_type\n                    [53] => quotation_vendor_id\n                    [54] => quotation_deal_registration_number\n                    [55] => quotation_status\n                    [56] => quotation_resellerpo_previous\n                    [57] => quotation_due_date\n                    [58] => flaer_phase\n                    [59] => updated\n                    [60] => created_at\n                    [61] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_sketchup_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [sold_to_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [sold_to_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [vendor_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [reseller_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [reseller_vendor_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_vendor_id] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_1] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_2] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_3] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_state] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_zip_code] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_account_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_email] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_phone] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_industry_segment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_program_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [agreement_start_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_terms] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_support_level] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_days_due] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [agreement_autorenew] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_family] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_market_segment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_release] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_deployment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_sku] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_sku_description] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_part] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_list_price] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [product_list_price_currency] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_quantity] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [subscription_start_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_contact_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_contact_email] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_level] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_days_due] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [quotation_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_vendor_id] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [quotation_deal_registration_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_resellerpo_previous] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_due_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [flaer_phase] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [updated] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_sketchup_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [sold_to_name] => sold_to_name\n                                            [sold_to_number] => sold_to_number\n                                            [vendor_name] => vendor_name\n                                            [reseller_number] => reseller_number\n                                            [reseller_vendor_id] => reseller_vendor_id\n                                            [end_customer_vendor_id] => end_customer_vendor_id\n                                            [end_customer_name] => end_customer_name\n                                            [end_customer_address_1] => end_customer_address_1\n                                            [end_customer_address_2] => end_customer_address_2\n                                            [end_customer_address_3] => end_customer_address_3\n                                            [end_customer_city] => end_customer_city\n                                            [end_customer_state] => end_customer_state\n                                            [end_customer_zip_code] => end_customer_zip_code\n                                            [end_customer_country] => end_customer_country\n                                            [end_customer_account_type] => end_customer_account_type\n                                            [end_customer_contact_name] => end_customer_contact_name\n                                            [end_customer_contact_email] => end_customer_contact_email\n                                            [end_customer_contact_phone] => end_customer_contact_phone\n                                            [end_customer_industry_segment] => end_customer_industry_segment\n                                            [agreement_program_name] => agreement_program_name\n                                            [agreement_number] => agreement_number\n                                            [agreement_start_date] => agreement_start_date\n                                            [agreement_end_date] => agreement_end_date\n                                            [agreement_terms] => agreement_terms\n                                            [agreement_type] => agreement_type\n                                            [agreement_status] => agreement_status\n                                            [agreement_support_level] => agreement_support_level\n                                            [agreement_days_due] => agreement_days_due\n                                            [agreement_autorenew] => agreement_autorenew\n                                            [product_name] => product_name\n                                            [product_family] => product_family\n                                            [product_market_segment] => product_market_segment\n                                            [product_release] => product_release\n                                            [product_type] => product_type\n                                            [product_deployment] => product_deployment\n                                            [product_sku] => product_sku\n                                            [product_sku_description] => product_sku_description\n                                            [product_part] => product_part\n                                            [product_list_price] => product_list_price\n                                            [product_list_price_currency] => product_list_price_currency\n                                            [subscription_id] => subscription_id\n                                            [subscription_serial_number] => subscription_serial_number\n                                            [subscription_status] => subscription_status\n                                            [subscription_quantity] => subscription_quantity\n                                            [subscription_start_date] => subscription_start_date\n                                            [subscription_end_date] => subscription_end_date\n                                            [subscription_contact_name] => subscription_contact_name\n                                            [subscription_contact_email] => subscription_contact_email\n                                            [subscription_level] => subscription_level\n                                            [subscription_days_due] => subscription_days_due\n                                            [quotation_id] => quotation_id\n                                            [quotation_type] => quotation_type\n                                            [quotation_vendor_id] => quotation_vendor_id\n                                            [quotation_deal_registration_number] => quotation_deal_registration_number\n                                            [quotation_status] => quotation_status\n                                            [quotation_resellerpo_previous] => quotation_resellerpo_previous\n                                            [quotation_due_date] => quotation_due_date\n                                            [flaer_phase] => flaer_phase\n                                            [updated] => updated\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [sold_to_name] => string\n                                            [sold_to_number] => integer\n                                            [vendor_name] => string\n                                            [reseller_number] => integer\n                                            [reseller_vendor_id] => string\n                                            [end_customer_vendor_id] => integer\n                                            [end_customer_name] => string\n                                            [end_customer_address_1] => string\n                                            [end_customer_address_2] => string\n                                            [end_customer_address_3] => string\n                                            [end_customer_city] => string\n                                            [end_customer_state] => string\n                                            [end_customer_zip_code] => string\n                                            [end_customer_country] => string\n                                            [end_customer_account_type] => string\n                                            [end_customer_contact_name] => string\n                                            [end_customer_contact_email] => string\n                                            [end_customer_contact_phone] => string\n                                            [end_customer_industry_segment] => string\n                                            [agreement_program_name] => string\n                                            [agreement_number] => integer\n                                            [agreement_start_date] => string\n                                            [agreement_end_date] => string\n                                            [agreement_terms] => string\n                                            [agreement_type] => string\n                                            [agreement_status] => string\n                                            [agreement_support_level] => string\n                                            [agreement_days_due] => integer\n                                            [agreement_autorenew] => integer\n                                            [product_name] => string\n                                            [product_family] => string\n                                            [product_market_segment] => string\n                                            [product_release] => string\n                                            [product_type] => string\n                                            [product_deployment] => string\n                                            [product_sku] => string\n                                            [product_sku_description] => string\n                                            [product_part] => string\n                                            [product_list_price] => integer\n                                            [product_list_price_currency] => string\n                                            [subscription_id] => string\n                                            [subscription_serial_number] => string\n                                            [subscription_status] => string\n                                            [subscription_quantity] => integer\n                                            [subscription_start_date] => string\n                                            [subscription_end_date] => string\n                                            [subscription_contact_name] => string\n                                            [subscription_contact_email] => string\n                                            [subscription_level] => string\n                                            [subscription_days_due] => integer\n                                            [quotation_id] => string\n                                            [quotation_type] => string\n                                            [quotation_vendor_id] => integer\n                                            [quotation_deal_registration_number] => string\n                                            [quotation_status] => string\n                                            [quotation_resellerpo_previous] => string\n                                            [quotation_due_date] => string\n                                            [flaer_phase] => string\n                                            [updated] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (79 rows, 79 analyzed)\n            [created_at] => 2025-08-11 13:49:45\n            [updated_at] => 2025-08-11 13:49:45\n        )\n\n    [table_name] => autobooks_sketchup_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-11 13:52:44] [data-table.edge.php:146] Data table template: table=autobooks_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:52:44] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:52:47] [data-table.edge.php:146] Data table template: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 13:52:47] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:22:10] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_855a49c592df215a4abe977e4b4ebe1e\n                            [label] => Sold To Name\n                            [field] => sold_to_name\n                            [fields] => Array\n                                (\n                                    [0] => sold_to_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_1a78581b905a2b8b3819cda087a681df\n                            [label] => Sold To Number\n                            [field] => sold_to_number\n                            [fields] => Array\n                                (\n                                    [0] => sold_to_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_0ec1da7f1dc2f8abef07b11a3193ef57\n                            [label] => Vendor Name\n                            [field] => vendor_name\n                            [fields] => Array\n                                (\n                                    [0] => vendor_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_2824711b2f4f6060078d068bf40f1e05\n                            [label] => Reseller Number\n                            [field] => reseller_number\n                            [fields] => Array\n                                (\n                                    [0] => reseller_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_46140d0548ed0b78e29e04793ef2af1b\n                            [label] => Reseller Vendor\n                            [field] => reseller_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => reseller_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_c706df21763a2714816b0a44cfe646c7\n                            [label] => End Customer Vendor\n                            [field] => end_customer_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_033954cce10564766238dfac726c2258\n                            [label] => End Customer Name\n                            [field] => end_customer_name\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_6e034352d34c13782d8d0bc5d65011bd\n                            [label] => End Customer Address 1\n                            [field] => end_customer_address_1\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_1\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_ea92edd1525cbbe624e2a16347e81e08\n                            [label] => End Customer Address 2\n                            [field] => end_customer_address_2\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_2\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_fd89e44f39c29b2e6e5ee8afcf84fe01\n                            [label] => End Customer Address 3\n                            [field] => end_customer_address_3\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_address_3\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_58125853b8960d245a3b5005f35a258c\n                            [label] => End Customer City\n                            [field] => end_customer_city\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_1f0774740183acc085318ae5debb1a62\n                            [label] => End Customer State\n                            [field] => end_customer_state\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_state\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_3efae453b93cd454ce02cf110d95f12d\n                            [label] => End Customer Zip Code\n                            [field] => end_customer_zip_code\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_zip_code\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_20d02fa8403a932b1721c8d870ae0495\n                            [label] => End Customer Country\n                            [field] => end_customer_country\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [15] => Array\n                        (\n                            [id] => col_15_8915939119a19af6a75ea0edf6663bba\n                            [label] => End Customer Account Type\n                            [field] => end_customer_account_type\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_account_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [16] => Array\n                        (\n                            [id] => col_16_5fdcafa9911363ca87918c99782fea0c\n                            [label] => End Customer Contact Name\n                            [field] => end_customer_contact_name\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [17] => Array\n                        (\n                            [id] => col_17_6d1e35008cf647b5c049c591cd4d3b7e\n                            [label] => End Customer Contact Email\n                            [field] => end_customer_contact_email\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_email\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [18] => Array\n                        (\n                            [id] => col_18_948bf27ad83a985d151cfdedc2fd4adf\n                            [label] => End Customer Contact Phone\n                            [field] => end_customer_contact_phone\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_contact_phone\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [19] => Array\n                        (\n                            [id] => col_19_c4aaf1d01f58e409021771db8b5bcc31\n                            [label] => End Customer Industry Segment\n                            [field] => end_customer_industry_segment\n                            [fields] => Array\n                                (\n                                    [0] => end_customer_industry_segment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [20] => Array\n                        (\n                            [id] => col_20_54215071b2e666430d1f2ef806d223e6\n                            [label] => Agreement Program Name\n                            [field] => agreement_program_name\n                            [fields] => Array\n                                (\n                                    [0] => agreement_program_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [21] => Array\n                        (\n                            [id] => col_21_881bfc8855aa6d118a647f50872f9c37\n                            [label] => Agreement Number\n                            [field] => agreement_number\n                            [fields] => Array\n                                (\n                                    [0] => agreement_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [22] => Array\n                        (\n                            [id] => col_22_f67fe48b64d22349cd8a3679e72358ad\n                            [label] => Agreement Start Date\n                            [field] => agreement_start_date\n                            [fields] => Array\n                                (\n                                    [0] => agreement_start_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [23] => Array\n                        (\n                            [id] => col_23_ee6ffb2ed1207134c2506d8e95f6f68a\n                            [label] => Agreement End Date\n                            [field] => agreement_end_date\n                            [fields] => Array\n                                (\n                                    [0] => agreement_end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [24] => Array\n                        (\n                            [id] => col_24_2d017dd473b0b2349dceb1d7f33e3e44\n                            [label] => Agreement Terms\n                            [field] => agreement_terms\n                            [fields] => Array\n                                (\n                                    [0] => agreement_terms\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [25] => Array\n                        (\n                            [id] => col_25_8af78a35c6e73e79bf51fc8563b871fe\n                            [label] => Agreement Type\n                            [field] => agreement_type\n                            [fields] => Array\n                                (\n                                    [0] => agreement_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [26] => Array\n                        (\n                            [id] => col_26_adb05f2097fd47dcf014a7b073b34987\n                            [label] => Agreement Status\n                            [field] => agreement_status\n                            [fields] => Array\n                                (\n                                    [0] => agreement_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [27] => Array\n                        (\n                            [id] => col_27_b4d565e162b8e7bc13cb12729aae502a\n                            [label] => Agreement Support Level\n                            [field] => agreement_support_level\n                            [fields] => Array\n                                (\n                                    [0] => agreement_support_level\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [28] => Array\n                        (\n                            [id] => col_28_52c6a8653a43c2ab289ef6e42d0dcc08\n                            [label] => Agreement Days Due\n                            [field] => agreement_days_due\n                            [fields] => Array\n                                (\n                                    [0] => agreement_days_due\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [29] => Array\n                        (\n                            [id] => col_29_a6a4ccca71939f4261a20b0006726122\n                            [label] => Agreement Autorenew\n                            [field] => agreement_autorenew\n                            [fields] => Array\n                                (\n                                    [0] => agreement_autorenew\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [30] => Array\n                        (\n                            [id] => col_30_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [31] => Array\n                        (\n                            [id] => col_31_31981304965b1a7ee49c64d093e02e94\n                            [label] => Product Family\n                            [field] => product_family\n                            [fields] => Array\n                                (\n                                    [0] => product_family\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [32] => Array\n                        (\n                            [id] => col_32_7d11591eed252e3b1e06c3c8b170dd38\n                            [label] => Product Market Segment\n                            [field] => product_market_segment\n                            [fields] => Array\n                                (\n                                    [0] => product_market_segment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [33] => Array\n                        (\n                            [id] => col_33_b647350956b1adeea9069ef1306de753\n                            [label] => Product Release\n                            [field] => product_release\n                            [fields] => Array\n                                (\n                                    [0] => product_release\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [34] => Array\n                        (\n                            [id] => col_34_b1d9df66b969ebda1ccfb1be323f165b\n                            [label] => Product Type\n                            [field] => product_type\n                            [fields] => Array\n                                (\n                                    [0] => product_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [35] => Array\n                        (\n                            [id] => col_35_a4ec132ba158d45a164578a02f6ef646\n                            [label] => Product Deployment\n                            [field] => product_deployment\n                            [fields] => Array\n                                (\n                                    [0] => product_deployment\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [36] => Array\n                        (\n                            [id] => col_36_197d90cc45a4687eb1ef153ac86bc05f\n                            [label] => Product Sku\n                            [field] => product_sku\n                            [fields] => Array\n                                (\n                                    [0] => product_sku\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [37] => Array\n                        (\n                            [id] => col_37_b487a1edb0f07fa54d48393bbfea8139\n                            [label] => Product Sku Description\n                            [field] => product_sku_description\n                            [fields] => Array\n                                (\n                                    [0] => product_sku_description\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [38] => Array\n                        (\n                            [id] => col_38_fdff8146fc34b8e8e8ee5abe920d4806\n                            [label] => Product Part\n                            [field] => product_part\n                            [fields] => Array\n                                (\n                                    [0] => product_part\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [39] => Array\n                        (\n                            [id] => col_39_ceefc2cd7f84cf31ee379912ce351ad3\n                            [label] => Product List Price\n                            [field] => product_list_price\n                            [fields] => Array\n                                (\n                                    [0] => product_list_price\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [40] => Array\n                        (\n                            [id] => col_40_a851a28a6e219e3ec67e534c626aeaf3\n                            [label] => Product List Price Currency\n                            [field] => product_list_price_currency\n                            [fields] => Array\n                                (\n                                    [0] => product_list_price_currency\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [41] => Array\n                        (\n                            [id] => col_41_ef42673f634b68005996f2c64d6a72e3\n                            [label] => Subscription\n                            [field] => subscription_id\n                            [fields] => Array\n                                (\n                                    [0] => subscription_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [42] => Array\n                        (\n                            [id] => col_42_8c20c72b0daef191013b97458e02bb25\n                            [label] => Subscription Serial Number\n                            [field] => subscription_serial_number\n                            [fields] => Array\n                                (\n                                    [0] => subscription_serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [43] => Array\n                        (\n                            [id] => col_43_2ec2483de1ecdf1c9ccb681489ffa517\n                            [label] => Subscription Status\n                            [field] => subscription_status\n                            [fields] => Array\n                                (\n                                    [0] => subscription_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [44] => Array\n                        (\n                            [id] => col_44_5401f206636abe557cb9b60eca0ca28f\n                            [label] => Subscription Quantity\n                            [field] => subscription_quantity\n                            [fields] => Array\n                                (\n                                    [0] => subscription_quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [45] => Array\n                        (\n                            [id] => col_45_9c01b07be7b67284c181d0855827c151\n                            [label] => Subscription Start Date\n                            [field] => subscription_start_date\n                            [fields] => Array\n                                (\n                                    [0] => subscription_start_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [46] => Array\n                        (\n                            [id] => col_46_ab5851cb73c308ad66653249acbaefe3\n                            [label] => Subscription End Date\n                            [field] => subscription_end_date\n                            [fields] => Array\n                                (\n                                    [0] => subscription_end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [47] => Array\n                        (\n                            [id] => col_47_e9775f536b7ca1a1fcc63bbce1a54d62\n                            [label] => Subscription Contact Name\n                            [field] => subscription_contact_name\n                            [fields] => Array\n                                (\n                                    [0] => subscription_contact_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [48] => Array\n                        (\n                            [id] => col_48_e1e0a62a0901372172e3a54b62492a89\n                            [label] => Subscription Contact Email\n                            [field] => subscription_contact_email\n                            [fields] => Array\n                                (\n                                    [0] => subscription_contact_email\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [49] => Array\n                        (\n                            [id] => col_49_aa1d9b48f2e7b9108127d34afdb14da8\n                            [label] => Subscription Level\n                            [field] => subscription_level\n                            [fields] => Array\n                                (\n                                    [0] => subscription_level\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [50] => Array\n                        (\n                            [id] => col_50_adafdf8e0515f1788d271d09e44dd3f2\n                            [label] => Subscription Days Due\n                            [field] => subscription_days_due\n                            [fields] => Array\n                                (\n                                    [0] => subscription_days_due\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [51] => Array\n                        (\n                            [id] => col_51_2b6213d56e0299b7b5a352f7337328d8\n                            [label] => Quotation\n                            [field] => quotation_id\n                            [fields] => Array\n                                (\n                                    [0] => quotation_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [52] => Array\n                        (\n                            [id] => col_52_737c3c888ee0cc2600a2dc0d7f55ed05\n                            [label] => Quotation Type\n                            [field] => quotation_type\n                            [fields] => Array\n                                (\n                                    [0] => quotation_type\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [53] => Array\n                        (\n                            [id] => col_53_eb42bc3afe7c7f333989b1a3ab57bac0\n                            [label] => Quotation Vendor\n                            [field] => quotation_vendor_id\n                            [fields] => Array\n                                (\n                                    [0] => quotation_vendor_id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [54] => Array\n                        (\n                            [id] => col_54_f83eab3e651df57f2cfbdaedec949a82\n                            [label] => Quotation Deal Registration Number\n                            [field] => quotation_deal_registration_number\n                            [fields] => Array\n                                (\n                                    [0] => quotation_deal_registration_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [55] => Array\n                        (\n                            [id] => col_55_a52a77131c467520565de2fb0c065189\n                            [label] => Quotation Status\n                            [field] => quotation_status\n                            [fields] => Array\n                                (\n                                    [0] => quotation_status\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [56] => Array\n                        (\n                            [id] => col_56_df1b0dfe471a20da3523bf2c525c3f39\n                            [label] => Quotation Resellerpo Previous\n                            [field] => quotation_resellerpo_previous\n                            [fields] => Array\n                                (\n                                    [0] => quotation_resellerpo_previous\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [57] => Array\n                        (\n                            [id] => col_57_5add4b7dce2f0f862189499e578133ea\n                            [label] => Quotation Due Date\n                            [field] => quotation_due_date\n                            [fields] => Array\n                                (\n                                    [0] => quotation_due_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [58] => Array\n                        (\n                            [id] => col_58_1180a372ed0e4f6d0f6d1babd14e28fb\n                            [label] => Flaer Phase\n                            [field] => flaer_phase\n                            [fields] => Array\n                                (\n                                    [0] => flaer_phase\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [59] => Array\n                        (\n                            [id] => col_59_0f81d52e06caaa4860887488d18271c7\n                            [label] => Updated\n                            [field] => updated\n                            [fields] => Array\n                                (\n                                    [0] => updated\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [60] => Array\n                        (\n                            [id] => col_60_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [61] => Array\n                        (\n                            [id] => col_61_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => sold_to_name\n                    [2] => sold_to_number\n                    [3] => vendor_name\n                    [4] => reseller_number\n                    [5] => reseller_vendor_id\n                    [6] => end_customer_vendor_id\n                    [7] => end_customer_name\n                    [8] => end_customer_address_1\n                    [9] => end_customer_address_2\n                    [10] => end_customer_address_3\n                    [11] => end_customer_city\n                    [12] => end_customer_state\n                    [13] => end_customer_zip_code\n                    [14] => end_customer_country\n                    [15] => end_customer_account_type\n                    [16] => end_customer_contact_name\n                    [17] => end_customer_contact_email\n                    [18] => end_customer_contact_phone\n                    [19] => end_customer_industry_segment\n                    [20] => agreement_program_name\n                    [21] => agreement_number\n                    [22] => agreement_start_date\n                    [23] => agreement_end_date\n                    [24] => agreement_terms\n                    [25] => agreement_type\n                    [26] => agreement_status\n                    [27] => agreement_support_level\n                    [28] => agreement_days_due\n                    [29] => agreement_autorenew\n                    [30] => product_name\n                    [31] => product_family\n                    [32] => product_market_segment\n                    [33] => product_release\n                    [34] => product_type\n                    [35] => product_deployment\n                    [36] => product_sku\n                    [37] => product_sku_description\n                    [38] => product_part\n                    [39] => product_list_price\n                    [40] => product_list_price_currency\n                    [41] => subscription_id\n                    [42] => subscription_serial_number\n                    [43] => subscription_status\n                    [44] => subscription_quantity\n                    [45] => subscription_start_date\n                    [46] => subscription_end_date\n                    [47] => subscription_contact_name\n                    [48] => subscription_contact_email\n                    [49] => subscription_level\n                    [50] => subscription_days_due\n                    [51] => quotation_id\n                    [52] => quotation_type\n                    [53] => quotation_vendor_id\n                    [54] => quotation_deal_registration_number\n                    [55] => quotation_status\n                    [56] => quotation_resellerpo_previous\n                    [57] => quotation_due_date\n                    [58] => flaer_phase\n                    [59] => updated\n                    [60] => created_at\n                    [61] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_import_sketchup_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [sold_to_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [sold_to_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [vendor_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [reseller_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [reseller_vendor_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_vendor_id] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_1] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_2] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_address_3] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_state] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_zip_code] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_account_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_email] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_contact_phone] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_customer_industry_segment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_program_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_number] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [agreement_start_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_terms] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_support_level] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [agreement_days_due] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [agreement_autorenew] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_family] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_market_segment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_release] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_deployment] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_sku] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_sku_description] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_part] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_list_price] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [product_list_price_currency] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_quantity] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [subscription_start_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_contact_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_contact_email] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_level] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [subscription_days_due] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [quotation_id] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_type] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_vendor_id] => Array\n                                (\n                                    [type] => integer\n                                    [nullable] => 1\n                                )\n\n                            [quotation_deal_registration_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_status] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_resellerpo_previous] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quotation_due_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [flaer_phase] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [updated] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_import_sketchup_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [sold_to_name] => sold_to_name\n                                            [sold_to_number] => sold_to_number\n                                            [vendor_name] => vendor_name\n                                            [reseller_number] => reseller_number\n                                            [reseller_vendor_id] => reseller_vendor_id\n                                            [end_customer_vendor_id] => end_customer_vendor_id\n                                            [end_customer_name] => end_customer_name\n                                            [end_customer_address_1] => end_customer_address_1\n                                            [end_customer_address_2] => end_customer_address_2\n                                            [end_customer_address_3] => end_customer_address_3\n                                            [end_customer_city] => end_customer_city\n                                            [end_customer_state] => end_customer_state\n                                            [end_customer_zip_code] => end_customer_zip_code\n                                            [end_customer_country] => end_customer_country\n                                            [end_customer_account_type] => end_customer_account_type\n                                            [end_customer_contact_name] => end_customer_contact_name\n                                            [end_customer_contact_email] => end_customer_contact_email\n                                            [end_customer_contact_phone] => end_customer_contact_phone\n                                            [end_customer_industry_segment] => end_customer_industry_segment\n                                            [agreement_program_name] => agreement_program_name\n                                            [agreement_number] => agreement_number\n                                            [agreement_start_date] => agreement_start_date\n                                            [agreement_end_date] => agreement_end_date\n                                            [agreement_terms] => agreement_terms\n                                            [agreement_type] => agreement_type\n                                            [agreement_status] => agreement_status\n                                            [agreement_support_level] => agreement_support_level\n                                            [agreement_days_due] => agreement_days_due\n                                            [agreement_autorenew] => agreement_autorenew\n                                            [product_name] => product_name\n                                            [product_family] => product_family\n                                            [product_market_segment] => product_market_segment\n                                            [product_release] => product_release\n                                            [product_type] => product_type\n                                            [product_deployment] => product_deployment\n                                            [product_sku] => product_sku\n                                            [product_sku_description] => product_sku_description\n                                            [product_part] => product_part\n                                            [product_list_price] => product_list_price\n                                            [product_list_price_currency] => product_list_price_currency\n                                            [subscription_id] => subscription_id\n                                            [subscription_serial_number] => subscription_serial_number\n                                            [subscription_status] => subscription_status\n                                            [subscription_quantity] => subscription_quantity\n                                            [subscription_start_date] => subscription_start_date\n                                            [subscription_end_date] => subscription_end_date\n                                            [subscription_contact_name] => subscription_contact_name\n                                            [subscription_contact_email] => subscription_contact_email\n                                            [subscription_level] => subscription_level\n                                            [subscription_days_due] => subscription_days_due\n                                            [quotation_id] => quotation_id\n                                            [quotation_type] => quotation_type\n                                            [quotation_vendor_id] => quotation_vendor_id\n                                            [quotation_deal_registration_number] => quotation_deal_registration_number\n                                            [quotation_status] => quotation_status\n                                            [quotation_resellerpo_previous] => quotation_resellerpo_previous\n                                            [quotation_due_date] => quotation_due_date\n                                            [flaer_phase] => flaer_phase\n                                            [updated] => updated\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [sold_to_name] => string\n                                            [sold_to_number] => integer\n                                            [vendor_name] => string\n                                            [reseller_number] => integer\n                                            [reseller_vendor_id] => string\n                                            [end_customer_vendor_id] => integer\n                                            [end_customer_name] => string\n                                            [end_customer_address_1] => string\n                                            [end_customer_address_2] => string\n                                            [end_customer_address_3] => string\n                                            [end_customer_city] => string\n                                            [end_customer_state] => string\n                                            [end_customer_zip_code] => string\n                                            [end_customer_country] => string\n                                            [end_customer_account_type] => string\n                                            [end_customer_contact_name] => string\n                                            [end_customer_contact_email] => string\n                                            [end_customer_contact_phone] => string\n                                            [end_customer_industry_segment] => string\n                                            [agreement_program_name] => string\n                                            [agreement_number] => integer\n                                            [agreement_start_date] => string\n                                            [agreement_end_date] => string\n                                            [agreement_terms] => string\n                                            [agreement_type] => string\n                                            [agreement_status] => string\n                                            [agreement_support_level] => string\n                                            [agreement_days_due] => integer\n                                            [agreement_autorenew] => integer\n                                            [product_name] => string\n                                            [product_family] => string\n                                            [product_market_segment] => string\n                                            [product_release] => string\n                                            [product_type] => string\n                                            [product_deployment] => string\n                                            [product_sku] => string\n                                            [product_sku_description] => string\n                                            [product_part] => string\n                                            [product_list_price] => integer\n                                            [product_list_price_currency] => string\n                                            [subscription_id] => string\n                                            [subscription_serial_number] => string\n                                            [subscription_status] => string\n                                            [subscription_quantity] => integer\n                                            [subscription_start_date] => string\n                                            [subscription_end_date] => string\n                                            [subscription_contact_name] => string\n                                            [subscription_contact_email] => string\n                                            [subscription_level] => string\n                                            [subscription_days_due] => integer\n                                            [quotation_id] => string\n                                            [quotation_type] => string\n                                            [quotation_vendor_id] => integer\n                                            [quotation_deal_registration_number] => string\n                                            [quotation_status] => string\n                                            [quotation_resellerpo_previous] => string\n                                            [quotation_due_date] => string\n                                            [flaer_phase] => string\n                                            [updated] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (79 rows, 79 analyzed)\n            [created_at] => 2025-08-11 14:22:10\n            [updated_at] => 2025-08-11 14:22:10\n        )\n\n    [table_name] => autobooks_import_sketchup_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-11 14:22:19] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:22:19] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:22:53] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_import_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_import_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-11 14:22:53\n            [updated_at] => 2025-08-11 14:22:53\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-11 14:22:59] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:22:59] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:02] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:02] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:04] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:04] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:11] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:11] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:13] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:13] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:16] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:16] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:20] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:23:20] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:27:03] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:27:03] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:28:34] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:28:34] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:51] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:51] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:55] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:55] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:57] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:57] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:58] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:58] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:59] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:59] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:59] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:33:59] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:34:00] [data-table.edge.php:47]  Auto-loading data: table=autodesk_subscriptions, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-11 14:34:00] [data-table.edge.php:146] Data table template: table=autodesk_subscriptions, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:34:01] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:34:01] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:52:56] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 14:52:56] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 15:08:42] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 15:08:42] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 15:25:43] [data-table.edge.php:146] Data table template: table=api_system_data_table_data_table_filter, type=hardcoded, id=
[data_table_saga] [2025-08-11 19:37:45] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 19:37:45] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 20:16:00] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 20:16:00] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 20:16:01] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 20:16:01] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 20:25:29] [data-table.edge.php:146] Data table template: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 20:25:29] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_sketchup_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 20:26:09] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 20:26:09] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 21:50:34] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_import_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_import_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-11 21:50:34\n            [updated_at] => 2025-08-11 21:50:34\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-11 21:50:39] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 21:50:39] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 21:50:41] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 21:50:41] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 21:59:10] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 21:59:10] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 21:59:30] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_import_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_import_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-11 21:59:30\n            [updated_at] => 2025-08-11 21:59:30\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-11 21:59:34] [data-table.edge.php:146] Data table template: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 21:59:34] [data-table-column-manager.edge.php:46]  Column manager props: table=, type=hardcoded, id=
[data_table_saga] [2025-08-11 21:59:36] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 21:59:36] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 22:14:20] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-11 22:14:20] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-12 07:44:09] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-12 07:44:09] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=hardcoded, id=
[data_table_saga] [2025-08-12 08:27:28] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_import_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_import_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-12 08:27:28\n            [updated_at] => 2025-08-12 08:27:28\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-12 08:33:10] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_import_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_import_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-12 08:33:10\n            [updated_at] => 2025-08-12 08:33:10\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-12 08:38:38] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:38:38] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:38:39] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:38:42] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:38:42] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:38:42] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:39:05] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_import_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_import_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-12 08:39:05\n            [updated_at] => 2025-08-12 08:39:05\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-12 08:39:07] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:39:07] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:39:07] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:39:10] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:39:10] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:39:10] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:43:46] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:43:46] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:43:46] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:45:39] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_import_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_import_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-12 08:45:39\n            [updated_at] => 2025-08-12 08:45:39\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-12 08:45:43] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:45:43] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:45:43] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:45:45] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:45:45] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:45:45] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:53:50] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:53:50] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:53:50] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:53:57] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:53:57] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:53:57] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:54:40] [data_table_storage.class.php:76]  Array\n(\n    [configuration] => Array\n        (\n            [structure] => Array\n                (\n                    [0] => Array\n                        (\n                            [id] => col_0_b80bb7740288fda1f201890375a60c8f\n                            [label] => ID\n                            [field] => id\n                            [fields] => Array\n                                (\n                                    [0] => id\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [1] => Array\n                        (\n                            [id] => col_1_87da425e91c92b2f5d360a2c60693425\n                            [label] => Serial Number\n                            [field] => serial_number\n                            [fields] => Array\n                                (\n                                    [0] => serial_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [2] => Array\n                        (\n                            [id] => col_2_b068931cc450442b63f5b3d276ea4297\n                            [label] => Name\n                            [field] => name\n                            [fields] => Array\n                                (\n                                    [0] => name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [3] => Array\n                        (\n                            [id] => col_3_800c327aefb3f9241513cbf551abbfda\n                            [label] => Contract\n                            [field] => contract\n                            [fields] => Array\n                                (\n                                    [0] => contract\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [4] => Array\n                        (\n                            [id] => col_4_f5625f45195c610c4ab60fdc118a2cdb\n                            [label] => Product Name\n                            [field] => product_name\n                            [fields] => Array\n                                (\n                                    [0] => product_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [5] => Array\n                        (\n                            [id] => col_5_221d2a4bfdae13dbd5aeff3b02adb8c1\n                            [label] => Quantity\n                            [field] => quantity\n                            [fields] => Array\n                                (\n                                    [0] => quantity\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [6] => Array\n                        (\n                            [id] => col_6_90e8da89bb462061b421c1cb7191de14\n                            [label] => End Date\n                            [field] => end_date\n                            [fields] => Array\n                                (\n                                    [0] => end_date\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [7] => Array\n                        (\n                            [id] => col_7_288687c5e352091a731f928f9e04b104\n                            [label] => Account Primary Reseller Name\n                            [field] => account_primary_reseller_name\n                            [fields] => Array\n                                (\n                                    [0] => account_primary_reseller_name\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [8] => Array\n                        (\n                            [id] => col_8_0b73b47752e1f6c2ae0e52e96bbb8166\n                            [label] => Order Po Number\n                            [field] => order_po_number\n                            [fields] => Array\n                                (\n                                    [0] => order_po_number\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [9] => Array\n                        (\n                            [id] => col_9_f2c00c960c944ce89567d780ec95a374\n                            [label] => Order Shipping Address\n                            [field] => order_shipping_address\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_address\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [10] => Array\n                        (\n                            [id] => col_10_ff382ee56809d83cefb5a6915c993395\n                            [label] => Order Shipping City\n                            [field] => order_shipping_city\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_city\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [11] => Array\n                        (\n                            [id] => col_11_aa44159db36fc407282859a119c89a7e\n                            [label] => Order Shipping State Province\n                            [field] => order_shipping_state_province\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_state_province\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [12] => Array\n                        (\n                            [id] => col_12_80a83b2a849406c5d5451ab15626b86e\n                            [label] => Order Shipping Country\n                            [field] => order_shipping_country\n                            [fields] => Array\n                                (\n                                    [0] => order_shipping_country\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [13] => Array\n                        (\n                            [id] => col_13_fde81f1177b1541755b672c9dc97315a\n                            [label] => Created At\n                            [field] => created_at\n                            [fields] => Array\n                                (\n                                    [0] => created_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                    [14] => Array\n                        (\n                            [id] => col_14_afd1a1a8e69e4b32a11a645da8280f8a\n                            [label] => Updated At\n                            [field] => updated_at\n                            [fields] => Array\n                                (\n                                    [0] => updated_at\n                                )\n\n                            [filter] => \n                            [visible] => 1\n                        )\n\n                )\n\n            [hidden] => Array\n                (\n                )\n\n            [available_fields] => Array\n                (\n                    [0] => id\n                    [1] => serial_number\n                    [2] => name\n                    [3] => contract\n                    [4] => product_name\n                    [5] => quantity\n                    [6] => end_date\n                    [7] => account_primary_reseller_name\n                    [8] => order_po_number\n                    [9] => order_shipping_address\n                    [10] => order_shipping_city\n                    [11] => order_shipping_state_province\n                    [12] => order_shipping_country\n                    [13] => created_at\n                    [14] => updated_at\n                )\n\n            [table_schema] => Array\n                (\n                    [table_name] => autobooks_import_bluebeam_data\n                    [primary_key] => id\n                    [columns] => Array\n                        (\n                            [id] => Array\n                                (\n                                    [type] => increments\n                                    [nullable] => \n                                )\n\n                            [serial_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [contract] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [product_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [quantity] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [end_date] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [account_primary_reseller_name] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_po_number] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_address] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_city] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_state_province] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [order_shipping_country] => Array\n                                (\n                                    [type] => string\n                                    [length] => 255\n                                    [nullable] => 1\n                                )\n\n                            [created_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP\n                                )\n\n                            [updated_at] => Array\n                                (\n                                    [type] => timestamp\n                                    [default] => CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n                                )\n\n                        )\n\n                    [mapping] => Array\n                        (\n                            [main] => Array\n                                (\n                                    [table] => autobooks_import_bluebeam_data\n                                    [key] => id\n                                    [columns] => Array\n                                        (\n                                            [﻿Serial Number] => serial_number\n                                            [Name] => name\n                                            [Contract] => contract\n                                            [Product Name] => product_name\n                                            [Quantity] => quantity\n                                            [End Date] => end_date\n                                            [Account Primary Reseller Name] => account_primary_reseller_name\n                                            [Order PO Number] => order_po_number\n                                            [Order Shipping Address] => order_shipping_address\n                                            [Order Shipping City] => order_shipping_city\n                                            [Order Shipping State/Province] => order_shipping_state_province\n                                            [Order Shipping Country] => order_shipping_country\n                                        )\n\n                                    [data_types] => Array\n                                        (\n                                            [serial_number] => string\n                                            [name] => string\n                                            [contract] => string\n                                            [product_name] => string\n                                            [quantity] => string\n                                            [end_date] => string\n                                            [account_primary_reseller_name] => string\n                                            [order_po_number] => string\n                                            [order_shipping_address] => string\n                                            [order_shipping_city] => string\n                                            [order_shipping_state_province] => string\n                                            [order_shipping_country] => string\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n            [data_source] => csv\n            [route_key] => \n            [description] => Auto-generated table from CSV import (489 rows, 100 analyzed)\n            [created_at] => 2025-08-12 08:54:40\n            [updated_at] => 2025-08-12 08:54:40\n        )\n\n    [table_name] => autobooks_import_bluebeam_data\n    [data_source_id] => \n)\n
[data_table_saga] [2025-08-12 08:54:43] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:54:43] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:54:43] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:54:45] [data-table.edge.php:49]  Auto-loading data: table=autobooks_import_bluebeam_data, success=true, source=hardcoded, count=0
[data_table_saga] [2025-08-12 08:54:45] [data-table.edge.php:146] Data table template: table=autobooks_import_bluebeam_data, type=, id=
[data_table_saga] [2025-08-12 08:54:45] [data-table-column-manager.edge.php:46]  Column manager props: table=autobooks_import_bluebeam_data, type=, id=
