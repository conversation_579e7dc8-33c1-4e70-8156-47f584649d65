[data_source_manager] [2025-08-10 13:25:55] [data_source_manager.class.php:978] Previewing multi-table merge for 2 tables using unified_field_mapper method
[data_source_manager] [2025-08-10 13:25:55] [data_source_manager.class.php:115] Getting sample data for table: autobooks_import_bluebeam_data (limit: 5)
[data_source_manager] [2025-08-10 13:25:55] [data_source_manager.class.php:120] Sample data for autobooks_import_bluebeam_data: 5 rows returned
[data_source_manager] [2025-08-10 13:31:32] [data_source_manager.class.php:974] Previewing multi-table merge for 2 tables using unified_field_mapper method
[data_source_manager] [2025-08-10 13:35:51] [data_source_manager.class.php:974] Previewing multi-table merge for 2 tables using unified_field_mapper method
[data_source_manager] [2025-08-10 13:38:39] [data_source_manager.class.php:974] Previewing multi-table merge for 2 tables using unified_field_mapper method
[data_source_manager] [2025-08-10 13:39:07] [data_source_manager.class.php:974] Previewing multi-table merge for 2 tables using unified_field_mapper method
[data_source_manager] [2025-08-10 13:40:46] [data_source_manager.class.php:974] Previewing multi-table merge for 2 tables using unified_field_mapper method
[data_source_manager] [2025-08-10 13:41:31] [data_source_manager.class.php:974] Previewing multi-table merge for 3 tables using unified_field_mapper method
[data_source_manager] [2025-08-10 13:46:56] [data_source_manager.class.php:236] Created data source: test12 with ID: 38
[data_source_manager] [2025-08-10 13:47:03] [data_source_manager.class.php:485] Executing query: SELECT * FROM `autobooks_import_bluebeam_data` LIMIT 50
[data_source_manager] [2025-08-12 08:38:38] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:38:42] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:39:07] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:39:10] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:43:46] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:45:42] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:45:45] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:53:50] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:53:57] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:54:43] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:54:45] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 08:59:00] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:01:08] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:01:10] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:09:12] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:09:14] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:19:33] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:20:02] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:22:14] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:22:39] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:22:41] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:23:55] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:23:58] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:42:25] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:42:28] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:42:31] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
[data_source_manager] [2025-08-12 09:42:43] [data_source_manager.class.php:485] Executing query: SELECT `autodesk_accounts`.`id` AS `autodesk_accounts_id`, `autodesk_accounts`.`account_csn` AS `autodesk_accounts_account_csn`, `autodesk_accounts`.`name` AS `autodesk_accounts_name`, `autodesk_accounts`.`first_name` AS `autodesk_accounts_first_name`, `autodesk_accounts`.`last_name` AS `autodesk_accounts_last_name`, `autodesk_accounts`.`email` AS `autodesk_accounts_email`, `autodesk_accounts`.`phone` AS `autodesk_accounts_phone`, `autodesk_accounts`.`city` AS `autodesk_accounts_city`, `autodesk_accounts`.`postal_code` AS `autodesk_accounts_postal_code`, `autodesk_accounts`.`country` AS `autodesk_accounts_country`, `autodesk_accounts`.`account_type` AS `autodesk_accounts_account_type` FROM `autodesk_accounts` WHERE `autodesk_accounts`.`name` LIKE 'Ltd' AND `autodesk_accounts`.`country` = 'United Kingdom' AND `autodesk_accounts`.`account_type` = 'Customer' LIMIT 50
