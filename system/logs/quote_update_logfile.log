[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-10 23:00:31
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 09da6bff-9160-4d7c-ab53-17d80177529e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-927963\n            [transactionId] => bd0f7ed8-2410-5ee5-9049-1e36983df684\n            [quoteStatus] => Expired\n            [message] => Quote# Q-927963 status changed to Expired.\n            [modifiedAt] => 2025-08-10T23:00:17.413Z\n        )\n\n    [publishedAt] => 2025-08-10T23:00:29.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-927963', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-927963', quote_status = 'Expired';\n
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-927963', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-927963', quote_status = 'Expired';\n
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-927963', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-927963', quote_status = 'Expired';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-927963', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-927963', quote_status = 'Expired';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-10 23:00:31
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 09da6bff-9160-4d7c-ab53-17d80177529e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-927963\n            [transactionId] => bd0f7ed8-2410-5ee5-9049-1e36983df684\n            [quoteStatus] => Expired\n            [message] => Quote# Q-927963 status changed to Expired.\n            [modifiedAt] => 2025-08-10T23:00:17.413Z\n        )\n\n    [publishedAt] => 2025-08-10T23:00:29.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-927963', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-927963', quote_status = 'Expired';\n
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-927963', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-927963', quote_status = 'Expired';\n
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-927963', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-927963', quote_status = 'Expired';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-10 23:00:31] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-927963', quote_status = 'Expired' ON DUPLICATE KEY UPDATE quote_number = 'Q-927963', quote_status = 'Expired';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 07:56:06] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 07:56:06
[quote_update] [2025-08-11 07:56:06] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 07:56:06] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => be4da8b9-3dc0-4134-81f9-9dabcb23b456\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995194\n            [transactionId] => dc436df5-dc86-59b3-a97f-0ce678c1a44f\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995194 status changed to Draft.\n            [modifiedAt] => 2025-08-11T07:56:04.001Z\n        )\n\n    [publishedAt] => 2025-08-11T07:56:04.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 07:56:06] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 07:56:06
[quote_update] [2025-08-11 07:56:06] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 07:56:06] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => be4da8b9-3dc0-4134-81f9-9dabcb23b456\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995194\n            [transactionId] => dc436df5-dc86-59b3-a97f-0ce678c1a44f\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995194 status changed to Draft.\n            [modifiedAt] => 2025-08-11T07:56:04.001Z\n        )\n\n    [publishedAt] => 2025-08-11T07:56:04.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 07:56:08] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 07:56:08 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1597\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 1eda1827-691a-481a-8f72-9f451b346883\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIZD1GQHIAMEWNg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a218-7d0a5f8d1478f4de245cdd0b\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995194\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T08:56:02+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => ORANGE KEY Ltd\n                            [addressLine1] => 4 Oak View\n                            [addressLine2] => Bradwell Village\n                            [city] => Burford\n                            [stateProvinceCode] => \n                            [stateProvince] => BERKSHIRE\n                            [postalCode] => OX18 4XQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Graham\n                            [lastName] => Leftwich\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995194\n)\n
[quote_update] [2025-08-11 07:56:08] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 07:56:08 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1597\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 94ea241d-ec53-4d9e-b624-142c18c77436\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIZD3GZcIAMEToA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a218-6e890bd721d2d2b50c1ab9f2\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995194\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T08:56:02+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => ORANGE KEY Ltd\n                            [addressLine1] => 4 Oak View\n                            [addressLine2] => Bradwell Village\n                            [city] => Burford\n                            [stateProvinceCode] => \n                            [stateProvince] => BERKSHIRE\n                            [postalCode] => OX18 4XQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Graham\n                            [lastName] => Leftwich\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995194\n)\n
[quote_update] [2025-08-11 07:56:48] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 07:56:48
[quote_update] [2025-08-11 07:56:48] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 07:56:48] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 20af0ad7-6859-4a56-ac9f-12fbe5c205cf\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995197\n            [transactionId] => 1269a382-1607-5ebc-bcd0-c291b27d508e\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995197 status changed to Draft.\n            [modifiedAt] => 2025-08-11T07:56:46.431Z\n        )\n\n    [publishedAt] => 2025-08-11T07:56:46.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 07:56:49] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 07:56:49
[quote_update] [2025-08-11 07:56:49] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 07:56:49] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 20af0ad7-6859-4a56-ac9f-12fbe5c205cf\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995197\n            [transactionId] => 1269a382-1607-5ebc-bcd0-c291b27d508e\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995197 status changed to Draft.\n            [modifiedAt] => 2025-08-11T07:56:46.431Z\n        )\n\n    [publishedAt] => 2025-08-11T07:56:46.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 07:56:50] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 07:56:50 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3611\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => e73f2f69-6705-4e19-9288-5ce09c226aba\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIZKYHWzoAMETWw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a242-0e0aa5f71f1c878e39935c86\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995197\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T08:56:44+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 820\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 820\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 820\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => ORANGE KEY Ltd\n                            [addressLine1] => 4 Oak View\n                            [addressLine2] => Bradwell Village\n                            [city] => Burford\n                            [stateProvinceCode] => \n                            [stateProvince] => BERKSHIRE\n                            [postalCode] => OX18 4XQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Graham\n                            [lastName] => Leftwich\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-11\n                                    [endDate] => 2026-10-10\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 2\n                                    [subscription] => Array\n                                        (\n                                            [id] => 56951207788277\n                                            [quantity] => 2\n                                            [endDate] => 2025-10-10\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 820\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 820\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 820\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-10-11\n                                                            [endDate] => 2026-10-10\n                                                            [extendedSRP] => 820\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 820\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995197\n)\n
[quote_update] [2025-08-11 07:56:50] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 07:56:50 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3710\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => d2a6432f-ad70-485e-bc5d-83582f404a03\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIZKYGaFoAMEolw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a242-010864f8260f238904fa9e12\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995197\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T08:56:44+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 820\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 820\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 820\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => ORANGE KEY Ltd\n                            [addressLine1] => 4 Oak View\n                            [addressLine2] => Bradwell Village\n                            [city] => Burford\n                            [stateProvinceCode] => \n                            [stateProvince] => BERKSHIRE\n                            [postalCode] => OX18 4XQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Graham\n                            [lastName] => Leftwich\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-11\n                                    [endDate] => 2026-10-10\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 2\n                                    [subscription] => Array\n                                        (\n                                            [id] => 56951207788277\n                                            [quantity] => 2\n                                            [endDate] => 2025-10-10\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 820\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 820\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 820\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-10-11\n                                                            [endDate] => 2026-10-10\n                                                            [extendedSRP] => 820\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 820\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995197\n)\n
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 07:57:26
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d7b462e1-abc2-49eb-a256-fa18fee41ab6\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995197\n            [transactionId] => 1269a382-1607-5ebc-bcd0-c291b27d508e\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995197 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T07:57:24.036Z\n        )\n\n    [publishedAt] => 2025-08-11T07:57:24.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995197', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995197', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995197', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995197', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995197', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995197', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995197', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995197', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 07:57:26
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d7b462e1-abc2-49eb-a256-fa18fee41ab6\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995197\n            [transactionId] => 1269a382-1607-5ebc-bcd0-c291b27d508e\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995197 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T07:57:24.036Z\n        )\n\n    [publishedAt] => 2025-08-11T07:57:24.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995197', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995197', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995197', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995197', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995197', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995197', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 07:57:26] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995197', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995197', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 08:01:05] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:01:05
[quote_update] [2025-08-11 08:01:05] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:01:05] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 0ce8a5ba-1835-445a-a3ff-bdd44e9a523b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995208\n            [transactionId] => 17aa203f-ed8b-57a3-b7c6-412c48f51395\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995208 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:01:02.804Z\n        )\n\n    [publishedAt] => 2025-08-11T08:01:03.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:01:05] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:01:05
[quote_update] [2025-08-11 08:01:05] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:01:05] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 0ce8a5ba-1835-445a-a3ff-bdd44e9a523b\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995208\n            [transactionId] => 17aa203f-ed8b-57a3-b7c6-412c48f51395\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995208 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:01:02.804Z\n        )\n\n    [publishedAt] => 2025-08-11T08:01:03.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:01:07] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:01:07 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3670\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 26dee111-7ba7-4044-a702-1f76e36aed0f\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIZygHGXoAMEMjQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a342-46c213dd1fa24e701e46f7ac\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995208\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:01:00+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 3250\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 3250\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 3250\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Fortem Civil Engineering Consultants Ltd\n                            [addressLine1] => 11 The Covert\n                            [city] => York\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => YO24 1JN\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Ricky\n                            [lastName] => Dale\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-15\n                                    [endDate] => 2026-08-14\n                                    [offeringId] => OD-000027\n                                    [offeringCode] => ACDIST\n                                    [offeringName] => AutoCAD - including specialized toolsets\n                                    [marketingName] => AutoCAD - including specialized toolsets\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 2\n                                    [subscription] => Array\n                                        (\n                                            [id] => 62982734356654\n                                            [quantity] => 2\n                                            [endDate] => 2025-08-14\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 1625\n                                            [extendedSRP] => 3250\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 3250\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 3250\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-08-15\n                                                            [endDate] => 2026-08-14\n                                                            [extendedSRP] => 3250\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 3250\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995208\n)\n
[quote_update] [2025-08-11 08:01:08] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:01:07 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3670\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 56e69ab5-26b4-457f-8f1a-8eb0f1bd4f08\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIZyhHEmoAMElIA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a342-347ba3cf1b66c47004530022\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995208\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:01:00+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 3250\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 3250\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 3250\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Fortem Civil Engineering Consultants Ltd\n                            [addressLine1] => 11 The Covert\n                            [city] => York\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => YO24 1JN\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Ricky\n                            [lastName] => Dale\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-15\n                                    [endDate] => 2026-08-14\n                                    [offeringId] => OD-000027\n                                    [offeringCode] => ACDIST\n                                    [offeringName] => AutoCAD - including specialized toolsets\n                                    [marketingName] => AutoCAD - including specialized toolsets\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 2\n                                    [subscription] => Array\n                                        (\n                                            [id] => 62982734356654\n                                            [quantity] => 2\n                                            [endDate] => 2025-08-14\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 1625\n                                            [extendedSRP] => 3250\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 3250\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 3250\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-08-15\n                                                            [endDate] => 2026-08-14\n                                                            [extendedSRP] => 3250\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 3250\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995208\n)\n
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:01:59
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 6c38f88f-27ed-44f3-b7e8-07b4b2c03b42\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995208\n            [transactionId] => 17aa203f-ed8b-57a3-b7c6-412c48f51395\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995208 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:01:57.095Z\n        )\n\n    [publishedAt] => 2025-08-11T08:01:57.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995208', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995208', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995208', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995208', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995208', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995208', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 08:01:59] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995208', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995208', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:02:00
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 6c38f88f-27ed-44f3-b7e8-07b4b2c03b42\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995208\n            [transactionId] => 17aa203f-ed8b-57a3-b7c6-412c48f51395\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995208 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:01:57.095Z\n        )\n\n    [publishedAt] => 2025-08-11T08:01:57.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995208', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995208', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995208', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995208', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995208', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995208', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 08:02:00] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995208', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995208', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 08:04:39] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:04:39
[quote_update] [2025-08-11 08:04:39] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:04:39] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => f4f88649-a9b5-419f-bb3c-9bbbe4a28e53\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995212\n            [transactionId] => dad4b5a5-084e-5c89-aee9-d4b6ae0fb016\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995212 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:04:36.810Z\n        )\n\n    [publishedAt] => 2025-08-11T08:04:37.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:04:39] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:04:39
[quote_update] [2025-08-11 08:04:39] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:04:39] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => f4f88649-a9b5-419f-bb3c-9bbbe4a28e53\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995212\n            [transactionId] => dad4b5a5-084e-5c89-aee9-d4b6ae0fb016\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995212 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:04:36.810Z\n        )\n\n    [publishedAt] => 2025-08-11T08:04:37.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:04:41] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:04:40 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3637\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => c26fd571-c351-4975-8c0e-12abdccae4d2\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIaT3GuBIAMEF1w=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a418-58debfd73800cfc2279818f2\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995212\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:04:34+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => WJS UK Ltd\n                            [addressLine1] => Unit 3, Moat House Square\n                            [addressLine2] => Thorp Arch Estate\n                            [city] => Wetherby\n                            [stateProvinceCode] => \n                            [stateProvince] => WEST YORKSHIRE\n                            [postalCode] => LS23 7FB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => william\n                            [lastName] => scarlett\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-07\n                                    [endDate] => 2026-08-06\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 59679767635256\n                                            [quantity] => 1\n                                            [endDate] => 2025-08-06\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-08-07\n                                                            [endDate] => 2026-08-06\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995212\n)\n
[quote_update] [2025-08-11 08:04:41] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:04:41 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3736\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 359d987c-c939-4bcc-b121-e11ae542551e\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIaT6F0iIAMEgIA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a418-14b4414319025c232a8651cd\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995212\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:04:34+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => WJS UK Ltd\n                            [addressLine1] => Unit 3, Moat House Square\n                            [addressLine2] => Thorp Arch Estate\n                            [city] => Wetherby\n                            [stateProvinceCode] => \n                            [stateProvince] => WEST YORKSHIRE\n                            [postalCode] => LS23 7FB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => william\n                            [lastName] => scarlett\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-07\n                                    [endDate] => 2026-08-06\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 59679767635256\n                                            [quantity] => 1\n                                            [endDate] => 2025-08-06\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-08-07\n                                                            [endDate] => 2026-08-06\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995212\n)\n
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:05:14
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 902a2482-bda7-471f-8c04-f751749d3382\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995212\n            [transactionId] => dad4b5a5-084e-5c89-aee9-d4b6ae0fb016\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995212 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:05:11.844Z\n        )\n\n    [publishedAt] => 2025-08-11T08:05:12.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:05:14
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 902a2482-bda7-471f-8c04-f751749d3382\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995212\n            [transactionId] => dad4b5a5-084e-5c89-aee9-d4b6ae0fb016\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995212 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:05:11.844Z\n        )\n\n    [publishedAt] => 2025-08-11T08:05:12.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 08:05:14] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995212', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995212', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 08:06:21] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:06:21
[quote_update] [2025-08-11 08:06:21] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:06:21] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 77b18d9a-2a4a-4d74-8949-d0e4f1a70bc1\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995217\n            [transactionId] => 64c6354e-4b3f-546c-b756-545ded8d2aec\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995217 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:06:18.507Z\n        )\n\n    [publishedAt] => 2025-08-11T08:06:18.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:06:21] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:06:21
[quote_update] [2025-08-11 08:06:21] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:06:21] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 77b18d9a-2a4a-4d74-8949-d0e4f1a70bc1\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995217\n            [transactionId] => 64c6354e-4b3f-546c-b756-545ded8d2aec\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995217 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:06:18.507Z\n        )\n\n    [publishedAt] => 2025-08-11T08:06:18.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:06:22] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:06:22 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3652\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 51b12609-e028-4a41-a4b6-576de8474a16\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIajxEsToAMEdIA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a47e-198ff9fe77542b500f96c83f\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995217\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:06:14+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Miller Industrial Cladding Ltd\n                            [addressLine1] => 5 Keepers Lane, via Wergs Drive (of\n                            [addressLine2] => f A41)\n                            [city] => Wolverhampton\n                            [stateProvinceCode] => \n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => WV6 8UA\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Christian\n                            [lastName] => Collett\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-15\n                                    [endDate] => 2026-08-14\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 72371771791527\n                                            [quantity] => 1\n                                            [endDate] => 2025-08-14\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-08-15\n                                                            [endDate] => 2026-08-14\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995217\n)\n
[quote_update] [2025-08-11 08:06:22] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:06:22 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3652\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 5d5e7bf7-65db-4467-bff4-66b9c4c17ae6\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIajzFrpIAMEo-g=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a47e-37255da614f308306cb2c191\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995217\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:06:14+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Miller Industrial Cladding Ltd\n                            [addressLine1] => 5 Keepers Lane, via Wergs Drive (of\n                            [addressLine2] => f A41)\n                            [city] => Wolverhampton\n                            [stateProvinceCode] => \n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => WV6 8UA\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Christian\n                            [lastName] => Collett\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-15\n                                    [endDate] => 2026-08-14\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 72371771791527\n                                            [quantity] => 1\n                                            [endDate] => 2025-08-14\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-08-15\n                                                            [endDate] => 2026-08-14\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995217\n)\n
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:06:58
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 968e73e8-961f-45c5-943c-3003d508b681\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995217\n            [transactionId] => 64c6354e-4b3f-546c-b756-545ded8d2aec\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995217 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:06:55.947Z\n        )\n\n    [publishedAt] => 2025-08-11T08:06:56.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995217', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995217', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995217', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995217', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995217', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995217', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995217', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995217', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:06:58
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 968e73e8-961f-45c5-943c-3003d508b681\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995217\n            [transactionId] => 64c6354e-4b3f-546c-b756-545ded8d2aec\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995217 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:06:55.947Z\n        )\n\n    [publishedAt] => 2025-08-11T08:06:56.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995217', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995217', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995217', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995217', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995217', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995217', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 08:06:58] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995217', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995217', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 08:07:48] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:07:48
[quote_update] [2025-08-11 08:07:48] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:07:48] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 559abccc-b535-4a0c-b262-420825853211\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995219\n            [transactionId] => 3b431047-3f8b-5095-93f1-71549975113b\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995219 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:07:45.743Z\n        )\n\n    [publishedAt] => 2025-08-11T08:07:46.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:07:48] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:07:48
[quote_update] [2025-08-11 08:07:48] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:07:48] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 559abccc-b535-4a0c-b262-420825853211\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995219\n            [transactionId] => 3b431047-3f8b-5095-93f1-71549975113b\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995219 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:07:45.743Z\n        )\n\n    [publishedAt] => 2025-08-11T08:07:46.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:07:50] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:07:50 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3582\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => d75dd6de-972d-4d09-a39c-7426944dabaf\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIaxaHnBIAMEFuw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a4d5-2dbf79da232a16dc4aea0e51\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995219\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:07:43+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Jonathan Carter Design\n                            [addressLine1] => 70 Tressillian Road\n                            [city] => London\n                            [stateProvinceCode] => \n                            [stateProvince] => LONDON\n                            [postalCode] => SE4 1YD\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Jonathan\n                            [lastName] => Carter\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-15\n                                    [endDate] => 2026-09-14\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 69477960142838\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-14\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-09-15\n                                                            [endDate] => 2026-09-14\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995219\n)\n
[quote_update] [2025-08-11 08:07:50] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:07:50 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3681\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => dc2e2a3d-23e2-408a-a160-15116939180f\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIaxcHNoIAMENEQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a4d5-08c9fe4a2a92eb017a8cd965\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995219\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:07:43+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Jonathan Carter Design\n                            [addressLine1] => 70 Tressillian Road\n                            [city] => London\n                            [stateProvinceCode] => \n                            [stateProvince] => LONDON\n                            [postalCode] => SE4 1YD\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Jonathan\n                            [lastName] => Carter\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-15\n                                    [endDate] => 2026-09-14\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 69477960142838\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-14\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-09-15\n                                                            [endDate] => 2026-09-14\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995219\n)\n
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:10:07
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d4a8e751-a533-4534-96af-6a17d0c46c8a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995219\n            [transactionId] => 3b431047-3f8b-5095-93f1-71549975113b\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995219 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:10:04.801Z\n        )\n\n    [publishedAt] => 2025-08-11T08:10:05.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:10:07
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => d4a8e751-a533-4534-96af-6a17d0c46c8a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995219\n            [transactionId] => 3b431047-3f8b-5095-93f1-71549975113b\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995219 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:10:04.801Z\n        )\n\n    [publishedAt] => 2025-08-11T08:10:05.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 08:10:07] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995219', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995219', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 08:17:35] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:17:35
[quote_update] [2025-08-11 08:17:35] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:17:35] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 7c7f36e4-479a-450d-8c87-324a26b51116\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995260\n            [transactionId] => b4a3e69d-df50-52b5-8bc0-878e76068777\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995260 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:17:32.878Z\n        )\n\n    [publishedAt] => 2025-08-11T08:17:33.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:17:35] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:17:35
[quote_update] [2025-08-11 08:17:35] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:17:35] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 7c7f36e4-479a-450d-8c87-324a26b51116\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995260\n            [transactionId] => b4a3e69d-df50-52b5-8bc0-878e76068777\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995260 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:17:32.878Z\n        )\n\n    [publishedAt] => 2025-08-11T08:17:33.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:17:37] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:17:37 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3607\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 0a8ed832-814b-4f80-abd9-b4e67697593c\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIcNMH1coAMEG5Q=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a720-2f3318e035c6f9075f20f76f\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995260\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:17:30+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 530\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 530\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 530\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => POQ Ltd\n                            [addressLine1] => 172 High Road\n                            [addressLine2] => Bushey Heath\n                            [city] => Bushey\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => WD23 1NP\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Sanjay\n                            [lastName] => Odedra\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-17\n                                    [endDate] => 2026-08-16\n                                    [offeringId] => OD-000280\n                                    [offeringCode] => RVTLTS\n                                    [offeringName] => AutoCAD Revit LT Suite\n                                    [marketingName] => AutoCAD Revit LT Suite\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 69226925631631\n                                            [quantity] => 1\n                                            [endDate] => 2025-08-16\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 530\n                                            [extendedSRP] => 530\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 530\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 530\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-08-17\n                                                            [endDate] => 2026-08-16\n                                                            [extendedSRP] => 530\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 530\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995260\n)\n
[quote_update] [2025-08-11 08:17:37] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:17:37 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3607\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 7c7d168f-bed8-4b5e-acad-ec373baddca2\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIcNNHTqIAMEe1g=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a720-2d4e25bd3e601bae0034e24b\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995260\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:17:30+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 530\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 530\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 530\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => POQ Ltd\n                            [addressLine1] => 172 High Road\n                            [addressLine2] => Bushey Heath\n                            [city] => Bushey\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => WD23 1NP\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Sanjay\n                            [lastName] => Odedra\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-17\n                                    [endDate] => 2026-08-16\n                                    [offeringId] => OD-000280\n                                    [offeringCode] => RVTLTS\n                                    [offeringName] => AutoCAD Revit LT Suite\n                                    [marketingName] => AutoCAD Revit LT Suite\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 69226925631631\n                                            [quantity] => 1\n                                            [endDate] => 2025-08-16\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 530\n                                            [extendedSRP] => 530\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 530\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 530\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-08-17\n                                                            [endDate] => 2026-08-16\n                                                            [extendedSRP] => 530\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 530\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995260\n)\n
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:18:08
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2623a7e8-ee57-4768-9d42-78290efb0bef\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995260\n            [transactionId] => b4a3e69d-df50-52b5-8bc0-878e76068777\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995260 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:18:05.497Z\n        )\n\n    [publishedAt] => 2025-08-11T08:18:05.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:18:08
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2623a7e8-ee57-4768-9d42-78290efb0bef\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995260\n            [transactionId] => b4a3e69d-df50-52b5-8bc0-878e76068777\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995260 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:18:05.497Z\n        )\n\n    [publishedAt] => 2025-08-11T08:18:05.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 08:18:08] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 08:19:04] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:19:04
[quote_update] [2025-08-11 08:19:04] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:19:04] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8167a87e-598e-4975-9186-e48ac3e23d28\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995264\n            [transactionId] => 172f82ac-7f02-5e9c-a141-e3bfd6ac82db\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995264 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:19:01.993Z\n        )\n\n    [publishedAt] => 2025-08-11T08:19:02.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:19:04] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:19:04
[quote_update] [2025-08-11 08:19:04] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:19:04] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8167a87e-598e-4975-9186-e48ac3e23d28\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995264\n            [transactionId] => 172f82ac-7f02-5e9c-a141-e3bfd6ac82db\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995264 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:19:01.993Z\n        )\n\n    [publishedAt] => 2025-08-11T08:19:02.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:19:06] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:19:06 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3601\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 617b25c7-3dd3-43fe-ade3-6802fb51eee4\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIcbDEhGoAMEUJA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a779-208c7fed1f94a70c7b7b4381\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995264\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:19:00+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 1230\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 1230\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 1230\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Coast Consulting Engineers\n                            [addressLine1] => 11 New Quay\n                            [city] => North Shields\n                            [stateProvinceCode] => \n                            [stateProvince] => TYNE AND WEAR\n                            [postalCode] => NE29 6LQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Richard\n                            [lastName] => Hall\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-15\n                                    [endDate] => 2026-08-14\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 3\n                                    [subscription] => Array\n                                        (\n                                            [id] => 56586113101817\n                                            [quantity] => 3\n                                            [endDate] => 2025-08-14\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 1230\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 1230\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 1230\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-08-15\n                                                            [endDate] => 2026-08-14\n                                                            [extendedSRP] => 1230\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 1230\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995264\n)\n
[quote_update] [2025-08-11 08:19:06] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:19:06 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3601\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 25befd8a-26bc-47ff-ad38-5d67ab2ba67f\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIcbDGiWoAMEe4g=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a779-20d15e445a36b67928d49fe3\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995264\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:19:00+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 1230\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 1230\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 1230\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => Coast Consulting Engineers\n                            [addressLine1] => 11 New Quay\n                            [city] => North Shields\n                            [stateProvinceCode] => \n                            [stateProvince] => TYNE AND WEAR\n                            [postalCode] => NE29 6LQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Richard\n                            [lastName] => Hall\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-08-15\n                                    [endDate] => 2026-08-14\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 3\n                                    [subscription] => Array\n                                        (\n                                            [id] => 56586113101817\n                                            [quantity] => 3\n                                            [endDate] => 2025-08-14\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 1230\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 1230\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 1230\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-08-15\n                                                            [endDate] => 2026-08-14\n                                                            [extendedSRP] => 1230\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 1230\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995264\n)\n
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:19:51
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => e5ee71ec-2422-4d79-9dd5-fce0c6162ed2\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995264\n            [transactionId] => 172f82ac-7f02-5e9c-a141-e3bfd6ac82db\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995264 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:19:49.003Z\n        )\n\n    [publishedAt] => 2025-08-11T08:19:49.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995264', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995264', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995264', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995264', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995264', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995264', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995264', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995264', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:19:51
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => e5ee71ec-2422-4d79-9dd5-fce0c6162ed2\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995264\n            [transactionId] => 172f82ac-7f02-5e9c-a141-e3bfd6ac82db\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995264 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:19:49.003Z\n        )\n\n    [publishedAt] => 2025-08-11T08:19:49.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995264', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995264', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995264', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995264', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995264', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995264', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 08:19:51] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995264', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995264', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 08:20:33] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:20:33
[quote_update] [2025-08-11 08:20:33] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:20:33] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 94a752bb-5d27-4f48-998e-1bf67d375e9a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995272\n            [transactionId] => 436c791e-32d3-5356-b5df-180d98d0d04f\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995272 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:20:31.013Z\n        )\n\n    [publishedAt] => 2025-08-11T08:20:31.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:20:33] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:20:33
[quote_update] [2025-08-11 08:20:33] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:20:33] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 94a752bb-5d27-4f48-998e-1bf67d375e9a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995272\n            [transactionId] => 436c791e-32d3-5356-b5df-180d98d0d04f\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995272 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:20:31.013Z\n        )\n\n    [publishedAt] => 2025-08-11T08:20:31.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:20:34] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:20:34 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1638\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => cf32179b-9b60-4d3b-9064-99605831b69c\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIco7HOgIAMEv9Q=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a7d2-0e7684cc148b4b994b7c1b33\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995272\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:20:29+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => SeAH Wind Ltd\n                            [addressLine1] => Stephenson House High Force Road\n                            [addressLine2] => Riverside Park Industrial Estate\n                            [city] => Middlesbrough\n                            [stateProvinceCode] => \n                            [stateProvince] => CLEVELAND\n                            [postalCode] => TS2 1RH\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Caitlan\n                            [lastName] => Scorer\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995272\n)\n
[quote_update] [2025-08-11 08:20:35] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:20:35 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1638\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 4d088a0e-9b7d-4be1-a5d4-eb86ee082078\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIco_F-8oAMEQdA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899a7d2-685a07647fd756615ce00c17\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995272\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:20:29+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => SeAH Wind Ltd\n                            [addressLine1] => Stephenson House High Force Road\n                            [addressLine2] => Riverside Park Industrial Estate\n                            [city] => Middlesbrough\n                            [stateProvinceCode] => \n                            [stateProvince] => CLEVELAND\n                            [postalCode] => TS2 1RH\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Caitlan\n                            [lastName] => Scorer\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995272\n)\n
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:21:40
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 59477164-faf1-4cc1-b356-4e65a1273c28\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995272\n            [transactionId] => 436c791e-32d3-5356-b5df-180d98d0d04f\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995272 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:21:38.538Z\n        )\n\n    [publishedAt] => 2025-08-11T08:21:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995272', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995272', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995272', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995272', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995272', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995272', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 08:21:40] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995272', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995272', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:21:41
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 59477164-faf1-4cc1-b356-4e65a1273c28\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995272\n            [transactionId] => 436c791e-32d3-5356-b5df-180d98d0d04f\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995272 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:21:38.538Z\n        )\n\n    [publishedAt] => 2025-08-11T08:21:38.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995272', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995272', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995272', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995272', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995272', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995272', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 08:21:41] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995272', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995272', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 08:48:43] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:48:43
[quote_update] [2025-08-11 08:48:43] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:48:43] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 9d09a821-c7fc-4b76-8852-3eca4670b6e3\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995368\n            [transactionId] => 1d862d2f-3516-502d-883f-4c3c85b12e2c\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995368 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:48:41.182Z\n        )\n\n    [publishedAt] => 2025-08-11T08:48:41.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:48:43] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:48:43
[quote_update] [2025-08-11 08:48:43] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:48:43] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 9d09a821-c7fc-4b76-8852-3eca4670b6e3\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995368\n            [transactionId] => 1d862d2f-3516-502d-883f-4c3c85b12e2c\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995368 status changed to Draft.\n            [modifiedAt] => 2025-08-11T08:48:41.182Z\n        )\n\n    [publishedAt] => 2025-08-11T08:48:41.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:48:45] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:48:45 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1620\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 0aea4540-652d-4de0-bbea-f2f843c062c0\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIgxFFUHIAMECew=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899ae6c-21e427b35479f839270f57ea\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995368\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:48:39+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Aurangzaib\n                            [lastName] => Mahmood\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => SHIRE GROUP BSC Ltd\n                            [addressLine1] => Grosvenor House 11 St. Pauls Square\n                            [city] => Birmingham\n                            [stateProvinceCode] => \n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => B3 1RB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => David\n                            [lastName] => Woods\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995368\n)\n
[quote_update] [2025-08-11 08:48:45] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 08:48:45 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1620\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 756a49ff-0bff-4d88-8491-8288cf784fbb\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIgxFGd6oAMEhNA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899ae6c-6e0ec404065ddca96187ae87\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995368\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T09:48:39+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Aurangzaib\n                            [lastName] => Mahmood\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => SHIRE GROUP BSC Ltd\n                            [addressLine1] => Grosvenor House 11 St. Pauls Square\n                            [city] => Birmingham\n                            [stateProvinceCode] => \n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => B3 1RB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => David\n                            [lastName] => Woods\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995368\n)\n
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:58:08
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2c0de1e2-6868-4960-bdf5-075d7b312023\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995368\n            [transactionId] => 1d862d2f-3516-502d-883f-4c3c85b12e2c\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995368 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:58:05.586Z\n        )\n\n    [publishedAt] => 2025-08-11T08:58:05.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995368', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995368', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 08:58:08
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 2c0de1e2-6868-4960-bdf5-075d7b312023\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995368\n            [transactionId] => 1d862d2f-3516-502d-883f-4c3c85b12e2c\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995368 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T08:58:05.586Z\n        )\n\n    [publishedAt] => 2025-08-11T08:58:05.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995368', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995368', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995368', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995368', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995368', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995368', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995368', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995368', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995368', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995368', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995368', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995368', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 08:58:08] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995368', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995368', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 09:22:47] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 09:22:47
[quote_update] [2025-08-11 09:22:47] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 09:22:47] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 54f625f3-4e54-49dc-a2be-fd7958dd5d97\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995460\n            [transactionId] => c0894c4c-dae9-5350-af28-2d5f6272ab73\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995460 status changed to Draft.\n            [modifiedAt] => 2025-08-11T09:22:44.591Z\n        )\n\n    [publishedAt] => 2025-08-11T09:22:45.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 09:22:47] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 09:22:47
[quote_update] [2025-08-11 09:22:47] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 09:22:47] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 54f625f3-4e54-49dc-a2be-fd7958dd5d97\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995460\n            [transactionId] => c0894c4c-dae9-5350-af28-2d5f6272ab73\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995460 status changed to Draft.\n            [modifiedAt] => 2025-08-11T09:22:44.591Z\n        )\n\n    [publishedAt] => 2025-08-11T09:22:45.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 09:22:49] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 09:22:49 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 4ed7ffc5-cbd9-44aa-adf2-c684a9dd3778\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIlwZHgdIAMEnow=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899b668-12c35389184770806d7dfff4\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995460\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T10:22:42+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => SHIRE GROUP BSC Ltd\n                            [addressLine1] => Grosvenor House 11 St. Pauls Square\n                            [city] => Birmingham\n                            [stateProvinceCode] => \n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => B3 1RB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => David\n                            [lastName] => Woods\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-13\n                                    [endDate] => 2026-09-12\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 69460117928610\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-12\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-09-13\n                                                            [endDate] => 2026-09-12\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995460\n)\n
[quote_update] [2025-08-11 09:22:49] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 09:22:49 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => aa141aad-68de-459a-9e55-095a5d1c4374\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIlwZF0PIAMEMjQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899b668-133085746be7a098547f44ad\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995460\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T10:22:42+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 0\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 410\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => SHIRE GROUP BSC Ltd\n                            [addressLine1] => Grosvenor House 11 St. Pauls Square\n                            [city] => Birmingham\n                            [stateProvinceCode] => \n                            [stateProvince] => WEST MIDLANDS\n                            [postalCode] => B3 1RB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => David\n                            [lastName] => Woods\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-09-13\n                                    [endDate] => 2026-09-12\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 69460117928610\n                                            [quantity] => 1\n                                            [endDate] => 2025-09-12\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-09-13\n                                                            [endDate] => 2026-09-12\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995460\n)\n
[quote_update] [2025-08-11 10:51:20] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 10:51:20
[quote_update] [2025-08-11 10:51:20] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 10:51:20] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 93c40c42-94f5-469a-8f28-01484c9bcac9\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995659\n            [transactionId] => 68e0f623-77f1-5c14-b23d-9730baa61934\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995659 status changed to Draft.\n            [modifiedAt] => 2025-08-11T10:51:17.778Z\n        )\n\n    [publishedAt] => 2025-08-11T10:51:18.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 10:51:20] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 10:51:20
[quote_update] [2025-08-11 10:51:20] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 10:51:20] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 93c40c42-94f5-469a-8f28-01484c9bcac9\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995659\n            [transactionId] => 68e0f623-77f1-5c14-b23d-9730baa61934\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995659 status changed to Draft.\n            [modifiedAt] => 2025-08-11T10:51:17.778Z\n        )\n\n    [publishedAt] => 2025-08-11T10:51:18.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 10:51:22] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 10:51:21 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1594\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => bd9ded1d-54d6-4b7c-899d-c3bb77200592\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIyuiFFhIAMEu5w=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899cb29-17fc8d1a576c098e132a37ca\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995659\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T11:51:16+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => NEIL DOWLMAN ARCHITECTURE Ltd\n                            [addressLine1] => 14 Main Ridge West\n                            [city] => Boston\n                            [stateProvinceCode] => \n                            [stateProvince] => LINCOLNSHIRE\n                            [postalCode] => PE21 6QQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Neil\n                            [lastName] => Dowlman\n                            [phone] => +*********\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995659\n)\n
[quote_update] [2025-08-11 10:51:22] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 10:51:22 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1594\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 2850f118-4e11-47f2-8e31-3e5a632c86e1\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIyujFdbIAMENfA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899cb29-4ae56c667d792a7567bad77a\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995659\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T11:51:16+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => NEIL DOWLMAN ARCHITECTURE Ltd\n                            [addressLine1] => 14 Main Ridge West\n                            [city] => Boston\n                            [stateProvinceCode] => \n                            [stateProvince] => LINCOLNSHIRE\n                            [postalCode] => PE21 6QQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Neil\n                            [lastName] => Dowlman\n                            [phone] => +*********\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995659\n)\n
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 10:52:29
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8565ffac-3133-4d5a-817c-6c75b0ee2f17\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995659\n            [transactionId] => 68e0f623-77f1-5c14-b23d-9730baa61934\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995659 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T10:52:26.745Z\n        )\n\n    [publishedAt] => 2025-08-11T10:52:27.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995659', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995659', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995659', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995659', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995659', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995659', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995659', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995659', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 10:52:29
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8565ffac-3133-4d5a-817c-6c75b0ee2f17\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995659\n            [transactionId] => 68e0f623-77f1-5c14-b23d-9730baa61934\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995659 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T10:52:26.745Z\n        )\n\n    [publishedAt] => 2025-08-11T10:52:27.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995659', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995659', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995659', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995659', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995659', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995659', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 10:52:29] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995659', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995659', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 10:52:48] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 10:52:48
[quote_update] [2025-08-11 10:52:48] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 10:52:48] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 9988795c-fa02-4e5d-9514-7b64356b75e8\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995669\n            [transactionId] => dec5934c-7e7e-5c66-aa46-f25fb3c6a9a3\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995669 status changed to Draft.\n            [modifiedAt] => 2025-08-11T10:52:45.676Z\n        )\n\n    [publishedAt] => 2025-08-11T10:52:46.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 10:52:48] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 10:52:48
[quote_update] [2025-08-11 10:52:48] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 10:52:48] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 9988795c-fa02-4e5d-9514-7b64356b75e8\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995669\n            [transactionId] => dec5934c-7e7e-5c66-aa46-f25fb3c6a9a3\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995669 status changed to Draft.\n            [modifiedAt] => 2025-08-11T10:52:45.676Z\n        )\n\n    [publishedAt] => 2025-08-11T10:52:46.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 10:52:50] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 10:52:49 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1594\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => c2104251-9aa9-4a6d-9242-6044404ea3e2\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIy8TEaNIAMEDEQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899cb81-4d9157f873380d5e0fe8bd61\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995669\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T11:52:44+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => NEIL DOWLMAN ARCHITECTURE Ltd\n                            [addressLine1] => 14 Main Ridge West\n                            [city] => Boston\n                            [stateProvinceCode] => \n                            [stateProvince] => LINCOLNSHIRE\n                            [postalCode] => PE21 6QQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Neil\n                            [lastName] => Dowlman\n                            [phone] => +*********\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995669\n)\n
[quote_update] [2025-08-11 10:52:50] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 10:52:50 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1594\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 03e40684-07e2-459b-b4b5-afc401aa53eb\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PIy8TEoKoAMElgQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899cb81-56b23dd45658e4b244ede50b\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995669\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T11:52:44+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => NEIL DOWLMAN ARCHITECTURE Ltd\n                            [addressLine1] => 14 Main Ridge West\n                            [city] => Boston\n                            [stateProvinceCode] => \n                            [stateProvince] => LINCOLNSHIRE\n                            [postalCode] => PE21 6QQ\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Neil\n                            [lastName] => Dowlman\n                            [phone] => +*********\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995669\n)\n
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 10:53:58
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8a93599c-dab4-4d5b-8cf0-489327029379\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995669\n            [transactionId] => dec5934c-7e7e-5c66-aa46-f25fb3c6a9a3\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995669 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T10:53:55.588Z\n        )\n\n    [publishedAt] => 2025-08-11T10:53:56.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995669', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995669', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995669', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995669', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995669', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995669', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995669', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995669', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 10:53:58
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 8a93599c-dab4-4d5b-8cf0-489327029379\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995669\n            [transactionId] => dec5934c-7e7e-5c66-aa46-f25fb3c6a9a3\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-995669 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T10:53:55.588Z\n        )\n\n    [publishedAt] => 2025-08-11T10:53:56.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995669', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995669', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995669', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995669', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995669', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995669', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 10:53:58] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995669', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995669', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 12:03:49] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 12:03:49
[quote_update] [2025-08-11 12:03:49] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 12:03:49] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 23c27420-10ed-4305-aa38-589151379e18\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995862\n            [transactionId] => 1d338cb2-a9f8-5a9c-a396-eda17798a6a2\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995862 status changed to Draft.\n            [modifiedAt] => 2025-08-11T12:03:46.999Z\n        )\n\n    [publishedAt] => 2025-08-11T12:03:47.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 12:03:49] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 12:03:49
[quote_update] [2025-08-11 12:03:49] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 12:03:49] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 23c27420-10ed-4305-aa38-589151379e18\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995862\n            [transactionId] => 1d338cb2-a9f8-5a9c-a396-eda17798a6a2\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995862 status changed to Draft.\n            [modifiedAt] => 2025-08-11T12:03:46.999Z\n        )\n\n    [publishedAt] => 2025-08-11T12:03:47.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 12:03:51] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 12:03:51 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 9da4bc79-06ee-4379-af01-1aa75b06c5ec\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PI9WGHrsIAMEuBg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899dc26-279a3b181ae95e19183981c1\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995862\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T13:03:45+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Aurangzaib\n                            [lastName] => Mahmood\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => AIR HANDLING SYSTEMS\n                            [addressLine1] => Unit 3-5 Furnace Industrial Estate\n                            [city] => Shildon\n                            [stateProvinceCode] => \n                            [stateProvince] => COUNTY DURHAM\n                            [postalCode] => DL4 1QB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jonathan\n                            [lastName] => Darby\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995862\n)\n
[quote_update] [2025-08-11 12:03:51] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 12:03:51 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1615\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 0cc754e6-3653-47bc-bcd7-fa6efa4d032d\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PI9WGGQXIAMEQHQ=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899dc26-75b60a9356d291d07b1ca681\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995862\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T13:03:45+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Aurangzaib\n                            [lastName] => Mahmood\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => AIR HANDLING SYSTEMS\n                            [addressLine1] => Unit 3-5 Furnace Industrial Estate\n                            [city] => Shildon\n                            [stateProvinceCode] => \n                            [stateProvince] => COUNTY DURHAM\n                            [postalCode] => DL4 1QB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jonathan\n                            [lastName] => Darby\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995862\n)\n
[quote_update] [2025-08-11 12:13:54] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 12:13:54
[quote_update] [2025-08-11 12:13:54] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 12:13:54] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 881e625a-18cf-44aa-be63-5627b13ae50a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995894\n            [transactionId] => 2f51d254-9ac5-5f87-85c5-b3477a6b0207\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995894 status changed to Draft.\n            [modifiedAt] => 2025-08-11T12:13:51.697Z\n        )\n\n    [publishedAt] => 2025-08-11T12:13:52.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 12:13:54] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 12:13:54
[quote_update] [2025-08-11 12:13:54] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 12:13:54] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 881e625a-18cf-44aa-be63-5627b13ae50a\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995894\n            [transactionId] => 2f51d254-9ac5-5f87-85c5-b3477a6b0207\n            [quoteStatus] => Draft\n            [message] => Quote# Q-995894 status changed to Draft.\n            [modifiedAt] => 2025-08-11T12:13:51.697Z\n        )\n\n    [publishedAt] => 2025-08-11T12:13:52.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 12:13:56] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 12:13:56 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1596\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 108815a5-a899-4859-bdb2-03596f5b049d\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PI-0oFOroAMEuqg=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899de83-7b7bd81e72088a150e0c4e26\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995894\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T13:13:50+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => AIR HANDLING SYSTEMS\n                            [addressLine1] => Unit 3-5 Furnace Industrial Estate\n                            [city] => Shildon\n                            [stateProvinceCode] => \n                            [stateProvince] => COUNTY DURHAM\n                            [postalCode] => DL4 1QB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jonathan\n                            [lastName] => Darby\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995894\n)\n
[quote_update] [2025-08-11 12:13:56] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 12:13:56 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1596\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 8b67eebf-bc0e-4909-b05a-75467f582b34\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PI-0pGQNoAMEUJA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899de83-6124536773aa667839f59aa1\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-995894\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T13:13:50+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => AIR HANDLING SYSTEMS\n                            [addressLine1] => Unit 3-5 Furnace Industrial Estate\n                            [city] => Shildon\n                            [stateProvinceCode] => \n                            [stateProvince] => COUNTY DURHAM\n                            [postalCode] => DL4 1QB\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => *********\n                            [email] => <EMAIL>\n                            [firstName] => Jonathan\n                            [lastName] => Darby\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-995894\n)\n
[quote_update] [2025-08-11 13:01:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 13:01:51
[quote_update] [2025-08-11 13:01:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 13:01:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ff02652a-3401-49b4-a10e-47e6f15a978e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-996066\n            [transactionId] => e37097b2-c81f-5cb4-8a01-f6a123f3cc5e\n            [quoteStatus] => Draft\n            [message] => Quote# Q-996066 status changed to Draft.\n            [modifiedAt] => 2025-08-11T13:01:48.954Z\n        )\n\n    [publishedAt] => 2025-08-11T13:01:49.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 13:01:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 13:01:51
[quote_update] [2025-08-11 13:01:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 13:01:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => ff02652a-3401-49b4-a10e-47e6f15a978e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-996066\n            [transactionId] => e37097b2-c81f-5cb4-8a01-f6a123f3cc5e\n            [quoteStatus] => Draft\n            [message] => Quote# Q-996066 status changed to Draft.\n            [modifiedAt] => 2025-08-11T13:01:48.954Z\n        )\n\n    [publishedAt] => 2025-08-11T13:01:49.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 13:01:53] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 13:01:53 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1584\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => d65a681d-467f-4e1a-b70f-a09c716e5e63\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PJF2JFpToAMERsw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899e9c0-1a8f45967e28c6554860bd3c\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-996066\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T14:01:47+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => AZUMI Ltd\n                            [addressLine1] => Kings Court 2-16 Goodge Street\n                            [city] => London\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => W1T 2QA\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Jacob\n                            [lastName] => Wojciechowski\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-996066\n)\n
[quote_update] [2025-08-11 13:01:53] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 13:01:53 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 1584\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 4e78420b-df65-4f50-a0b7-97bfc768a578\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PJF2JFNooAMERQw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-6899e9c0-4cf0b40e2e4b12dc76ca3286\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-996066\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T14:01:47+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 0\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 0\n                            [estimatedTax] => \n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 0\n                            [currency] => GBP\n                            [priceRegionCode] => \n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => AZUMI Ltd\n                            [addressLine1] => Kings Court 2-16 Goodge Street\n                            [city] => London\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => W1T 2QA\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Jacob\n                            [lastName] => Wojciechowski\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-996066\n)\n
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 13:03:02
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 20fc231d-f902-45c4-a735-bf8540c828c1\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-996066\n            [transactionId] => e37097b2-c81f-5cb4-8a01-f6a123f3cc5e\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-996066 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T13:03:00.153Z\n        )\n\n    [publishedAt] => 2025-08-11T13:03:00.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-996066', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996066', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-996066', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996066', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-996066', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996066', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-996066', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996066', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 13:03:02
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 20fc231d-f902-45c4-a735-bf8540c828c1\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-996066\n            [transactionId] => e37097b2-c81f-5cb4-8a01-f6a123f3cc5e\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-996066 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T13:03:00.153Z\n        )\n\n    [publishedAt] => 2025-08-11T13:03:00.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-996066', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996066', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-996066', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996066', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-996066', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996066', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 13:03:02] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-996066', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996066', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 15:14:58] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 15:14:58
[quote_update] [2025-08-11 15:14:58] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 15:14:58] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => bd91c689-45f3-47b8-957a-38a7988bee13\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-996534\n            [transactionId] => 0ccab044-0a90-5bed-8b5b-d150018ff67c\n            [quoteStatus] => Draft\n            [message] => Quote# Q-996534 status changed to Draft.\n            [modifiedAt] => 2025-08-11T15:14:55.093Z\n        )\n\n    [publishedAt] => 2025-08-11T15:14:55.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 15:14:58] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 15:14:58
[quote_update] [2025-08-11 15:14:58] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 15:14:58] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => bd91c689-45f3-47b8-957a-38a7988bee13\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-996534\n            [transactionId] => 0ccab044-0a90-5bed-8b5b-d150018ff67c\n            [quoteStatus] => Draft\n            [message] => Quote# Q-996534 status changed to Draft.\n            [modifiedAt] => 2025-08-11T15:14:55.093Z\n        )\n\n    [publishedAt] => 2025-08-11T15:14:55.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 15:15:00] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 15:15:00 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3697\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => e00678c6-28a9-4b16-9af8-f26f8c042742\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PJZWHHiVoAMEBrw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689a08f3-3434b6be45f0b7325e715188\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-996534\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T16:14:52+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => EMILY ESTATE UK Ltd\n                            [addressLine1] => Estate Office Hadspen House\n                            [city] => Castle Cary\n                            [stateProvinceCode] => \n                            [stateProvince] => SOMERSET\n                            [postalCode] => BA7 7NG\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Sammy\n                            [lastName] => Foote\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-26\n                                    [endDate] => 2026-10-25\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 60372230832636\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-25\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-10-26\n                                                            [endDate] => 2026-10-25\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-996534\n)\n
[quote_update] [2025-08-11 15:15:00] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Mon, 11 Aug 2025 15:15:00 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3697\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 643cc8b0-4103-491a-8b93-cb71bf06ee40\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PJZWHGf7oAMEsAA=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689a08f3-71e00dc5118e20750feb4734\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-996534\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-11T16:14:52+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => \n                            [name] => EMILY ESTATE UK Ltd\n                            [addressLine1] => Estate Office Hadspen House\n                            [city] => Castle Cary\n                            [stateProvinceCode] => \n                            [stateProvince] => SOMERSET\n                            [postalCode] => BA7 7NG\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => ********\n                            [email] => <EMAIL>\n                            [firstName] => Sammy\n                            [lastName] => Foote\n                            [phone] => +************\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => Array\n                        (\n                            [contactCsn] => \n                            [email] => \n                            [firstName] => \n                            [lastName] => \n                            [phone] => \n                            [preferredLanguage] => en\n                        )\n\n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-26\n                                    [endDate] => 2026-10-25\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 60372230832636\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-25\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-11\n                                                            [startDate] => 2025-10-26\n                                                            [endDate] => 2026-10-25\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-996534\n)\n
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 15:15:50
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => cf6bad92-4c88-4d8f-8dc2-0cadcc79f758\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-996534\n            [transactionId] => 0ccab044-0a90-5bed-8b5b-d150018ff67c\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-996534 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T15:15:47.551Z\n        )\n\n    [publishedAt] => 2025-08-11T15:15:47.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-996534', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996534', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-996534', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996534', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-996534', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996534', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-996534', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996534', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 15:15:50
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => cf6bad92-4c88-4d8f-8dc2-0cadcc79f758\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-996534\n            [transactionId] => 0ccab044-0a90-5bed-8b5b-d150018ff67c\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-996534 status changed to Quoted.\n            [modifiedAt] => 2025-08-11T15:15:47.551Z\n        )\n\n    [publishedAt] => 2025-08-11T15:15:47.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-996534', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996534', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-996534', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996534', quote_status = 'Quoted';\n
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-996534', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996534', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 15:15:50] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-996534', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-996534', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 17:28:51
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 10c71e38-c787-4d5a-98c0-01095fe1d859\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995260\n            [transactionId] => b4a3e69d-df50-52b5-8bc0-878e76068777\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-995260 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-11T17:28:48.531Z\n        )\n\n    [publishedAt] => 2025-08-11T17:28:49.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 17:28:51
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 10c71e38-c787-4d5a-98c0-01095fe1d859\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995260\n            [transactionId] => b4a3e69d-df50-52b5-8bc0-878e76068777\n            [quoteStatus] => Order Submitted\n            [message] => Quote# Q-995260 status changed to Order Submitted.\n            [modifiedAt] => 2025-08-11T17:28:48.531Z\n        )\n\n    [publishedAt] => 2025-08-11T17:28:49.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Order Submitted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Order Submitted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Order Submitted';\n
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Order Submitted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 17:28:51] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Order Submitted' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Order Submitted';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 17:28:53
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 5e89e7a9-d783-4c7e-9de8-2bce3a653812\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995260\n            [transactionId] => b4a3e69d-df50-52b5-8bc0-878e76068777\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-995260 status changed to Ordered.\n            [modifiedAt] => 2025-08-11T17:28:50.517Z\n        )\n\n    [publishedAt] => 2025-08-11T17:28:50.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Ordered';\n
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Ordered';\n
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Ordered';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Ordered';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-11 17:28:53
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 5e89e7a9-d783-4c7e-9de8-2bce3a653812\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-995260\n            [transactionId] => b4a3e69d-df50-52b5-8bc0-878e76068777\n            [quoteStatus] => Ordered\n            [message] => Quote# Q-995260 status changed to Ordered.\n            [modifiedAt] => 2025-08-11T17:28:50.517Z\n        )\n\n    [publishedAt] => 2025-08-11T17:28:50.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Ordered';\n
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Ordered';\n
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Ordered';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-11 17:28:53] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-995260', quote_status = 'Ordered' ON DUPLICATE KEY UPDATE quote_number = 'Q-995260', quote_status = 'Ordered';\n",\n        "affected_rows": 0\n    }\n]
[quote_update] [2025-08-12 08:09:12] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-12 08:09:12
[quote_update] [2025-08-12 08:09:12] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-12 08:09:12] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 421bb94f-841c-4884-af8f-9c6ef331d95e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-998113\n            [transactionId] => 2a49818f-fc12-52a6-9ee6-e09b04c736e5\n            [quoteStatus] => Draft\n            [message] => Quote# Q-998113 status changed to Draft.\n            [modifiedAt] => 2025-08-12T08:09:09.435Z\n        )\n\n    [publishedAt] => 2025-08-12T08:09:09.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-12 08:09:12] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-12 08:09:12
[quote_update] [2025-08-12 08:09:12] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-12 08:09:12] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 421bb94f-841c-4884-af8f-9c6ef331d95e\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-998113\n            [transactionId] => 2a49818f-fc12-52a6-9ee6-e09b04c736e5\n            [quoteStatus] => Draft\n            [message] => Quote# Q-998113 status changed to Draft.\n            [modifiedAt] => 2025-08-12T08:09:09.435Z\n        )\n\n    [publishedAt] => 2025-08-12T08:09:09.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-12 08:09:13] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Tue, 12 Aug 2025 08:09:13 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3590\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 6e29a356-cc8a-4674-bfc4-4eaeb717a7e1\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PLt6hGSeoAMEk1A=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689af6a9-2bf0adba0318c64f01fe1529\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-998113\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-12T09:09:06+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => 1\n                            [name] => Martyn Lowther\n                            [addressLine1] => 26 Freeman Avenue\n                            [city] => Brough\n                            [stateProvinceCode] => \n                            [stateProvince] => NORTH HUMBERSIDE\n                            [postalCode] => HU15 1BW\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Martyn\n                            [lastName] => Lowther\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-10\n                                    [endDate] => 2026-10-09\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 72866878277300\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-09\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-12\n                                                            [startDate] => 2025-10-10\n                                                            [endDate] => 2026-10-09\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-998113\n)\n
[quote_update] [2025-08-12 08:09:14] [autodesk_quote.class.php:557] Got quote from api: Array\n(\n    [status] => success\n    [headers] => Array\n        (\n            [Date] => Array\n                (\n                    [0] => Tue, 12 Aug 2025 08:09:13 GMT\n                )\n\n            [Content-Type] => Array\n                (\n                    [0] => application/json\n                )\n\n            [Content-Length] => Array\n                (\n                    [0] => 3590\n                )\n\n            [Connection] => Array\n                (\n                    [0] => keep-alive\n                )\n\n            [x-amzn-RequestId] => Array\n                (\n                    [0] => 7f385fe1-8ecc-4589-b953-f832d833d9a7\n                )\n\n            [x-amz-apigw-id] => Array\n                (\n                    [0] => PLt6iGjgIAMEngw=\n                )\n\n            [X-Amzn-Trace-Id] => Array\n                (\n                    [0] => Root=1-689af6a9-734e66dd1455e7b37cd5cd81\n                )\n\n        )\n\n    [body] => Array\n        (\n            [0] => Array\n                (\n                    [quoteNumber] => Q-998113\n                    [pdfLink] => \n                    [opportunityNumber] => \n                    [quoteCreatedTime] => 2025-08-12T09:09:06+01:00\n                    [quoteExpirationDate] => \n                    [quotedDate] => \n                    [quoteStatus] => Draft\n                    [quoteLanguage] => en\n                    [timeZone] => Europe/London\n                    [paymentTerms] => Array\n                        (\n                            [code] => \n                            [display] => \n                            [description] => \n                        )\n\n                    [pricing] => Array\n                        (\n                            [totalListAmount] => 410\n                            [totalDiscount] => 0\n                            [totalNetAmount] => 410\n                            [estimatedTax] => 82\n                            [totalTradeInAmount] => 0\n                            [totalTradeInTaxAmount] => 0\n                            [totalAmount] => 492\n                            [currency] => GBP\n                            [priceRegionCode] => E5\n                        )\n\n                    [agentAccount] => Array\n                        (\n                            [accountCsn] => **********\n                            [name] => TCS CAD & BIM Solutions Limited\n                            [addressLine1] => Unit F, Yorkway\n                            [addressLine2] => Mandale Ind Est\n                            [city] => Stockton On Tees\n                            [stateProvinceCode] => \n                            [stateProvince] => \n                            [postalCode] => TS17 6BX\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [agentContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Emily\n                            [lastName] => Wood\n                            [phone] => +01642 677582\n                            [preferredLanguage] => en\n                        )\n\n                    [endCustomer] => Array\n                        (\n                            [accountCsn] => **********\n                            [isIndividual] => 1\n                            [name] => Martyn Lowther\n                            [addressLine1] => 26 Freeman Avenue\n                            [city] => Brough\n                            [stateProvinceCode] => \n                            [stateProvince] => NORTH HUMBERSIDE\n                            [postalCode] => HU15 1BW\n                            [countryCode] => GB\n                            [country] => United Kingdom\n                        )\n\n                    [quoteContact] => Array\n                        (\n                            [contactCsn] => **********\n                            [email] => <EMAIL>\n                            [firstName] => Martyn\n                            [lastName] => Lowther\n                            [phone] => +***********\n                            [preferredLanguage] => en\n                        )\n\n                    [admin] => \n                    [salesRep] => \n                    [skipDDACheck] => \n                    [additionalRecipients] => Array\n                        (\n                        )\n\n                    [quoteNotes] => \n                    [originatedBy] => Partner\n                    [skipM2SDiscountValidation] => \n                    [agentQuoteReference] => \n                    [lineItems] => Array\n                        (\n                            [0] => Array\n                                (\n                                    [lineNumber] => 1\n                                    [quoteLineNumber] => **********\n                                    [startDate] => 2025-10-10\n                                    [endDate] => 2026-10-09\n                                    [offeringId] => OD-000031\n                                    [offeringCode] => ACDLT\n                                    [offeringName] => AutoCAD LT\n                                    [marketingName] => AutoCAD LT\n                                    [action] => Renewal\n                                    [unitOfMeasure] => EA\n                                    [quantity] => 1\n                                    [subscription] => Array\n                                        (\n                                            [id] => 72866878277300\n                                            [quantity] => 1\n                                            [endDate] => 2025-10-09\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                        )\n\n                                    [referenceSubscription] => \n                                    [promotionCode] => \n                                    [promotionDescription] => \n                                    [promotionEndDate] => \n                                    [annualDeclaredValue] => \n                                    [declaredValueBasedOn] => \n                                    [scopeOfUse] => \n                                    [scopeDetails] => \n                                    [numberOfProjectsIncluded] => \n                                    [referenceSubscriptions] => \n                                    [agentLineReference] => \n                                    [specialProgramDiscountCode] => \n                                    [specialProgramDiscountDescription] => \n                                    [pricing] => Array\n                                        (\n                                            [currency] => GBP\n                                            [unitSRP] => 410\n                                            [extendedSRP] => 410\n                                            [specialProgramDiscountAmount] => 0\n                                            [renewalDiscountPercent] => 0\n                                            [renewalDiscountAmount] => 0\n                                            [transactionVolumeDiscountPercent] => 0\n                                            [transactionVolumeDiscountAmount] => 0\n                                            [serviceDurationDiscountPercent] => 0\n                                            [serviceDurationDiscountAmount] => 0\n                                            [promotionDiscountPercent] => 0\n                                            [promotionDiscountAmount] => 0\n                                            [discountsApplied] => 0\n                                            [extendedDiscountedSRP] => 410\n                                            [endUserAdditionalDiscountPercent] => 0\n                                            [endUserAdditionalDiscountAmount] => 0\n                                            [exclusiveDiscountsApplied] => 0\n                                            [endUserPrice] => 410\n                                            [tax] => \n                                            [billPlans] => Array\n                                                (\n                                                    [0] => Array\n                                                        (\n                                                            [plan] => 1\n                                                            [billDate] => 2025-08-12\n                                                            [startDate] => 2025-10-10\n                                                            [endDate] => 2026-10-09\n                                                            [extendedSRP] => 410\n                                                            [discountsApplied] => 0\n                                                            [exclusiveDiscountsApplied] => 0\n                                                            [amount] => 410\n                                                        )\n\n                                                )\n\n                                        )\n\n                                    [offer] => Array\n                                        (\n                                            [term] => Array\n                                                (\n                                                    [code] => A01\n                                                    [description] => Annual\n                                                )\n\n                                            [accessModel] => Array\n                                                (\n                                                    [code] => S\n                                                    [description] => Single User\n                                                )\n\n                                            [intendedUsage] => Array\n                                                (\n                                                    [code] => COM\n                                                    [description] => Commercial\n                                                )\n\n                                            [connectivity] => Array\n                                                (\n                                                    [code] => C100\n                                                    [description] => Online\n                                                )\n\n                                            [connectivityInterval] => Array\n                                                (\n                                                    [code] => C04\n                                                    [description] => 30 Days\n                                                )\n\n                                            [billingBehavior] => Array\n                                                (\n                                                    [code] => A200\n                                                    [description] => Recurring\n                                                )\n\n                                            [billingType] => Array\n                                                (\n                                                    [code] => B100\n                                                    [description] => Up front\n                                                )\n\n                                            [billingFrequency] => Array\n                                                (\n                                                    [code] => B05\n                                                    [description] => Annual\n                                                )\n\n                                            [pricingMethod] => Array\n                                                (\n                                                    [code] => QTY\n                                                    [description] => Quantity Based\n                                                )\n\n                                            [servicePlan] => Array\n                                                (\n                                                    [code] => STND\n                                                    [description] => Standard\n                                                )\n\n                                        )\n\n                                )\n\n                        )\n\n                )\n\n        )\n\n    [effective_url] => https://enterprise-api.autodesk.com/v3/quotes?filter%5BquoteNumber%5D=Q-998113\n)\n
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-12 08:10:17
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 54e02fbe-62dc-41b6-916b-6a098fc02ceb\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-998113\n            [transactionId] => 2a49818f-fc12-52a6-9ee6-e09b04c736e5\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-998113 status changed to Quoted.\n            [modifiedAt] => 2025-08-12T08:10:15.404Z\n        )\n\n    [publishedAt] => 2025-08-12T08:10:15.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-998113', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-998113', quote_status = 'Quoted';\n
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-998113', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-998113', quote_status = 'Quoted';\n
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-998113', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-998113', quote_status = 'Quoted';\n\n            [affected_rows] => 2\n        )\n\n)\n
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-998113', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-998113', quote_status = 'Quoted';\n",\n        "affected_rows": 2\n    }\n]
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:515] Starting quote change processing at 2025-08-12 08:10:17
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:532] quote column mapping retrieved
[quote_update] [2025-08-12 08:10:17] [autodesk_quote.class.php:538] Incoming payload: Array\n(\n    [id] => 54e02fbe-62dc-41b6-916b-6a098fc02ceb\n    [topic] => quote-status\n    [event] => changed\n    [sender] => Autodesk Quote Status\n    [environment] => prd\n    [payload] => Array\n        (\n            [quoteNumber] => Q-998113\n            [transactionId] => 2a49818f-fc12-52a6-9ee6-e09b04c736e5\n            [quoteStatus] => Quoted\n            [message] => Quote# Q-998113 status changed to Quoted.\n            [modifiedAt] => 2025-08-12T08:10:15.404Z\n        )\n\n    [publishedAt] => 2025-08-12T08:10:15.000Z\n    [csn] => **********\n)\n
[quote_update] [2025-08-12 08:10:18] [autodesk_quote.class.php:575] found key: quote_number and mapped to autodesk_quotes.quote_number
[quote_update] [2025-08-12 08:10:18] [autodesk_quote.class.php:587] Skipped unmapped key: transactionId
[quote_update] [2025-08-12 08:10:18] [autodesk_quote.class.php:575] found key: quote_status and mapped to autodesk_quotes.quote_status
[quote_update] [2025-08-12 08:10:18] [autodesk_quote.class.php:587] Skipped unmapped key: message
[quote_update] [2025-08-12 08:10:18] [autodesk_quote.class.php:587] Skipped unmapped key: modifiedAt
[quote_update] [2025-08-12 08:10:18] [autodesk_quote.class.php:604] $query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-998113', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-998113', quote_status = 'Quoted';\n
[quote_update] [2025-08-12 08:10:18] [autodesk_quote.class.php:612] query_sql: INSERT INTO autodesk_quotes SET quote_number = 'Q-998113', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-998113', quote_status = 'Quoted';\n
[quote_update] [2025-08-12 08:10:18] [autodesk_quote.class.php:613] response: Array\n(\n    [0] => Array\n        (\n            [status] => success\n            [message] => autodesk_quotes updated successfull\n            [query_sql] => INSERT INTO autodesk_quotes SET quote_number = 'Q-998113', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-998113', quote_status = 'Quoted';\n\n            [affected_rows] => 0\n        )\n\n)\n
[quote_update] [2025-08-12 08:10:18] [autodesk_quote.class.php:617] [\n    {\n        "status": "success",\n        "message": "autodesk_quotes updated successfull",\n        "query_sql": "INSERT INTO autodesk_quotes SET quote_number = 'Q-998113', quote_status = 'Quoted' ON DUPLICATE KEY UPDATE quote_number = 'Q-998113', quote_status = 'Quoted';\n",\n        "affected_rows": 0\n    }\n]
