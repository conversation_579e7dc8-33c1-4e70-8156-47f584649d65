[navigation] [2025-08-11 13:06:12] [nav_tree.api.php:932] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_sketchup_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-11 13:06:14] [nav_tree.api.php:932] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => Bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => 1\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-11 13:06:40] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754917600.csv
[navigation] [2025-08-11 13:06:41] [nav_tree.api.php:549] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754917600.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 13:12:53] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754917973.csv
[navigation] [2025-08-11 13:12:54] [nav_tree.api.php:549] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754917973.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 13:14:32] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754918072.csv
[navigation] [2025-08-11 13:14:33] [nav_tree.api.php:549] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754918072.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 13:16:14] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754918174.csv
[navigation] [2025-08-11 13:16:15] [nav_tree.api.php:549] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754918174.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 13:16:16] [nav_tree.api.php:313] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-11 13:16:16] [nav_tree.api.php:391] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete)\n        )\n\n)\n
[navigation] [2025-08-11 13:20:36] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1754918436.csv
[navigation] [2025-08-11 13:20:37] [nav_tree.api.php:549] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1754918436.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 13:20:38] [nav_tree.api.php:313] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-11 13:20:38] [nav_tree.api.php:391] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete)\n        )\n\n)\n
[navigation] [2025-08-11 13:29:17] [nav_tree.api.php:955] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_sketchup_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-11 13:29:53] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/Sketchup_csv_file_1754918993.csv
[navigation] [2025-08-11 13:29:54] [nav_tree.api.php:572] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/Sketchup_csv_file_1754918993.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => Sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 13:29:54] [nav_tree.api.php:330] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/Sketchup/Sketchup.edge.php created successfully
[navigation] [2025-08-11 13:35:56] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1754919356.csv
[navigation] [2025-08-11 13:35:57] [nav_tree.api.php:572] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1754919356.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 13:35:58] [nav_tree.api.php:330] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-11 13:35:58] [nav_tree.api.php:414] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-11 13:46:42] [nav_tree.api.php:955] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => 1\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-11 13:46:45] [nav_tree.api.php:955] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => 1\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-11 13:47:32] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754920052.csv
[navigation] [2025-08-11 13:47:33] [nav_tree.api.php:572] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754920052.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 13:47:35] [nav_tree.api.php:330] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-11 13:47:35] [nav_tree.api.php:414] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-11 13:49:43] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1754920183.csv
[navigation] [2025-08-11 13:49:44] [nav_tree.api.php:577] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1754920183.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 13:49:45] [nav_tree.api.php:335] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-11 13:49:45] [nav_tree.api.php:419] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-11 13:52:55] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => 1\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-11 13:53:51] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => 1\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-11 14:22:08] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1754922128.csv
[navigation] [2025-08-11 14:22:09] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => asketchup080525.csv\n                    [type] => text/csv\n                    [size] => 58826\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/sketchup_csv_file_1754922128.csv\n                    [original_name] => asketchup080525.csv\n                )\n\n        )\n\n    [key] => sketchup\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 14:22:10] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/sketchup/sketchup.edge.php created successfully
[navigation] [2025-08-11 14:22:10] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [icon] => sketchup\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-11 14:22:51] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754922171.csv
[navigation] [2025-08-11 14:22:52] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754922171.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 14:22:53] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-11 14:22:53] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-11 21:49:36] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-11 21:49:38] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => sketchup\n    [name] => Sketchup\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_sketchup_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-11 21:50:30] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754949030.csv
[navigation] [2025-08-11 21:50:31] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754949030.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 21:50:34] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-11 21:50:34] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-11 21:53:35] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-11 21:59:28] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754949568.csv
[navigation] [2025-08-11 21:59:29] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754949568.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-11 21:59:30] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-11 21:59:30] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-12 08:25:45] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-12 08:27:26] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754987246.csv
[navigation] [2025-08-12 08:27:27] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754987246.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-12 08:27:28] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-12 08:27:28] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-12 08:32:00] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-12 08:33:08] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754987588.csv
[navigation] [2025-08-12 08:33:09] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754987588.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-12 08:33:10] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-12 08:33:10] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-12 08:38:46] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-12 08:39:03] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754987943.csv
[navigation] [2025-08-12 08:39:04] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754987943.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-12 08:39:05] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-12 08:39:05] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-12 08:44:47] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => 1\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-12 08:45:37] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754988337.csv
[navigation] [2025-08-12 08:45:38] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754988337.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-12 08:45:39] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-12 08:45:39] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-12 08:54:24] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-12 08:54:38] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754988878.csv
[navigation] [2025-08-12 08:54:39] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754988878.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-12 08:54:40] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-12 08:54:40] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-12 09:00:45] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-12 09:01:00] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754989260.csv
[navigation] [2025-08-12 09:01:01] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754989260.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-12 09:01:04] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-12 09:01:04] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-12 09:08:49] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-12 09:09:07] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754989747.csv
[navigation] [2025-08-12 09:09:08] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754989747.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-12 09:09:09] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-12 09:09:09] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-12 09:19:40] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-12 09:19:56] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754990396.csv
[navigation] [2025-08-12 09:19:57] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754990396.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-12 09:19:58] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-12 09:19:58] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
[navigation] [2025-08-12 09:38:29] [nav_tree.api.php:966] Array\n(\n    [action] => navigation_entry_deletion_attempt\n    [success] => 1\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [nav_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_navigation WHERE `parent_path` = :where_parent_path_0 AND `route_key` = :where_route_key_1\n        )\n\n    [data_table_delete] => PDOStatement Object\n        (\n            [queryString] => DROP TABLE `autobooks_import_bluebeam_data`\n        )\n\n    [config_delete] => PDOStatement Object\n        (\n            [queryString] => DELETE FROM autobooks_table_configs WHERE `table_name` = :where_table_name_0\n        )\n\n    [folder_delete] => 1\n    [csv_cleanup] => 1\n    [errors] => Array\n        (\n        )\n\n    [user_id] => 2\n)\n
[navigation] [2025-08-12 09:42:20] [nav_tree.api.php:94]  File uploaded successfully: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754991740.csv
[navigation] [2025-08-12 09:42:21] [nav_tree.api.php:583] Array\n(\n    [template_file] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/templatesdata_table_template.hilt.php\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [files] => Array\n        (\n            [csv_file] => Array\n                (\n                    [name] => BluebeamExport.csv\n                    [type] => text/csv\n                    [size] => 63069\n                    [path] => /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/uploads/bluebeam_csv_file_1754991740.csv\n                    [original_name] => BluebeamExport.csv\n                )\n\n        )\n\n    [key] => bluebeam\n    [template_type] => csv\n    [hilt_template_type] => database\n)\n
[navigation] [2025-08-12 09:42:21] [nav_tree.api.php:341] View file /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/resources/views/bluebeam/bluebeam.edge.php created successfully
[navigation] [2025-08-12 09:42:21] [nav_tree.api.php:425] Array\n(\n    [action] => navigation_entry_created\n    [template] => data_table_template\n    [template_data] => Array\n        (\n            [csv_data] => \n        )\n\n    [parent_path] => root\n    [route_key] => bluebeam\n    [name] => Bluebeam\n    [icon] => bluebeam\n    [required_roles] => Array\n        (\n        )\n\n    [user_id] => 2\n    [result] => PDOStatement Object\n        (\n            [queryString] => INSERT INTO autobooks_navigation (`parent_path`, `route_key`, `name`, `icon`, `required_roles`, `file_path`, `is_system`, `can_delete`, `sort_order`) VALUES (:parent_path, :route_key, :name, :icon, :required_roles, :file_path, :is_system, :can_delete, :sort_order)\n        )\n\n)\n
