[database_errors] [2025-08-11 14:08:35] [database.class.php:807] \n<!--array(13) {\n  ["error_code"]: string(5) "42S02"\n  ["error_message"]: string(120) "SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_import_navigation' doesn't exist"\n  ["sql_state"]: string(5) "42S02"\n  ["driver_error_code"]: int(1146)\n  ["driver_error_message"]: string(68) "Table 'wwwcadservicescouk.autobooks_import_navigation' doesn't exist"\n  ["query"]: string(182) "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_import_navigation as nav ORDER BY sort_order ASC, name ASC"\n  ["parameters"]: array(0) {\n  }\n  ["table"]: string(34) "autobooks_import_navigation as nav"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(38) "/baffletrain/autocadlt/autobooks/login"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(675) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(606): PDO->prepare()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(232): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/paths.php(283): system\database->get()\n#3 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/startup_sequence.class.php(19): build_routes()\n#4 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/index.php(26): startup_sequence::start()\n#5 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 807\n         <strong>Arguments:</strong> \n         0: {"error_code":"42S02","error_message":"SQLSTATE[42S02]: Base table or view not found: 1146 Table 'wwwcadservicescouk.autobooks_import_navigation' doesn't exist","sql_state":"42S02","driver_error_code":1146,"driver_error_message":"Table 'wwwcadservicescouk.autobooks_import_navigation' doesn't exist","query":"SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_import_navigation as nav ORDER BY sort_order ASC, name ASC","parameters":[],"table":"autobooks_import_navigation as nav","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/login","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(606): PDO->prepare()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(232): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/paths.php(283): system\\database->get()\n#3 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/startup_sequence.class.php(19): build_routes()\n#4 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/index.php(26): startup_sequence::start()\n#5 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 611\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["42S02",1146,"Table 'wwwcadservicescouk.autobooks_import_navigation' doesn't exist"]}\n         1: "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_import_navigation as nav ORDER BY sort_order ASC, name ASC"\n         2: []\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 232\n         <strong>Arguments:</strong> \n         0: "SELECT id, parent_path, route_key, name, icon, required_roles, show_navbar, file_path, can_delete, is_system FROM autobooks_import_navigation as nav ORDER BY sort_order ASC, name ASC"\n         1: []\n-->\n
[database_errors] [2025-08-11 15:02:05] [database.class.php:883] \n<!--array(13) {\n  ["error_code"]: string(5) "HY093"\n  ["error_message"]: string(68) "SQLSTATE[HY093]: Invalid parameter number: parameter was not defined"\n  ["sql_state"]: string(5) "HY093"\n  ["driver_error_code"]: int(0)\n  ["driver_error_message"]: string(7) "Unknown"\n  ["query"]: string(3443) "SELECT COUNT(*) FROM autobooks_import_sketchup_data WHERE (`sold_to_name` LIKE :nested_0_where_sold_to_name_0 OR `vendor_name` LIKE :nested_0_where_vendor_name_1 OR `reseller_vendor_id` LIKE :nested_0_where_reseller_vendor_id_2 OR `end_customer_name` LIKE :nested_0_where_end_customer_name_3 OR `end_customer_address_1` LIKE :nested_0_where_end_customer_address_1_4 OR `end_customer_address_2` LIKE :nested_0_where_end_customer_address_2_5 OR `end_customer_address_3` LIKE :nested_0_where_end_customer_address_3_6 OR `end_customer_city` LIKE :nested_0_where_end_customer_city_7 OR `end_customer_state` LIKE :nested_0_where_end_customer_state_8 OR `end_customer_zip_code` LIKE :nested_0_where_end_customer_zip_code_9 OR `end_customer_country` LIKE :nested_0_where_end_customer_country_10 OR `end_customer_account_type` LIKE :nested_0_where_end_customer_account_type_11 OR `end_customer_contact_name` LIKE :nested_0_where_end_customer_contact_name_12 OR `end_customer_contact_email` LIKE :nested_0_where_end_customer_contact_email_13 OR `end_customer_contact_phone` LIKE :nested_0_where_end_customer_contact_phone_14 OR `end_customer_industry_segment` LIKE :nested_0_where_end_customer_industry_segment_15 OR `agreement_program_name` LIKE :nested_0_where_agreement_program_name_16 OR `agreement_start_date` LIKE :nested_0_where_agreement_start_date_17 OR `agreement_end_date` LIKE :nested_0_where_agreement_end_date_18 OR `agreement_terms` LIKE :nested_0_where_agreement_terms_19 OR `agreement_type` LIKE :nested_0_where_agreement_type_20 OR `agreement_status` LIKE :nested_0_where_agreement_status_21 OR `agreement_support_level` LIKE :nested_0_where_agreement_support_level_22 OR `product_name` LIKE :nested_0_where_product_name_23 OR `product_family` LIKE :nested_0_where_product_family_24 OR `product_market_segment` LIKE :nested_0_where_product_market_segment_25 OR `product_release` LIKE :nested_0_where_product_release_26 OR `product_type` LIKE :nested_0_where_product_type_27 OR `product_deployment` LIKE :nested_0_where_product_deployment_28 OR `product_sku` LIKE :nested_0_where_product_sku_29 OR `product_sku_description` LIKE :nested_0_where_product_sku_description_30 OR `product_part` LIKE :nested_0_where_product_part_31 OR `product_list_price_currency` LIKE :nested_0_where_product_list_price_currency_32 OR `subscription_id` LIKE :nested_0_where_subscription_id_33 OR `subscription_serial_number` LIKE :nested_0_where_subscription_serial_number_34 OR `subscription_status` LIKE :nested_0_where_subscription_status_35 OR `subscription_start_date` LIKE :nested_0_where_subscription_start_date_36 OR `subscription_end_date` LIKE :nested_0_where_subscription_end_date_37 OR `subscription_contact_name` LIKE :nested_0_where_subscription_contact_name_38 OR `subscription_contact_email` LIKE :nested_0_where_subscription_contact_email_39 OR `subscription_level` LIKE :nested_0_where_subscription_level_40 OR `quotation_id` LIKE :nested_0_where_quotation_id_41 OR `quotation_type` LIKE :nested_0_where_quotation_type_42 OR `quotation_deal_registration_number` LIKE :nested_0_where_quotation_deal_registration_number_43 OR `quotation_status` LIKE :nested_0_where_quotation_status_44 OR `quotation_resellerpo_previous` LIKE :nested_0_where_quotation_resellerpo_previous_45 OR `quotation_due_date` LIKE :nested_0_where_quotation_due_date_46 OR `flaer_phase` LIKE :nested_0_where_flaer_phase_47 OR `updated` LIKE :nested_0_where_updated_48)"\n  ["parameters"]: array(49) {\n    [":nested_0_:where_sold_to_name_0"]: string(6) "%bsba%"\n    [":nested_0_:where_vendor_name_1"]: string(6) "%bsba%"\n    [":nested_0_:where_reseller_vendor_id_2"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_name_3"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_address_1_4"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_address_2_5"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_address_3_6"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_city_7"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_state_8"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_zip_code_9"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_country_10"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_account_type_11"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_contact_name_12"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_contact_email_13"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_contact_phone_14"]: string(6) "%bsba%"\n    [":nested_0_:where_end_customer_industry_segment_15"]: string(6) "%bsba%"\n    [":nested_0_:where_agreement_program_name_16"]: string(6) "%bsba%"\n    [":nested_0_:where_agreement_start_date_17"]: string(6) "%bsba%"\n    [":nested_0_:where_agreement_end_date_18"]: string(6) "%bsba%"\n    [":nested_0_:where_agreement_terms_19"]: string(6) "%bsba%"\n    [":nested_0_:where_agreement_type_20"]: string(6) "%bsba%"\n    [":nested_0_:where_agreement_status_21"]: string(6) "%bsba%"\n    [":nested_0_:where_agreement_support_level_22"]: string(6) "%bsba%"\n    [":nested_0_:where_product_name_23"]: string(6) "%bsba%"\n    [":nested_0_:where_product_family_24"]: string(6) "%bsba%"\n    [":nested_0_:where_product_market_segment_25"]: string(6) "%bsba%"\n    [":nested_0_:where_product_release_26"]: string(6) "%bsba%"\n    [":nested_0_:where_product_type_27"]: string(6) "%bsba%"\n    [":nested_0_:where_product_deployment_28"]: string(6) "%bsba%"\n    [":nested_0_:where_product_sku_29"]: string(6) "%bsba%"\n    [":nested_0_:where_product_sku_description_30"]: string(6) "%bsba%"\n    [":nested_0_:where_product_part_31"]: string(6) "%bsba%"\n    [":nested_0_:where_product_list_price_currency_32"]: string(6) "%bsba%"\n    [":nested_0_:where_subscription_id_33"]: string(6) "%bsba%"\n    [":nested_0_:where_subscription_serial_number_34"]: string(6) "%bsba%"\n    [":nested_0_:where_subscription_status_35"]: string(6) "%bsba%"\n    [":nested_0_:where_subscription_start_date_36"]: string(6) "%bsba%"\n    [":nested_0_:where_subscription_end_date_37"]: string(6) "%bsba%"\n    [":nested_0_:where_subscription_contact_name_38"]: string(6) "%bsba%"\n    [":nested_0_:where_subscription_contact_email_39"]: string(6) "%bsba%"\n    [":nested_0_:where_subscription_level_40"]: string(6) "%bsba%"\n    [":nested_0_:where_quotation_id_41"]: string(6) "%bsba%"\n    [":nested_0_:where_quotation_type_42"]: string(6) "%bsba%"\n    [":nested_0_:where_quotation_deal_registration_number_43"]: string(6) "%bsba%"\n    [":nested_0_:where_quotation_status_44"]: string(6) "%bsba%"\n    [":nested_0_:where_quotation_resellerpo_previous_45"]: string(6) "%bsba%"\n    [":nested_0_:where_quotation_due_date_46"]: string(6) "%bsba%"\n    [":nested_0_:where_flaer_phase_47"]: string(6) "%bsba%"\n    [":nested_0_:where_updated_48"]: string(6) "%bsba%"\n  }\n  ["table"]: string(30) "autobooks_import_sketchup_data"\n  ["user_id"]: string(9) "Anonymous"\n  ["request_uri"]: string(59) "/baffletrain/autocadlt/autobooks/test_improved_database.php"\n  ["user_agent"]: string(80) "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko/20100101 Firefox/141.0"\n  ["ip_address"]: string(12) "************"\n  ["stack_trace"]: string(436) "#0 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(683): PDOStatement->execute()\n#1 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php(298): system\database->executeQuery()\n#2 /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/test_improved_database.php(51): system\database->count()\n#3 {main}"\n}\n      <strong>Function:</strong> tcs_log, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 883\n         <strong>Arguments:</strong> \n         0: {"error_code":"HY093","error_message":"SQLSTATE[HY093]: Invalid parameter number: parameter was not defined","sql_state":"HY093","driver_error_code":0,"driver_error_message":"Unknown","query":"SELECT COUNT(*) FROM autobooks_import_sketchup_data WHERE (`sold_to_name` LIKE :nested_0_where_sold_to_name_0 OR `vendor_name` LIKE :nested_0_where_vendor_name_1 OR `reseller_vendor_id` LIKE :nested_0_where_reseller_vendor_id_2 OR `end_customer_name` LIKE :nested_0_where_end_customer_name_3 OR `end_customer_address_1` LIKE :nested_0_where_end_customer_address_1_4 OR `end_customer_address_2` LIKE :nested_0_where_end_customer_address_2_5 OR `end_customer_address_3` LIKE :nested_0_where_end_customer_address_3_6 OR `end_customer_city` LIKE :nested_0_where_end_customer_city_7 OR `end_customer_state` LIKE :nested_0_where_end_customer_state_8 OR `end_customer_zip_code` LIKE :nested_0_where_end_customer_zip_code_9 OR `end_customer_country` LIKE :nested_0_where_end_customer_country_10 OR `end_customer_account_type` LIKE :nested_0_where_end_customer_account_type_11 OR `end_customer_contact_name` LIKE :nested_0_where_end_customer_contact_name_12 OR `end_customer_contact_email` LIKE :nested_0_where_end_customer_contact_email_13 OR `end_customer_contact_phone` LIKE :nested_0_where_end_customer_contact_phone_14 OR `end_customer_industry_segment` LIKE :nested_0_where_end_customer_industry_segment_15 OR `agreement_program_name` LIKE :nested_0_where_agreement_program_name_16 OR `agreement_start_date` LIKE :nested_0_where_agreement_start_date_17 OR `agreement_end_date` LIKE :nested_0_where_agreement_end_date_18 OR `agreement_terms` LIKE :nested_0_where_agreement_terms_19 OR `agreement_type` LIKE :nested_0_where_agreement_type_20 OR `agreement_status` LIKE :nested_0_where_agreement_status_21 OR `agreement_support_level` LIKE :nested_0_where_agreement_support_level_22 OR `product_name` LIKE :nested_0_where_product_name_23 OR `product_family` LIKE :nested_0_where_product_family_24 OR `product_market_segment` LIKE :nested_0_where_product_market_segment_25 OR `product_release` LIKE :nested_0_where_product_release_26 OR `product_type` LIKE :nested_0_where_product_type_27 OR `product_deployment` LIKE :nested_0_where_product_deployment_28 OR `product_sku` LIKE :nested_0_where_product_sku_29 OR `product_sku_description` LIKE :nested_0_where_product_sku_description_30 OR `product_part` LIKE :nested_0_where_product_part_31 OR `product_list_price_currency` LIKE :nested_0_where_product_list_price_currency_32 OR `subscription_id` LIKE :nested_0_where_subscription_id_33 OR `subscription_serial_number` LIKE :nested_0_where_subscription_serial_number_34 OR `subscription_status` LIKE :nested_0_where_subscription_status_35 OR `subscription_start_date` LIKE :nested_0_where_subscription_start_date_36 OR `subscription_end_date` LIKE :nested_0_where_subscription_end_date_37 OR `subscription_contact_name` LIKE :nested_0_where_subscription_contact_name_38 OR `subscription_contact_email` LIKE :nested_0_where_subscription_contact_email_39 OR `subscription_level` LIKE :nested_0_where_subscription_level_40 OR `quotation_id` LIKE :nested_0_where_quotation_id_41 OR `quotation_type` LIKE :nested_0_where_quotation_type_42 OR `quotation_deal_registration_number` LIKE :nested_0_where_quotation_deal_registration_number_43 OR `quotation_status` LIKE :nested_0_where_quotation_status_44 OR `quotation_resellerpo_previous` LIKE :nested_0_where_quotation_resellerpo_previous_45 OR `quotation_due_date` LIKE :nested_0_where_quotation_due_date_46 OR `flaer_phase` LIKE :nested_0_where_flaer_phase_47 OR `updated` LIKE :nested_0_where_updated_48)","parameters":{":nested_0_:where_sold_to_name_0":"%bsba%",":nested_0_:where_vendor_name_1":"%bsba%",":nested_0_:where_reseller_vendor_id_2":"%bsba%",":nested_0_:where_end_customer_name_3":"%bsba%",":nested_0_:where_end_customer_address_1_4":"%bsba%",":nested_0_:where_end_customer_address_2_5":"%bsba%",":nested_0_:where_end_customer_address_3_6":"%bsba%",":nested_0_:where_end_customer_city_7":"%bsba%",":nested_0_:where_end_customer_state_8":"%bsba%",":nested_0_:where_end_customer_zip_code_9":"%bsba%",":nested_0_:where_end_customer_country_10":"%bsba%",":nested_0_:where_end_customer_account_type_11":"%bsba%",":nested_0_:where_end_customer_contact_name_12":"%bsba%",":nested_0_:where_end_customer_contact_email_13":"%bsba%",":nested_0_:where_end_customer_contact_phone_14":"%bsba%",":nested_0_:where_end_customer_industry_segment_15":"%bsba%",":nested_0_:where_agreement_program_name_16":"%bsba%",":nested_0_:where_agreement_start_date_17":"%bsba%",":nested_0_:where_agreement_end_date_18":"%bsba%",":nested_0_:where_agreement_terms_19":"%bsba%",":nested_0_:where_agreement_type_20":"%bsba%",":nested_0_:where_agreement_status_21":"%bsba%",":nested_0_:where_agreement_support_level_22":"%bsba%",":nested_0_:where_product_name_23":"%bsba%",":nested_0_:where_product_family_24":"%bsba%",":nested_0_:where_product_market_segment_25":"%bsba%",":nested_0_:where_product_release_26":"%bsba%",":nested_0_:where_product_type_27":"%bsba%",":nested_0_:where_product_deployment_28":"%bsba%",":nested_0_:where_product_sku_29":"%bsba%",":nested_0_:where_product_sku_description_30":"%bsba%",":nested_0_:where_product_part_31":"%bsba%",":nested_0_:where_product_list_price_currency_32":"%bsba%",":nested_0_:where_subscription_id_33":"%bsba%",":nested_0_:where_subscription_serial_number_34":"%bsba%",":nested_0_:where_subscription_status_35":"%bsba%",":nested_0_:where_subscription_start_date_36":"%bsba%",":nested_0_:where_subscription_end_date_37":"%bsba%",":nested_0_:where_subscription_contact_name_38":"%bsba%",":nested_0_:where_subscription_contact_email_39":"%bsba%",":nested_0_:where_subscription_level_40":"%bsba%",":nested_0_:where_quotation_id_41":"%bsba%",":nested_0_:where_quotation_type_42":"%bsba%",":nested_0_:where_quotation_deal_registration_number_43":"%bsba%",":nested_0_:where_quotation_status_44":"%bsba%",":nested_0_:where_quotation_resellerpo_previous_45":"%bsba%",":nested_0_:where_quotation_due_date_46":"%bsba%",":nested_0_:where_flaer_phase_47":"%bsba%",":nested_0_:where_updated_48":"%bsba%"},"table":"autobooks_import_sketchup_data","user_id":"Anonymous","request_uri":"\/baffletrain\/autocadlt\/autobooks\/test_improved_database.php","user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64; rv:141.0) Gecko\/20100101 Firefox\/141.0","ip_address":"************","stack_trace":"#0 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(683): PDOStatement->execute()\n#1 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/system\/classes\/database.class.php(298): system\\database->executeQuery()\n#2 \/var\/www\/vhosts\/cadservices.co.uk\/httpdocs\/baffletrain\/autocadlt\/autobooks\/test_improved_database.php(51): system\\database->count()\n#3 {main}"}\n         1: "database_errors"\n         2: true\n      <strong>Function:</strong> handle_database_error, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 687\n         <strong>Arguments:</strong> \n         0: {"errorInfo":["HY093",0]}\n         1: "SELECT COUNT(*) FROM autobooks_import_sketchup_data WHERE (`sold_to_name` LIKE :nested_0_where_sold_to_name_0 OR `vendor_name` LIKE :nested_0_where_vendor_name_1 OR `reseller_vendor_id` LIKE :nested_0_where_reseller_vendor_id_2 OR `end_customer_name` LIKE :nested_0_where_end_customer_name_3 OR `end_customer_address_1` LIKE :nested_0_where_end_customer_address_1_4 OR `end_customer_address_2` LIKE :nested_0_where_end_customer_address_2_5 OR `end_customer_address_3` LIKE :nested_0_where_end_customer_address_3_6 OR `end_customer_city` LIKE :nested_0_where_end_customer_city_7 OR `end_customer_state` LIKE :nested_0_where_end_customer_state_8 OR `end_customer_zip_code` LIKE :nested_0_where_end_customer_zip_code_9 OR `end_customer_country` LIKE :nested_0_where_end_customer_country_10 OR `end_customer_account_type` LIKE :nested_0_where_end_customer_account_type_11 OR `end_customer_contact_name` LIKE :nested_0_where_end_customer_contact_name_12 OR `end_customer_contact_email` LIKE :nested_0_where_end_customer_contact_email_13 OR `end_customer_contact_phone` LIKE :nested_0_where_end_customer_contact_phone_14 OR `end_customer_industry_segment` LIKE :nested_0_where_end_customer_industry_segment_15 OR `agreement_program_name` LIKE :nested_0_where_agreement_program_name_16 OR `agreement_start_date` LIKE :nested_0_where_agreement_start_date_17 OR `agreement_end_date` LIKE :nested_0_where_agreement_end_date_18 OR `agreement_terms` LIKE :nested_0_where_agreement_terms_19 OR `agreement_type` LIKE :nested_0_where_agreement_type_20 OR `agreement_status` LIKE :nested_0_where_agreement_status_21 OR `agreement_support_level` LIKE :nested_0_where_agreement_support_level_22 OR `product_name` LIKE :nested_0_where_product_name_23 OR `product_family` LIKE :nested_0_where_product_family_24 OR `product_market_segment` LIKE :nested_0_where_product_market_segment_25 OR `product_release` LIKE :nested_0_where_product_release_26 OR `product_type` LIKE :nested_0_where_product_type_27 OR `product_deployment` LIKE :nested_0_where_product_deployment_28 OR `product_sku` LIKE :nested_0_where_product_sku_29 OR `product_sku_description` LIKE :nested_0_where_product_sku_description_30 OR `product_part` LIKE :nested_0_where_product_part_31 OR `product_list_price_currency` LIKE :nested_0_where_product_list_price_currency_32 OR `subscription_id` LIKE :nested_0_where_subscription_id_33 OR `subscription_serial_number` LIKE :nested_0_where_subscription_serial_number_34 OR `subscription_status` LIKE :nested_0_where_subscription_status_35 OR `subscription_start_date` LIKE :nested_0_where_subscription_start_date_36 OR `subscription_end_date` LIKE :nested_0_where_subscription_end_date_37 OR `subscription_contact_name` LIKE :nested_0_where_subscription_contact_name_38 OR `subscription_contact_email` LIKE :nested_0_where_subscription_contact_email_39 OR `subscription_level` LIKE :nested_0_where_subscription_level_40 OR `quotation_id` LIKE :nested_0_where_quotation_id_41 OR `quotation_type` LIKE :nested_0_where_quotation_type_42 OR `quotation_deal_registration_number` LIKE :nested_0_where_quotation_deal_registration_number_43 OR `quotation_status` LIKE :nested_0_where_quotation_status_44 OR `quotation_resellerpo_previous` LIKE :nested_0_where_quotation_resellerpo_previous_45 OR `quotation_due_date` LIKE :nested_0_where_quotation_due_date_46 OR `flaer_phase` LIKE :nested_0_where_flaer_phase_47 OR `updated` LIKE :nested_0_where_updated_48)"\n         2: {":nested_0_:where_sold_to_name_0":"%bsba%",":nested_0_:where_vendor_name_1":"%bsba%",":nested_0_:where_reseller_vendor_id_2":"%bsba%",":nested_0_:where_end_customer_name_3":"%bsba%",":nested_0_:where_end_customer_address_1_4":"%bsba%",":nested_0_:where_end_customer_address_2_5":"%bsba%",":nested_0_:where_end_customer_address_3_6":"%bsba%",":nested_0_:where_end_customer_city_7":"%bsba%",":nested_0_:where_end_customer_state_8":"%bsba%",":nested_0_:where_end_customer_zip_code_9":"%bsba%",":nested_0_:where_end_customer_country_10":"%bsba%",":nested_0_:where_end_customer_account_type_11":"%bsba%",":nested_0_:where_end_customer_contact_name_12":"%bsba%",":nested_0_:where_end_customer_contact_email_13":"%bsba%",":nested_0_:where_end_customer_contact_phone_14":"%bsba%",":nested_0_:where_end_customer_industry_segment_15":"%bsba%",":nested_0_:where_agreement_program_name_16":"%bsba%",":nested_0_:where_agreement_start_date_17":"%bsba%",":nested_0_:where_agreement_end_date_18":"%bsba%",":nested_0_:where_agreement_terms_19":"%bsba%",":nested_0_:where_agreement_type_20":"%bsba%",":nested_0_:where_agreement_status_21":"%bsba%",":nested_0_:where_agreement_support_level_22":"%bsba%",":nested_0_:where_product_name_23":"%bsba%",":nested_0_:where_product_family_24":"%bsba%",":nested_0_:where_product_market_segment_25":"%bsba%",":nested_0_:where_product_release_26":"%bsba%",":nested_0_:where_product_type_27":"%bsba%",":nested_0_:where_product_deployment_28":"%bsba%",":nested_0_:where_product_sku_29":"%bsba%",":nested_0_:where_product_sku_description_30":"%bsba%",":nested_0_:where_product_part_31":"%bsba%",":nested_0_:where_product_list_price_currency_32":"%bsba%",":nested_0_:where_subscription_id_33":"%bsba%",":nested_0_:where_subscription_serial_number_34":"%bsba%",":nested_0_:where_subscription_status_35":"%bsba%",":nested_0_:where_subscription_start_date_36":"%bsba%",":nested_0_:where_subscription_end_date_37":"%bsba%",":nested_0_:where_subscription_contact_name_38":"%bsba%",":nested_0_:where_subscription_contact_email_39":"%bsba%",":nested_0_:where_subscription_level_40":"%bsba%",":nested_0_:where_quotation_id_41":"%bsba%",":nested_0_:where_quotation_type_42":"%bsba%",":nested_0_:where_quotation_deal_registration_number_43":"%bsba%",":nested_0_:where_quotation_status_44":"%bsba%",":nested_0_:where_quotation_resellerpo_previous_45":"%bsba%",":nested_0_:where_quotation_due_date_46":"%bsba%",":nested_0_:where_flaer_phase_47":"%bsba%",":nested_0_:where_updated_48":"%bsba%"}\n      <strong>Function:</strong> executeQuery, File: /var/www/vhosts/cadservices.co.uk/httpdocs/baffletrain/autocadlt/autobooks/system/classes/database.class.php, Line: 298\n         <strong>Arguments:</strong> \n         0: "SELECT COUNT(*) FROM autobooks_import_sketchup_data WHERE (`sold_to_name` LIKE :nested_0_where_sold_to_name_0 OR `vendor_name` LIKE :nested_0_where_vendor_name_1 OR `reseller_vendor_id` LIKE :nested_0_where_reseller_vendor_id_2 OR `end_customer_name` LIKE :nested_0_where_end_customer_name_3 OR `end_customer_address_1` LIKE :nested_0_where_end_customer_address_1_4 OR `end_customer_address_2` LIKE :nested_0_where_end_customer_address_2_5 OR `end_customer_address_3` LIKE :nested_0_where_end_customer_address_3_6 OR `end_customer_city` LIKE :nested_0_where_end_customer_city_7 OR `end_customer_state` LIKE :nested_0_where_end_customer_state_8 OR `end_customer_zip_code` LIKE :nested_0_where_end_customer_zip_code_9 OR `end_customer_country` LIKE :nested_0_where_end_customer_country_10 OR `end_customer_account_type` LIKE :nested_0_where_end_customer_account_type_11 OR `end_customer_contact_name` LIKE :nested_0_where_end_customer_contact_name_12 OR `end_customer_contact_email` LIKE :nested_0_where_end_customer_contact_email_13 OR `end_customer_contact_phone` LIKE :nested_0_where_end_customer_contact_phone_14 OR `end_customer_industry_segment` LIKE :nested_0_where_end_customer_industry_segment_15 OR `agreement_program_name` LIKE :nested_0_where_agreement_program_name_16 OR `agreement_start_date` LIKE :nested_0_where_agreement_start_date_17 OR `agreement_end_date` LIKE :nested_0_where_agreement_end_date_18 OR `agreement_terms` LIKE :nested_0_where_agreement_terms_19 OR `agreement_type` LIKE :nested_0_where_agreement_type_20 OR `agreement_status` LIKE :nested_0_where_agreement_status_21 OR `agreement_support_level` LIKE :nested_0_where_agreement_support_level_22 OR `product_name` LIKE :nested_0_where_product_name_23 OR `product_family` LIKE :nested_0_where_product_family_24 OR `product_market_segment` LIKE :nested_0_where_product_market_segment_25 OR `product_release` LIKE :nested_0_where_product_release_26 OR `product_type` LIKE :nested_0_where_product_type_27 OR `product_deployment` LIKE :nested_0_where_product_deployment_28 OR `product_sku` LIKE :nested_0_where_product_sku_29 OR `product_sku_description` LIKE :nested_0_where_product_sku_description_30 OR `product_part` LIKE :nested_0_where_product_part_31 OR `product_list_price_currency` LIKE :nested_0_where_product_list_price_currency_32 OR `subscription_id` LIKE :nested_0_where_subscription_id_33 OR `subscription_serial_number` LIKE :nested_0_where_subscription_serial_number_34 OR `subscription_status` LIKE :nested_0_where_subscription_status_35 OR `subscription_start_date` LIKE :nested_0_where_subscription_start_date_36 OR `subscription_end_date` LIKE :nested_0_where_subscription_end_date_37 OR `subscription_contact_name` LIKE :nested_0_where_subscription_contact_name_38 OR `subscription_contact_email` LIKE :nested_0_where_subscription_contact_email_39 OR `subscription_level` LIKE :nested_0_where_subscription_level_40 OR `quotation_id` LIKE :nested_0_where_quotation_id_41 OR `quotation_type` LIKE :nested_0_where_quotation_type_42 OR `quotation_deal_registration_number` LIKE :nested_0_where_quotation_deal_registration_number_43 OR `quotation_status` LIKE :nested_0_where_quotation_status_44 OR `quotation_resellerpo_previous` LIKE :nested_0_where_quotation_resellerpo_previous_45 OR `quotation_due_date` LIKE :nested_0_where_quotation_due_date_46 OR `flaer_phase` LIKE :nested_0_where_flaer_phase_47 OR `updated` LIKE :nested_0_where_updated_48)"\n         1: {":nested_0_:where_sold_to_name_0":"%bsba%",":nested_0_:where_vendor_name_1":"%bsba%",":nested_0_:where_reseller_vendor_id_2":"%bsba%",":nested_0_:where_end_customer_name_3":"%bsba%",":nested_0_:where_end_customer_address_1_4":"%bsba%",":nested_0_:where_end_customer_address_2_5":"%bsba%",":nested_0_:where_end_customer_address_3_6":"%bsba%",":nested_0_:where_end_customer_city_7":"%bsba%",":nested_0_:where_end_customer_state_8":"%bsba%",":nested_0_:where_end_customer_zip_code_9":"%bsba%",":nested_0_:where_end_customer_country_10":"%bsba%",":nested_0_:where_end_customer_account_type_11":"%bsba%",":nested_0_:where_end_customer_contact_name_12":"%bsba%",":nested_0_:where_end_customer_contact_email_13":"%bsba%",":nested_0_:where_end_customer_contact_phone_14":"%bsba%",":nested_0_:where_end_customer_industry_segment_15":"%bsba%",":nested_0_:where_agreement_program_name_16":"%bsba%",":nested_0_:where_agreement_start_date_17":"%bsba%",":nested_0_:where_agreement_end_date_18":"%bsba%",":nested_0_:where_agreement_terms_19":"%bsba%",":nested_0_:where_agreement_type_20":"%bsba%",":nested_0_:where_agreement_status_21":"%bsba%",":nested_0_:where_agreement_support_level_22":"%bsba%",":nested_0_:where_product_name_23":"%bsba%",":nested_0_:where_product_family_24":"%bsba%",":nested_0_:where_product_market_segment_25":"%bsba%",":nested_0_:where_product_release_26":"%bsba%",":nested_0_:where_product_type_27":"%bsba%",":nested_0_:where_product_deployment_28":"%bsba%",":nested_0_:where_product_sku_29":"%bsba%",":nested_0_:where_product_sku_description_30":"%bsba%",":nested_0_:where_product_part_31":"%bsba%",":nested_0_:where_product_list_price_currency_32":"%bsba%",":nested_0_:where_subscription_id_33":"%bsba%",":nested_0_:where_subscription_serial_number_34":"%bsba%",":nested_0_:where_subscription_status_35":"%bsba%",":nested_0_:where_subscription_start_date_36":"%bsba%",":nested_0_:where_subscription_end_date_37":"%bsba%",":nested_0_:where_subscription_contact_name_38":"%bsba%",":nested_0_:where_subscription_contact_email_39":"%bsba%",":nested_0_:where_subscription_level_40":"%bsba%",":nested_0_:where_quotation_id_41":"%bsba%",":nested_0_:where_quotation_type_42":"%bsba%",":nested_0_:where_quotation_deal_registration_number_43":"%bsba%",":nested_0_:where_quotation_status_44":"%bsba%",":nested_0_:where_quotation_resellerpo_previous_45":"%bsba%",":nested_0_:where_quotation_due_date_46":"%bsba%",":nested_0_:where_flaer_phase_47":"%bsba%",":nested_0_:where_updated_48":"%bsba%"}\n-->\n
