[data_table_generator] [2025-08-11 13:05:52] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_sketchup_data
[data_table_generator] [2025-08-11 13:06:42] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_bluebeam_data
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_bluebeam_data
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:16:16] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_sketchup_data
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column sold_to_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: sold_to_name = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: sold_to_number = 86
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column vendor_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: vendor_name = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column reseller_number mapped to reseller_name with confidence 80
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: reseller_number = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column reseller_vendor_id mapped to reseller_name with confidence 80
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: reseller_vendor_id = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_vendor_id mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_vendor_id = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_name = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_address_1 mapped to address with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_address_1 = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_address_2 mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_address_2 = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_address_3 mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_address_3 = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_city mapped to city with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_city = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_state mapped to state with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_state = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_zip_code mapped to postal_code with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_zip_code = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_country mapped to country with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_country = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_account_type mapped to company_name with confidence 58
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_account_type = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_contact_name mapped to contact_name with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_name = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_contact_email mapped to email with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_email = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_contact_phone mapped to contact_name with confidence 65
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_phone = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column end_customer_industry_segment mapped to company_name with confidence 56
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: end_customer_industry_segment = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column agreement_program_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: agreement_program_name = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column agreement_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: agreement_number = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column agreement_start_date mapped to start_date with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: agreement_start_date = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column agreement_end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: agreement_end_date = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: agreement_terms = 87
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: agreement_type = 95
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column agreement_status mapped to status with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: agreement_status = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: agreement_support_level = 87
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: agreement_days_due = 86
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: agreement_autorenew = 82
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_family mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_family = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_market_segment mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_market_segment = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_release mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_release = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_type mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_type = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_deployment mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_deployment = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_sku mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_sku = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_sku_description mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_sku_description = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_part mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_part = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_list_price mapped to price with confidence 85
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_list_price = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column product_list_price_currency mapped to currency with confidence 85
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: product_list_price_currency = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column subscription_id mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: subscription_id = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column subscription_serial_number mapped to subscription_reference with confidence 85
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: subscription_serial_number = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column subscription_status mapped to status with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: subscription_status = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column subscription_quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: subscription_quantity = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column subscription_start_date mapped to start_date with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: subscription_start_date = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column subscription_end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: subscription_end_date = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column subscription_contact_name mapped to contact_name with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: subscription_contact_name = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column subscription_contact_email mapped to email with confidence 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: subscription_contact_email = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: subscription_level = 93
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: subscription_days_due = 94
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: quotation_id = 93
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: quotation_type = 93
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: quotation_vendor_id = 98
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: quotation_deal_registration_number = 87
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column quotation_status mapped to status with confidence 85
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: quotation_status = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column quotation_resellerpo_previous mapped to reseller_name with confidence 56
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: quotation_resellerpo_previous = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:225] Column quotation_due_date mapped to end_date with confidence 85
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: quotation_due_date = 100
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: flaer_phase = 87
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:173] Column relevance score: updated = 89
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:191] Showing column: sold_to_name (score: 100)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:191] Showing column: vendor_name (score: 100)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:191] Showing column: reseller_number (score: 100)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:191] Showing column: reseller_vendor_id (score: 100)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:191] Showing column: end_customer_vendor_id (score: 100)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:191] Showing column: end_customer_name (score: 100)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:191] Showing column: end_customer_address_1 (score: 100)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:191] Showing column: end_customer_address_2 (score: 100)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: end_customer_address_3 (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: end_customer_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: end_customer_state (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: end_customer_zip_code (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: end_customer_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: end_customer_account_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: end_customer_contact_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: end_customer_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: end_customer_contact_phone (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: end_customer_industry_segment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: agreement_program_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: agreement_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: agreement_start_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: agreement_end_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: agreement_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_family (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_market_segment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_release (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_deployment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_sku (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_sku_description (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_part (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_list_price (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: product_list_price_currency (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: subscription_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: subscription_serial_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: subscription_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: subscription_quantity (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: subscription_start_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: subscription_end_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: subscription_contact_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: subscription_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: quotation_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: quotation_resellerpo_previous (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: quotation_due_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: quotation_vendor_id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: agreement_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: subscription_days_due (score: 94, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: subscription_level (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: quotation_id (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: quotation_type (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: updated (score: 89, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: agreement_terms (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: agreement_support_level (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: quotation_deal_registration_number (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: flaer_phase (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: sold_to_number (score: 86, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: agreement_days_due (score: 86, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:188] Hiding column: agreement_autorenew (score: 82, visible_count: 8)
[data_table_generator] [2025-08-11 13:20:37] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_Sketchup_data
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column sold_to_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: sold_to_name = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: sold_to_number = 86
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column vendor_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: vendor_name = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column reseller_number mapped to reseller_name with confidence 80
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: reseller_number = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column reseller_vendor_id mapped to reseller_name with confidence 80
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: reseller_vendor_id = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_vendor_id mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_vendor_id = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_name = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_address_1 mapped to address with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_address_1 = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_address_2 mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_address_2 = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_address_3 mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_address_3 = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_city mapped to city with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_city = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_state mapped to state with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_state = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_zip_code mapped to postal_code with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_zip_code = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_country mapped to country with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_country = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_account_type mapped to company_name with confidence 58
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_account_type = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_contact_name mapped to contact_name with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_name = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_contact_email mapped to email with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_email = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_contact_phone mapped to contact_name with confidence 65
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_phone = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column end_customer_industry_segment mapped to company_name with confidence 56
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: end_customer_industry_segment = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column agreement_program_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: agreement_program_name = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column agreement_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: agreement_number = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column agreement_start_date mapped to start_date with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: agreement_start_date = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column agreement_end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: agreement_end_date = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: agreement_terms = 87
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: agreement_type = 95
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column agreement_status mapped to status with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: agreement_status = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: agreement_support_level = 87
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: agreement_days_due = 86
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: agreement_autorenew = 82
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_family mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_family = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_market_segment mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_market_segment = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_release mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_release = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_type mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_type = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_deployment mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_deployment = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_sku mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_sku = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_sku_description mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_sku_description = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_part mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_part = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_list_price mapped to price with confidence 85
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_list_price = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column product_list_price_currency mapped to currency with confidence 85
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: product_list_price_currency = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column subscription_id mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: subscription_id = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column subscription_serial_number mapped to subscription_reference with confidence 85
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: subscription_serial_number = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column subscription_status mapped to status with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: subscription_status = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column subscription_quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: subscription_quantity = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column subscription_start_date mapped to start_date with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: subscription_start_date = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column subscription_end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: subscription_end_date = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column subscription_contact_name mapped to contact_name with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: subscription_contact_name = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column subscription_contact_email mapped to email with confidence 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: subscription_contact_email = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: subscription_level = 93
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: subscription_days_due = 94
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: quotation_id = 93
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: quotation_type = 93
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: quotation_vendor_id = 98
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: quotation_deal_registration_number = 87
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column quotation_status mapped to status with confidence 85
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: quotation_status = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column quotation_resellerpo_previous mapped to reseller_name with confidence 56
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: quotation_resellerpo_previous = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:225] Column quotation_due_date mapped to end_date with confidence 85
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: quotation_due_date = 100
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: flaer_phase = 87
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:173] Column relevance score: updated = 89
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:191] Showing column: sold_to_name (score: 100)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:191] Showing column: vendor_name (score: 100)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:191] Showing column: reseller_number (score: 100)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:191] Showing column: reseller_vendor_id (score: 100)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:191] Showing column: end_customer_vendor_id (score: 100)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:191] Showing column: end_customer_name (score: 100)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:191] Showing column: end_customer_address_1 (score: 100)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:191] Showing column: end_customer_address_2 (score: 100)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: end_customer_address_3 (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: end_customer_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: end_customer_state (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: end_customer_zip_code (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: end_customer_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: end_customer_account_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: end_customer_contact_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: end_customer_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: end_customer_contact_phone (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: end_customer_industry_segment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: agreement_program_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: agreement_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: agreement_start_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: agreement_end_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: agreement_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_family (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_market_segment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_release (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_deployment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_sku (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_sku_description (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_part (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_list_price (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: product_list_price_currency (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: subscription_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: subscription_serial_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: subscription_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: subscription_quantity (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: subscription_start_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: subscription_end_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: subscription_contact_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: subscription_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: quotation_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: quotation_resellerpo_previous (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: quotation_due_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: quotation_vendor_id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: agreement_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: subscription_days_due (score: 94, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: subscription_level (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: quotation_id (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: quotation_type (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: updated (score: 89, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: agreement_terms (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: agreement_support_level (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: quotation_deal_registration_number (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: flaer_phase (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: sold_to_number (score: 86, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: agreement_days_due (score: 86, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:188] Hiding column: agreement_autorenew (score: 82, visible_count: 8)
[data_table_generator] [2025-08-11 13:29:54] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_sketchup_data
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column sold_to_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: sold_to_name = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: sold_to_number = 86
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column vendor_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: vendor_name = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column reseller_number mapped to reseller_name with confidence 80
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: reseller_number = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column reseller_vendor_id mapped to reseller_name with confidence 80
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: reseller_vendor_id = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_vendor_id mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_vendor_id = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_name = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_address_1 mapped to address with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_address_1 = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_address_2 mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_address_2 = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_address_3 mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_address_3 = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_city mapped to city with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_city = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_state mapped to state with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_state = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_zip_code mapped to postal_code with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_zip_code = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_country mapped to country with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_country = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_account_type mapped to company_name with confidence 58
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_account_type = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_contact_name mapped to contact_name with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_name = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_contact_email mapped to email with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_email = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_contact_phone mapped to contact_name with confidence 65
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_phone = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column end_customer_industry_segment mapped to company_name with confidence 56
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: end_customer_industry_segment = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column agreement_program_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: agreement_program_name = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column agreement_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: agreement_number = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column agreement_start_date mapped to start_date with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: agreement_start_date = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column agreement_end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: agreement_end_date = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: agreement_terms = 87
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: agreement_type = 95
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:225] Column agreement_status mapped to status with confidence 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: agreement_status = 100
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: agreement_support_level = 87
[data_table_generator] [2025-08-11 13:35:57] [data_table_generator.class.php:173] Column relevance score: agreement_days_due = 86
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: agreement_autorenew = 82
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_family mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_family = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_market_segment mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_market_segment = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_release mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_release = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_type mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_type = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_deployment mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_deployment = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_sku mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_sku = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_sku_description mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_sku_description = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_part mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_part = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_list_price mapped to price with confidence 85
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_list_price = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column product_list_price_currency mapped to currency with confidence 85
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: product_list_price_currency = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column subscription_id mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: subscription_id = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column subscription_serial_number mapped to subscription_reference with confidence 85
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: subscription_serial_number = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column subscription_status mapped to status with confidence 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: subscription_status = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column subscription_quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: subscription_quantity = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column subscription_start_date mapped to start_date with confidence 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: subscription_start_date = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column subscription_end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: subscription_end_date = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column subscription_contact_name mapped to contact_name with confidence 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: subscription_contact_name = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column subscription_contact_email mapped to email with confidence 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: subscription_contact_email = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: subscription_level = 93
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: subscription_days_due = 94
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: quotation_id = 93
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: quotation_type = 93
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: quotation_vendor_id = 98
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: quotation_deal_registration_number = 87
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column quotation_status mapped to status with confidence 85
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: quotation_status = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column quotation_resellerpo_previous mapped to reseller_name with confidence 56
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: quotation_resellerpo_previous = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:225] Column quotation_due_date mapped to end_date with confidence 85
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: quotation_due_date = 100
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: flaer_phase = 87
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:173] Column relevance score: updated = 89
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:191] Showing column: sold_to_name (score: 100)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:191] Showing column: vendor_name (score: 100)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:191] Showing column: reseller_number (score: 100)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:191] Showing column: reseller_vendor_id (score: 100)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:191] Showing column: end_customer_vendor_id (score: 100)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:191] Showing column: end_customer_name (score: 100)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:191] Showing column: end_customer_address_1 (score: 100)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:191] Showing column: end_customer_address_2 (score: 100)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: end_customer_address_3 (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: end_customer_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: end_customer_state (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: end_customer_zip_code (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: end_customer_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: end_customer_account_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: end_customer_contact_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: end_customer_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: end_customer_contact_phone (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: end_customer_industry_segment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: agreement_program_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: agreement_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: agreement_start_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: agreement_end_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: agreement_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_family (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_market_segment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_release (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_deployment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_sku (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_sku_description (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_part (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_list_price (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: product_list_price_currency (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: subscription_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: subscription_serial_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: subscription_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: subscription_quantity (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: subscription_start_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: subscription_end_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: subscription_contact_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: subscription_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: quotation_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: quotation_resellerpo_previous (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: quotation_due_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: quotation_vendor_id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: agreement_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: subscription_days_due (score: 94, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: subscription_level (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: quotation_id (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: quotation_type (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: updated (score: 89, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: agreement_terms (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: agreement_support_level (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: quotation_deal_registration_number (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: flaer_phase (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: sold_to_number (score: 86, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: agreement_days_due (score: 86, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:188] Hiding column: agreement_autorenew (score: 82, visible_count: 8)
[data_table_generator] [2025-08-11 13:35:58] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_bluebeam_data
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-11 13:47:34] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:47:35] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_sketchup_data
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column sold_to_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: sold_to_name = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: sold_to_number = 86
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column vendor_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: vendor_name = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column reseller_number mapped to reseller_name with confidence 80
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: reseller_number = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column reseller_vendor_id mapped to reseller_name with confidence 80
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: reseller_vendor_id = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_vendor_id mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_vendor_id = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_name = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_address_1 mapped to address with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_address_1 = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_address_2 mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_address_2 = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_address_3 mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_address_3 = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_city mapped to city with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_city = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_state mapped to state with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_state = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_zip_code mapped to postal_code with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_zip_code = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_country mapped to country with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_country = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_account_type mapped to company_name with confidence 58
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_account_type = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_contact_name mapped to contact_name with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_name = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_contact_email mapped to email with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_email = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_contact_phone mapped to contact_name with confidence 65
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_phone = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column end_customer_industry_segment mapped to company_name with confidence 56
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: end_customer_industry_segment = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column agreement_program_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: agreement_program_name = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column agreement_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: agreement_number = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column agreement_start_date mapped to start_date with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: agreement_start_date = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column agreement_end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: agreement_end_date = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: agreement_terms = 87
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: agreement_type = 95
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column agreement_status mapped to status with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: agreement_status = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: agreement_support_level = 87
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: agreement_days_due = 86
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: agreement_autorenew = 82
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_family mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_family = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_market_segment mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_market_segment = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_release mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_release = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_type mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_type = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_deployment mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_deployment = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_sku mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_sku = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_sku_description mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_sku_description = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_part mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_part = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_list_price mapped to price with confidence 85
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_list_price = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column product_list_price_currency mapped to currency with confidence 85
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: product_list_price_currency = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column subscription_id mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: subscription_id = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column subscription_serial_number mapped to subscription_reference with confidence 85
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: subscription_serial_number = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column subscription_status mapped to status with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: subscription_status = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column subscription_quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: subscription_quantity = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column subscription_start_date mapped to start_date with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: subscription_start_date = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column subscription_end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: subscription_end_date = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column subscription_contact_name mapped to contact_name with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: subscription_contact_name = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column subscription_contact_email mapped to email with confidence 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: subscription_contact_email = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: subscription_level = 93
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: subscription_days_due = 94
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: quotation_id = 93
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: quotation_type = 93
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: quotation_vendor_id = 98
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: quotation_deal_registration_number = 87
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column quotation_status mapped to status with confidence 85
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: quotation_status = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column quotation_resellerpo_previous mapped to reseller_name with confidence 56
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: quotation_resellerpo_previous = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:225] Column quotation_due_date mapped to end_date with confidence 85
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: quotation_due_date = 100
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: flaer_phase = 87
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:173] Column relevance score: updated = 89
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:191] Showing column: sold_to_name (score: 100)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:191] Showing column: vendor_name (score: 100)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:191] Showing column: reseller_number (score: 100)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:191] Showing column: reseller_vendor_id (score: 100)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:191] Showing column: end_customer_vendor_id (score: 100)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:191] Showing column: end_customer_name (score: 100)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:191] Showing column: end_customer_address_1 (score: 100)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:191] Showing column: end_customer_address_2 (score: 100)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: end_customer_address_3 (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: end_customer_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: end_customer_state (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: end_customer_zip_code (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: end_customer_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: end_customer_account_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: end_customer_contact_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: end_customer_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: end_customer_contact_phone (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: end_customer_industry_segment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: agreement_program_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: agreement_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: agreement_start_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: agreement_end_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: agreement_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_family (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_market_segment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_release (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_deployment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_sku (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_sku_description (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_part (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_list_price (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: product_list_price_currency (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: subscription_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: subscription_serial_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: subscription_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: subscription_quantity (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: subscription_start_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: subscription_end_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: subscription_contact_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: subscription_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: quotation_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: quotation_resellerpo_previous (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: quotation_due_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: quotation_vendor_id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: agreement_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: subscription_days_due (score: 94, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: subscription_level (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: quotation_id (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: quotation_type (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: updated (score: 89, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: agreement_terms (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: agreement_support_level (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: quotation_deal_registration_number (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: flaer_phase (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: sold_to_number (score: 86, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: agreement_days_due (score: 86, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:188] Hiding column: agreement_autorenew (score: 82, visible_count: 8)
[data_table_generator] [2025-08-11 13:49:45] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_sketchup_data
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column sold_to_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: sold_to_name = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: sold_to_number = 86
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column vendor_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: vendor_name = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column reseller_number mapped to reseller_name with confidence 80
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: reseller_number = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column reseller_vendor_id mapped to reseller_name with confidence 80
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: reseller_vendor_id = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_vendor_id mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_vendor_id = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_name = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_address_1 mapped to address with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_address_1 = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_address_2 mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_address_2 = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_address_3 mapped to company_name with confidence 59
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_address_3 = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_city mapped to city with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_city = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_state mapped to state with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_state = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_zip_code mapped to postal_code with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_zip_code = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_country mapped to country with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_country = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_account_type mapped to company_name with confidence 58
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_account_type = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_contact_name mapped to contact_name with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_name = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_contact_email mapped to email with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_email = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_contact_phone mapped to contact_name with confidence 65
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_contact_phone = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column end_customer_industry_segment mapped to company_name with confidence 56
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: end_customer_industry_segment = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column agreement_program_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: agreement_program_name = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column agreement_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: agreement_number = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column agreement_start_date mapped to start_date with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: agreement_start_date = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column agreement_end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: agreement_end_date = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: agreement_terms = 87
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: agreement_type = 95
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column agreement_status mapped to status with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: agreement_status = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: agreement_support_level = 87
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: agreement_days_due = 86
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: agreement_autorenew = 82
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_family mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_family = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_market_segment mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_market_segment = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_release mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_release = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_type mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_type = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_deployment mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_deployment = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_sku mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_sku = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_sku_description mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_sku_description = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_part mapped to product_name with confidence 80
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_part = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_list_price mapped to price with confidence 85
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_list_price = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column product_list_price_currency mapped to currency with confidence 85
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: product_list_price_currency = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column subscription_id mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: subscription_id = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column subscription_serial_number mapped to subscription_reference with confidence 85
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: subscription_serial_number = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column subscription_status mapped to status with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: subscription_status = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column subscription_quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: subscription_quantity = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column subscription_start_date mapped to start_date with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: subscription_start_date = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column subscription_end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: subscription_end_date = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column subscription_contact_name mapped to contact_name with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: subscription_contact_name = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:225] Column subscription_contact_email mapped to email with confidence 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: subscription_contact_email = 100
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: subscription_level = 93
[data_table_generator] [2025-08-11 14:22:09] [data_table_generator.class.php:173] Column relevance score: subscription_days_due = 94
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:173] Column relevance score: quotation_id = 93
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:173] Column relevance score: quotation_type = 93
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:173] Column relevance score: quotation_vendor_id = 98
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:173] Column relevance score: quotation_deal_registration_number = 87
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:225] Column quotation_status mapped to status with confidence 85
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:173] Column relevance score: quotation_status = 100
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:225] Column quotation_resellerpo_previous mapped to reseller_name with confidence 56
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:173] Column relevance score: quotation_resellerpo_previous = 100
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:225] Column quotation_due_date mapped to end_date with confidence 85
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:173] Column relevance score: quotation_due_date = 100
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:173] Column relevance score: flaer_phase = 87
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:173] Column relevance score: updated = 89
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:191] Showing column: sold_to_name (score: 100)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:191] Showing column: vendor_name (score: 100)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:191] Showing column: reseller_number (score: 100)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:191] Showing column: reseller_vendor_id (score: 100)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:191] Showing column: end_customer_vendor_id (score: 100)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:191] Showing column: end_customer_name (score: 100)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:191] Showing column: end_customer_address_1 (score: 100)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:191] Showing column: end_customer_address_2 (score: 100)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: end_customer_address_3 (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: end_customer_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: end_customer_state (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: end_customer_zip_code (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: end_customer_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: end_customer_account_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: end_customer_contact_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: end_customer_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: end_customer_contact_phone (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: end_customer_industry_segment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: agreement_program_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: agreement_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: agreement_start_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: agreement_end_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: agreement_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_family (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_market_segment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_release (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_type (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_deployment (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_sku (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_sku_description (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_part (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_list_price (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: product_list_price_currency (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: subscription_id (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: subscription_serial_number (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: subscription_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: subscription_quantity (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: subscription_start_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: subscription_end_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: subscription_contact_name (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: subscription_contact_email (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: quotation_status (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: quotation_resellerpo_previous (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: quotation_due_date (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: quotation_vendor_id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: agreement_type (score: 95, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: subscription_days_due (score: 94, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: subscription_level (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: quotation_id (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: quotation_type (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: updated (score: 89, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: agreement_terms (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: agreement_support_level (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: quotation_deal_registration_number (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: flaer_phase (score: 87, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: sold_to_number (score: 86, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: agreement_days_due (score: 86, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:188] Hiding column: agreement_autorenew (score: 82, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:10] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 54
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 14:22:53] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 21:50:34] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-11 21:59:29] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-11 21:59:30] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-12 08:27:28] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-12 08:33:10] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-12 08:39:04] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-12 08:45:39] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-12 08:54:40] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-12 09:01:04] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-12 09:09:09] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-12 09:19:58] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:154] Applying intelligent column selection for table: autobooks_import_bluebeam_data
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: id = 98
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column serial_number mapped to subscription_reference with confidence 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: serial_number = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column name mapped to company_name with confidence 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: name = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column contract mapped to subscription_reference with confidence 60
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: contract = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column product_name mapped to product_name with confidence 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: product_name = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column quantity mapped to quantity with confidence 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: quantity = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column end_date mapped to end_date with confidence 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: end_date = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column account_primary_reseller_name mapped to reseller_name with confidence 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: account_primary_reseller_name = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: order_po_number = 93
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column order_shipping_address mapped to address with confidence 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: order_shipping_address = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column order_shipping_city mapped to city with confidence 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: order_shipping_city = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column order_shipping_state_province mapped to state with confidence 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: order_shipping_state_province = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:225] Column order_shipping_country mapped to country with confidence 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:173] Column relevance score: order_shipping_country = 100
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:191] Showing column: serial_number (score: 100)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:191] Showing column: name (score: 100)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:191] Showing column: contract (score: 100)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:191] Showing column: product_name (score: 100)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:191] Showing column: quantity (score: 100)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:191] Showing column: end_date (score: 100)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:191] Showing column: account_primary_reseller_name (score: 100)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:191] Showing column: order_shipping_address (score: 100)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:188] Hiding column: order_shipping_city (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:188] Hiding column: order_shipping_state_province (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:188] Hiding column: order_shipping_country (score: 100, visible_count: 8)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:188] Hiding column: id (score: 98, visible_count: 8)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:188] Hiding column: order_po_number (score: 93, visible_count: 8)
[data_table_generator] [2025-08-12 09:42:21] [data_table_generator.class.php:197] Intelligent column selection complete. Showing 8 columns, hiding 7
