[webhook] [2025-08-10 23:00:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-10 23:00:31
[webhook] [2025-08-10 23:00:31] [adwsapi_v2.php:36]  Provided signature: sha256=1e107e37ad7974ca6d45a94f2f79859377faee29b00b80bf98147daa61ef0791
[webhook] [2025-08-10 23:00:31] [adwsapi_v2.php:37]  Calculated signature: sha256=72e3e60f9b1cfa6c4726085a0220aeef0d090f47b34938904ceb9a832becc62a
[webhook] [2025-08-10 23:00:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6afc2d125b43b34f\n    [X-B3-<PERSON><PERSON>] => 6899248c49a218290ef744f50fc16d7d\n    [B3] => 6899248c49a218290ef744f50fc16d7d-6afc2d125b43b34f-1\n    [Traceparent] => 00-6899248c49a218290ef744f50fc16d7d-6afc2d125b43b34f-01\n    [X-Amzn-Trace-Id] => Root=1-6899248c-49a218290ef744f50fc16d7d;Parent=6afc2d125b43b34f;Sampled=1\n    [X-Adsk-Signature] => sha256=1e107e37ad7974ca6d45a94f2f79859377faee29b00b80bf98147daa61ef0791\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 09da6bff-9160-4d7c-ab53-17d80177529e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-10 23:00:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"09da6bff-9160-4d7c-ab53-17d80177529e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-927963","transactionId":"bd0f7ed8-2410-5ee5-9049-1e36983df684","quoteStatus":"Expired","message":"Quote# Q-927963 status changed to Expired.","modifiedAt":"2025-08-10T23:00:17.413Z"},"publishedAt":"2025-08-10T23:00:29.000Z","csn":"5103159758"}
[webhook] [2025-08-10 23:00:31] [adwsapi_v2.php:20]  Webhook request received at 2025-08-10 23:00:31
[webhook] [2025-08-10 23:00:31] [adwsapi_v2.php:36]  Provided signature: sha256=72e3e60f9b1cfa6c4726085a0220aeef0d090f47b34938904ceb9a832becc62a
[webhook] [2025-08-10 23:00:31] [adwsapi_v2.php:37]  Calculated signature: sha256=72e3e60f9b1cfa6c4726085a0220aeef0d090f47b34938904ceb9a832becc62a
[webhook] [2025-08-10 23:00:31] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d7522b0aa2d6a8cd\n    [X-B3-Traceid] => 6899248c49a218290ef744f50fc16d7d\n    [B3] => 6899248c49a218290ef744f50fc16d7d-d7522b0aa2d6a8cd-1\n    [Traceparent] => 00-6899248c49a218290ef744f50fc16d7d-d7522b0aa2d6a8cd-01\n    [X-Amzn-Trace-Id] => Root=1-6899248c-49a218290ef744f50fc16d7d;Parent=d7522b0aa2d6a8cd;Sampled=1\n    [X-Adsk-Signature] => sha256=72e3e60f9b1cfa6c4726085a0220aeef0d090f47b34938904ceb9a832becc62a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 09da6bff-9160-4d7c-ab53-17d80177529e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-10 23:00:31] [adwsapi_v2.php:57]  Received webhook data: {"id":"09da6bff-9160-4d7c-ab53-17d80177529e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-927963","transactionId":"bd0f7ed8-2410-5ee5-9049-1e36983df684","quoteStatus":"Expired","message":"Quote# Q-927963 status changed to Expired.","modifiedAt":"2025-08-10T23:00:17.413Z"},"publishedAt":"2025-08-10T23:00:29.000Z","csn":"5103159758"}
[webhook] [2025-08-10 23:02:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-10 23:02:48
[webhook] [2025-08-10 23:02:48] [adwsapi_v2.php:36]  Provided signature: sha256=e40f1a33e7bb8e431bf28f1364b28779e62656f07c21f2e6da9f52d8b92baaba
[webhook] [2025-08-10 23:02:48] [adwsapi_v2.php:37]  Calculated signature: sha256=2cce4919d9edd3a7e8575751d379edded6aee4b9e7f4a035c2c3f9f5e4e0cc8c
[webhook] [2025-08-10 23:02:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2b8eaa530df10374\n    [X-B3-Traceid] => 689925162c9a75e14b53c5ef4f016f00\n    [B3] => 689925162c9a75e14b53c5ef4f016f00-2b8eaa530df10374-1\n    [Traceparent] => 00-689925162c9a75e14b53c5ef4f016f00-2b8eaa530df10374-01\n    [X-Amzn-Trace-Id] => Root=1-68992516-2c9a75e14b53c5ef4f016f00;Parent=2b8eaa530df10374;Sampled=1\n    [X-Adsk-Signature] => sha256=e40f1a33e7bb8e431bf28f1364b28779e62656f07c21f2e6da9f52d8b92baaba\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754866966043-572-62544390\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-10 23:02:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754866966043-572-62544390","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"572-62544390","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-10T23:02:46.043Z"},"publishedAt":"2025-08-10T23:02:46.000Z","csn":"5103159758"}
[webhook] [2025-08-10 23:02:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-10 23:02:48
[webhook] [2025-08-10 23:02:48] [adwsapi_v2.php:36]  Provided signature: sha256=2cce4919d9edd3a7e8575751d379edded6aee4b9e7f4a035c2c3f9f5e4e0cc8c
[webhook] [2025-08-10 23:02:48] [adwsapi_v2.php:37]  Calculated signature: sha256=2cce4919d9edd3a7e8575751d379edded6aee4b9e7f4a035c2c3f9f5e4e0cc8c
[webhook] [2025-08-10 23:02:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 925b33c10f657e44\n    [X-B3-Traceid] => 689925162c9a75e14b53c5ef4f016f00\n    [B3] => 689925162c9a75e14b53c5ef4f016f00-925b33c10f657e44-1\n    [Traceparent] => 00-689925162c9a75e14b53c5ef4f016f00-925b33c10f657e44-01\n    [X-Amzn-Trace-Id] => Root=1-68992516-2c9a75e14b53c5ef4f016f00;Parent=925b33c10f657e44;Sampled=1\n    [X-Adsk-Signature] => sha256=2cce4919d9edd3a7e8575751d379edded6aee4b9e7f4a035c2c3f9f5e4e0cc8c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754866966043-572-62544390\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-10 23:02:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754866966043-572-62544390","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"572-62544390","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-10T23:02:46.043Z"},"publishedAt":"2025-08-10T23:02:46.000Z","csn":"5103159758"}
[webhook] [2025-08-11 00:07:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 00:07:35
[webhook] [2025-08-11 00:07:35] [adwsapi_v2.php:36]  Provided signature: sha256=4e2aebdd6261de6d124177eb34fb0c948d4482803ea3138ae90eaa1eb342991c
[webhook] [2025-08-11 00:07:35] [adwsapi_v2.php:37]  Calculated signature: sha256=4e2aebdd6261de6d124177eb34fb0c948d4482803ea3138ae90eaa1eb342991c
[webhook] [2025-08-11 00:07:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => db71b717a8fe879b\n    [X-B3-Traceid] => 689934457fad02783a687808062dbf94\n    [B3] => 689934457fad02783a687808062dbf94-db71b717a8fe879b-1\n    [Traceparent] => 00-689934457fad02783a687808062dbf94-db71b717a8fe879b-01\n    [X-Amzn-Trace-Id] => Root=1-68993445-7fad02783a687808062dbf94;Parent=db71b717a8fe879b;Sampled=1\n    [X-Adsk-Signature] => sha256=4e2aebdd6261de6d124177eb34fb0c948d4482803ea3138ae90eaa1eb342991c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5c6fa27f-b9d9-4949-a961-fe59468588f0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 00:07:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"5c6fa27f-b9d9-4949-a961-fe59468588f0","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"74700029815254","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-10T23:52:30.000+0000"},"publishedAt":"2025-08-11T00:07:33.000Z","csn":"5103159758"}
[webhook] [2025-08-11 00:07:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 00:07:35
[webhook] [2025-08-11 00:07:35] [adwsapi_v2.php:36]  Provided signature: sha256=005018927381c4e8230818cf59d747adcef4ac11ec97e74a70c4d7a622e61f9c
[webhook] [2025-08-11 00:07:35] [adwsapi_v2.php:37]  Calculated signature: sha256=4e2aebdd6261de6d124177eb34fb0c948d4482803ea3138ae90eaa1eb342991c
[webhook] [2025-08-11 00:07:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5517cda69d50a3e5\n    [X-B3-Traceid] => 689934457fad02783a687808062dbf94\n    [B3] => 689934457fad02783a687808062dbf94-5517cda69d50a3e5-1\n    [Traceparent] => 00-689934457fad02783a687808062dbf94-5517cda69d50a3e5-01\n    [X-Amzn-Trace-Id] => Root=1-68993445-7fad02783a687808062dbf94;Parent=5517cda69d50a3e5;Sampled=1\n    [X-Adsk-Signature] => sha256=005018927381c4e8230818cf59d747adcef4ac11ec97e74a70c4d7a622e61f9c\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5c6fa27f-b9d9-4949-a961-fe59468588f0\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 00:07:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"5c6fa27f-b9d9-4949-a961-fe59468588f0","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"74700029815254","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-10T23:52:30.000+0000"},"publishedAt":"2025-08-11T00:07:33.000Z","csn":"5103159758"}
[webhook] [2025-08-11 07:56:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 07:56:06
[webhook] [2025-08-11 07:56:06] [adwsapi_v2.php:36]  Provided signature: sha256=e5eb06ff4a07889fba9d79c80a27e434499117a1c7d1ce0a06d05b6cf9aaedbc
[webhook] [2025-08-11 07:56:06] [adwsapi_v2.php:37]  Calculated signature: sha256=894e145fdeac41e6a759cc3f9b532ac700aa468ca064c0039bfb435f0b99698d
[webhook] [2025-08-11 07:56:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8a8580c97bb1fe90\n    [X-B3-Traceid] => 6899a2141641b897d5471cdb6eb785c9\n    [B3] => 6899a2141641b897d5471cdb6eb785c9-8a8580c97bb1fe90-1\n    [Traceparent] => 00-6899a2141641b897d5471cdb6eb785c9-8a8580c97bb1fe90-01\n    [X-Amzn-Trace-Id] => Root=1-6899a214-1641b897d5471cdb6eb785c9;Parent=8a8580c97bb1fe90;Sampled=1\n    [X-Adsk-Signature] => sha256=e5eb06ff4a07889fba9d79c80a27e434499117a1c7d1ce0a06d05b6cf9aaedbc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => be4da8b9-3dc0-4134-81f9-9dabcb23b456\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 07:56:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"be4da8b9-3dc0-4134-81f9-9dabcb23b456","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995194","transactionId":"dc436df5-dc86-59b3-a97f-0ce678c1a44f","quoteStatus":"Draft","message":"Quote# Q-995194 status changed to Draft.","modifiedAt":"2025-08-11T07:56:04.001Z"},"publishedAt":"2025-08-11T07:56:04.000Z","csn":"5103159758"}
[webhook] [2025-08-11 07:56:06] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 07:56:06
[webhook] [2025-08-11 07:56:06] [adwsapi_v2.php:36]  Provided signature: sha256=894e145fdeac41e6a759cc3f9b532ac700aa468ca064c0039bfb435f0b99698d
[webhook] [2025-08-11 07:56:06] [adwsapi_v2.php:37]  Calculated signature: sha256=894e145fdeac41e6a759cc3f9b532ac700aa468ca064c0039bfb435f0b99698d
[webhook] [2025-08-11 07:56:06] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ea50aa30b8236320\n    [X-B3-Traceid] => 6899a2141641b897d5471cdb6eb785c9\n    [B3] => 6899a2141641b897d5471cdb6eb785c9-ea50aa30b8236320-1\n    [Traceparent] => 00-6899a2141641b897d5471cdb6eb785c9-ea50aa30b8236320-01\n    [X-Amzn-Trace-Id] => Root=1-6899a214-1641b897d5471cdb6eb785c9;Parent=ea50aa30b8236320;Sampled=1\n    [X-Adsk-Signature] => sha256=894e145fdeac41e6a759cc3f9b532ac700aa468ca064c0039bfb435f0b99698d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => be4da8b9-3dc0-4134-81f9-9dabcb23b456\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 07:56:06] [adwsapi_v2.php:57]  Received webhook data: {"id":"be4da8b9-3dc0-4134-81f9-9dabcb23b456","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995194","transactionId":"dc436df5-dc86-59b3-a97f-0ce678c1a44f","quoteStatus":"Draft","message":"Quote# Q-995194 status changed to Draft.","modifiedAt":"2025-08-11T07:56:04.001Z"},"publishedAt":"2025-08-11T07:56:04.000Z","csn":"5103159758"}
[webhook] [2025-08-11 07:56:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 07:56:48
[webhook] [2025-08-11 07:56:48] [adwsapi_v2.php:36]  Provided signature: sha256=ffcce0b7406dfb549334ebc298af1b236e1808b53eeb4feaa058651827f07bbe
[webhook] [2025-08-11 07:56:48] [adwsapi_v2.php:37]  Calculated signature: sha256=2e277931add8ff102ffbeab4734a5461d4fd3d7dd18d67b05fb8e122fcbf8891
[webhook] [2025-08-11 07:56:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5024c11e0df656be\n    [X-B3-Traceid] => 6899a23e032b0e38b9a0068440ef6012\n    [B3] => 6899a23e032b0e38b9a0068440ef6012-5024c11e0df656be-1\n    [Traceparent] => 00-6899a23e032b0e38b9a0068440ef6012-5024c11e0df656be-01\n    [X-Amzn-Trace-Id] => Root=1-6899a23e-032b0e38b9a0068440ef6012;Parent=5024c11e0df656be;Sampled=1\n    [X-Adsk-Signature] => sha256=ffcce0b7406dfb549334ebc298af1b236e1808b53eeb4feaa058651827f07bbe\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 20af0ad7-6859-4a56-ac9f-12fbe5c205cf\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 07:56:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"20af0ad7-6859-4a56-ac9f-12fbe5c205cf","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995197","transactionId":"1269a382-1607-5ebc-bcd0-c291b27d508e","quoteStatus":"Draft","message":"Quote# Q-995197 status changed to Draft.","modifiedAt":"2025-08-11T07:56:46.431Z"},"publishedAt":"2025-08-11T07:56:46.000Z","csn":"5103159758"}
[webhook] [2025-08-11 07:56:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 07:56:49
[webhook] [2025-08-11 07:56:49] [adwsapi_v2.php:36]  Provided signature: sha256=2e277931add8ff102ffbeab4734a5461d4fd3d7dd18d67b05fb8e122fcbf8891
[webhook] [2025-08-11 07:56:49] [adwsapi_v2.php:37]  Calculated signature: sha256=2e277931add8ff102ffbeab4734a5461d4fd3d7dd18d67b05fb8e122fcbf8891
[webhook] [2025-08-11 07:56:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0105aae87733ad99\n    [X-B3-Traceid] => 6899a23e032b0e38b9a0068440ef6012\n    [B3] => 6899a23e032b0e38b9a0068440ef6012-0105aae87733ad99-1\n    [Traceparent] => 00-6899a23e032b0e38b9a0068440ef6012-0105aae87733ad99-01\n    [X-Amzn-Trace-Id] => Root=1-6899a23e-032b0e38b9a0068440ef6012;Parent=0105aae87733ad99;Sampled=1\n    [X-Adsk-Signature] => sha256=2e277931add8ff102ffbeab4734a5461d4fd3d7dd18d67b05fb8e122fcbf8891\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 20af0ad7-6859-4a56-ac9f-12fbe5c205cf\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 07:56:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"20af0ad7-6859-4a56-ac9f-12fbe5c205cf","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995197","transactionId":"1269a382-1607-5ebc-bcd0-c291b27d508e","quoteStatus":"Draft","message":"Quote# Q-995197 status changed to Draft.","modifiedAt":"2025-08-11T07:56:46.431Z"},"publishedAt":"2025-08-11T07:56:46.000Z","csn":"5103159758"}
[webhook] [2025-08-11 07:57:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 07:57:26
[webhook] [2025-08-11 07:57:26] [adwsapi_v2.php:36]  Provided signature: sha256=269d3b429679483e6d42b08b2bed719fdd83dc792426a6e00d56186ac68b3e50
[webhook] [2025-08-11 07:57:26] [adwsapi_v2.php:37]  Calculated signature: sha256=269d3b429679483e6d42b08b2bed719fdd83dc792426a6e00d56186ac68b3e50
[webhook] [2025-08-11 07:57:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7126cf4eeffec9da\n    [X-B3-Traceid] => 6899a26477a524ddf54744a1b999bd0e\n    [B3] => 6899a26477a524ddf54744a1b999bd0e-7126cf4eeffec9da-1\n    [Traceparent] => 00-6899a26477a524ddf54744a1b999bd0e-7126cf4eeffec9da-01\n    [X-Amzn-Trace-Id] => Root=1-6899a264-77a524ddf54744a1b999bd0e;Parent=7126cf4eeffec9da;Sampled=1\n    [X-Adsk-Signature] => sha256=269d3b429679483e6d42b08b2bed719fdd83dc792426a6e00d56186ac68b3e50\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d7b462e1-abc2-49eb-a256-fa18fee41ab6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 07:57:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"d7b462e1-abc2-49eb-a256-fa18fee41ab6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995197","transactionId":"1269a382-1607-5ebc-bcd0-c291b27d508e","quoteStatus":"Quoted","message":"Quote# Q-995197 status changed to Quoted.","modifiedAt":"2025-08-11T07:57:24.036Z"},"publishedAt":"2025-08-11T07:57:24.000Z","csn":"5103159758"}
[webhook] [2025-08-11 07:57:26] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 07:57:26
[webhook] [2025-08-11 07:57:26] [adwsapi_v2.php:36]  Provided signature: sha256=dcdba3862e645038e86e5385496b807b15d6b06c0ad5ea57db7a4603432411c8
[webhook] [2025-08-11 07:57:26] [adwsapi_v2.php:37]  Calculated signature: sha256=269d3b429679483e6d42b08b2bed719fdd83dc792426a6e00d56186ac68b3e50
[webhook] [2025-08-11 07:57:26] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 08f15d8dab07cd0f\n    [X-B3-Traceid] => 6899a26477a524ddf54744a1b999bd0e\n    [B3] => 6899a26477a524ddf54744a1b999bd0e-08f15d8dab07cd0f-1\n    [Traceparent] => 00-6899a26477a524ddf54744a1b999bd0e-08f15d8dab07cd0f-01\n    [X-Amzn-Trace-Id] => Root=1-6899a264-77a524ddf54744a1b999bd0e;Parent=08f15d8dab07cd0f;Sampled=1\n    [X-Adsk-Signature] => sha256=dcdba3862e645038e86e5385496b807b15d6b06c0ad5ea57db7a4603432411c8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d7b462e1-abc2-49eb-a256-fa18fee41ab6\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 07:57:26] [adwsapi_v2.php:57]  Received webhook data: {"id":"d7b462e1-abc2-49eb-a256-fa18fee41ab6","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995197","transactionId":"1269a382-1607-5ebc-bcd0-c291b27d508e","quoteStatus":"Quoted","message":"Quote# Q-995197 status changed to Quoted.","modifiedAt":"2025-08-11T07:57:24.036Z"},"publishedAt":"2025-08-11T07:57:24.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:01:05] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:01:05
[webhook] [2025-08-11 08:01:05] [adwsapi_v2.php:36]  Provided signature: sha256=d543415850d3ad95297f43059df21cbd30dcd49f06d8afafdda78a3af8c2b3fc
[webhook] [2025-08-11 08:01:05] [adwsapi_v2.php:37]  Calculated signature: sha256=d543415850d3ad95297f43059df21cbd30dcd49f06d8afafdda78a3af8c2b3fc
[webhook] [2025-08-11 08:01:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2409d44d231dac9d\n    [X-B3-Traceid] => 6899a33f61c0c75bd1406f10e37a9046\n    [B3] => 6899a33f61c0c75bd1406f10e37a9046-2409d44d231dac9d-1\n    [Traceparent] => 00-6899a33f61c0c75bd1406f10e37a9046-2409d44d231dac9d-01\n    [X-Amzn-Trace-Id] => Root=1-6899a33f-61c0c75bd1406f10e37a9046;Parent=2409d44d231dac9d;Sampled=1\n    [X-Adsk-Signature] => sha256=d543415850d3ad95297f43059df21cbd30dcd49f06d8afafdda78a3af8c2b3fc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0ce8a5ba-1835-445a-a3ff-bdd44e9a523b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:01:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"0ce8a5ba-1835-445a-a3ff-bdd44e9a523b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995208","transactionId":"17aa203f-ed8b-57a3-b7c6-412c48f51395","quoteStatus":"Draft","message":"Quote# Q-995208 status changed to Draft.","modifiedAt":"2025-08-11T08:01:02.804Z"},"publishedAt":"2025-08-11T08:01:03.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:01:05] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:01:05
[webhook] [2025-08-11 08:01:05] [adwsapi_v2.php:36]  Provided signature: sha256=9d95a7079238451681db4975d93e2d5d0c33d74c83255646f22f5c38368b8a27
[webhook] [2025-08-11 08:01:05] [adwsapi_v2.php:37]  Calculated signature: sha256=d543415850d3ad95297f43059df21cbd30dcd49f06d8afafdda78a3af8c2b3fc
[webhook] [2025-08-11 08:01:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 878b67d9d7d4eaaf\n    [X-B3-Traceid] => 6899a33f61c0c75bd1406f10e37a9046\n    [B3] => 6899a33f61c0c75bd1406f10e37a9046-878b67d9d7d4eaaf-1\n    [Traceparent] => 00-6899a33f61c0c75bd1406f10e37a9046-878b67d9d7d4eaaf-01\n    [X-Amzn-Trace-Id] => Root=1-6899a33f-61c0c75bd1406f10e37a9046;Parent=878b67d9d7d4eaaf;Sampled=1\n    [X-Adsk-Signature] => sha256=9d95a7079238451681db4975d93e2d5d0c33d74c83255646f22f5c38368b8a27\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 0ce8a5ba-1835-445a-a3ff-bdd44e9a523b\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:01:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"0ce8a5ba-1835-445a-a3ff-bdd44e9a523b","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995208","transactionId":"17aa203f-ed8b-57a3-b7c6-412c48f51395","quoteStatus":"Draft","message":"Quote# Q-995208 status changed to Draft.","modifiedAt":"2025-08-11T08:01:02.804Z"},"publishedAt":"2025-08-11T08:01:03.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:01:59] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:01:59
[webhook] [2025-08-11 08:01:59] [adwsapi_v2.php:36]  Provided signature: sha256=9a33b1beb82e786ee6ffaf0d3208e3521b362f2627bae5f35cc4bec79ca2536a
[webhook] [2025-08-11 08:01:59] [adwsapi_v2.php:37]  Calculated signature: sha256=9a33b1beb82e786ee6ffaf0d3208e3521b362f2627bae5f35cc4bec79ca2536a
[webhook] [2025-08-11 08:01:59] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8a1a2ec4d46e8f66\n    [X-B3-Traceid] => 6899a375227f3ef2406da86ca78c53df\n    [B3] => 6899a375227f3ef2406da86ca78c53df-8a1a2ec4d46e8f66-1\n    [Traceparent] => 00-6899a375227f3ef2406da86ca78c53df-8a1a2ec4d46e8f66-01\n    [X-Amzn-Trace-Id] => Root=1-6899a375-227f3ef2406da86ca78c53df;Parent=8a1a2ec4d46e8f66;Sampled=1\n    [X-Adsk-Signature] => sha256=9a33b1beb82e786ee6ffaf0d3208e3521b362f2627bae5f35cc4bec79ca2536a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6c38f88f-27ed-44f3-b7e8-07b4b2c03b42\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:01:59] [adwsapi_v2.php:57]  Received webhook data: {"id":"6c38f88f-27ed-44f3-b7e8-07b4b2c03b42","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995208","transactionId":"17aa203f-ed8b-57a3-b7c6-412c48f51395","quoteStatus":"Quoted","message":"Quote# Q-995208 status changed to Quoted.","modifiedAt":"2025-08-11T08:01:57.095Z"},"publishedAt":"2025-08-11T08:01:57.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:02:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:02:00
[webhook] [2025-08-11 08:02:00] [adwsapi_v2.php:36]  Provided signature: sha256=a49f7b8597671482eb6aa2c4e4fcf27938835f5ef7b0310e23cd815fbc40e32f
[webhook] [2025-08-11 08:02:00] [adwsapi_v2.php:37]  Calculated signature: sha256=9a33b1beb82e786ee6ffaf0d3208e3521b362f2627bae5f35cc4bec79ca2536a
[webhook] [2025-08-11 08:02:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2d589d902ed71983\n    [X-B3-Traceid] => 6899a375227f3ef2406da86ca78c53df\n    [B3] => 6899a375227f3ef2406da86ca78c53df-2d589d902ed71983-1\n    [Traceparent] => 00-6899a375227f3ef2406da86ca78c53df-2d589d902ed71983-01\n    [X-Amzn-Trace-Id] => Root=1-6899a375-227f3ef2406da86ca78c53df;Parent=2d589d902ed71983;Sampled=1\n    [X-Adsk-Signature] => sha256=a49f7b8597671482eb6aa2c4e4fcf27938835f5ef7b0310e23cd815fbc40e32f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 6c38f88f-27ed-44f3-b7e8-07b4b2c03b42\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:02:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"6c38f88f-27ed-44f3-b7e8-07b4b2c03b42","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995208","transactionId":"17aa203f-ed8b-57a3-b7c6-412c48f51395","quoteStatus":"Quoted","message":"Quote# Q-995208 status changed to Quoted.","modifiedAt":"2025-08-11T08:01:57.095Z"},"publishedAt":"2025-08-11T08:01:57.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:04:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:04:39
[webhook] [2025-08-11 08:04:39] [adwsapi_v2.php:36]  Provided signature: sha256=dba8bd3d018ffaeb037176b733f3b902e28facb3a2d91d6fe94fa66fdd4ed5e5
[webhook] [2025-08-11 08:04:39] [adwsapi_v2.php:37]  Calculated signature: sha256=dba8bd3d018ffaeb037176b733f3b902e28facb3a2d91d6fe94fa66fdd4ed5e5
[webhook] [2025-08-11 08:04:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7fe21b18a4bd39a1\n    [X-B3-Traceid] => 6899a415d82c672fa0fb9f8639177ce4\n    [B3] => 6899a415d82c672fa0fb9f8639177ce4-7fe21b18a4bd39a1-1\n    [Traceparent] => 00-6899a415d82c672fa0fb9f8639177ce4-7fe21b18a4bd39a1-01\n    [X-Amzn-Trace-Id] => Root=1-6899a415-d82c672fa0fb9f8639177ce4;Parent=7fe21b18a4bd39a1;Sampled=1\n    [X-Adsk-Signature] => sha256=dba8bd3d018ffaeb037176b733f3b902e28facb3a2d91d6fe94fa66fdd4ed5e5\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f4f88649-a9b5-419f-bb3c-9bbbe4a28e53\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:04:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"f4f88649-a9b5-419f-bb3c-9bbbe4a28e53","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995212","transactionId":"dad4b5a5-084e-5c89-aee9-d4b6ae0fb016","quoteStatus":"Draft","message":"Quote# Q-995212 status changed to Draft.","modifiedAt":"2025-08-11T08:04:36.810Z"},"publishedAt":"2025-08-11T08:04:37.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:04:39] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:04:39
[webhook] [2025-08-11 08:04:39] [adwsapi_v2.php:36]  Provided signature: sha256=e4656f9ff53e9af58be30371f3fd39e265b59625f921bbb5098bf39ce2921eba
[webhook] [2025-08-11 08:04:39] [adwsapi_v2.php:37]  Calculated signature: sha256=dba8bd3d018ffaeb037176b733f3b902e28facb3a2d91d6fe94fa66fdd4ed5e5
[webhook] [2025-08-11 08:04:39] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 687ccd69b53da859\n    [X-B3-Traceid] => 6899a415d82c672fa0fb9f8639177ce4\n    [B3] => 6899a415d82c672fa0fb9f8639177ce4-687ccd69b53da859-1\n    [Traceparent] => 00-6899a415d82c672fa0fb9f8639177ce4-687ccd69b53da859-01\n    [X-Amzn-Trace-Id] => Root=1-6899a415-d82c672fa0fb9f8639177ce4;Parent=687ccd69b53da859;Sampled=1\n    [X-Adsk-Signature] => sha256=e4656f9ff53e9af58be30371f3fd39e265b59625f921bbb5098bf39ce2921eba\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f4f88649-a9b5-419f-bb3c-9bbbe4a28e53\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:04:39] [adwsapi_v2.php:57]  Received webhook data: {"id":"f4f88649-a9b5-419f-bb3c-9bbbe4a28e53","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995212","transactionId":"dad4b5a5-084e-5c89-aee9-d4b6ae0fb016","quoteStatus":"Draft","message":"Quote# Q-995212 status changed to Draft.","modifiedAt":"2025-08-11T08:04:36.810Z"},"publishedAt":"2025-08-11T08:04:37.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:05:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:05:14
[webhook] [2025-08-11 08:05:14] [adwsapi_v2.php:36]  Provided signature: sha256=1e4b09161b7a935555aca63bdf33f1685b8b95005aec8eecf78b03979af1968f
[webhook] [2025-08-11 08:05:14] [adwsapi_v2.php:37]  Calculated signature: sha256=3d9e9febf2b7a7f8b09939dd35c3270ae406d29b719bb5af2199ad62500ec538
[webhook] [2025-08-11 08:05:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5f85811239136f28\n    [X-B3-Traceid] => 6899a438fb3555cda7742fa7efb7b4d1\n    [B3] => 6899a438fb3555cda7742fa7efb7b4d1-5f85811239136f28-1\n    [Traceparent] => 00-6899a438fb3555cda7742fa7efb7b4d1-5f85811239136f28-01\n    [X-Amzn-Trace-Id] => Root=1-6899a438-fb3555cda7742fa7efb7b4d1;Parent=5f85811239136f28;Sampled=1\n    [X-Adsk-Signature] => sha256=1e4b09161b7a935555aca63bdf33f1685b8b95005aec8eecf78b03979af1968f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 902a2482-bda7-471f-8c04-f751749d3382\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:05:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"902a2482-bda7-471f-8c04-f751749d3382","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995212","transactionId":"dad4b5a5-084e-5c89-aee9-d4b6ae0fb016","quoteStatus":"Quoted","message":"Quote# Q-995212 status changed to Quoted.","modifiedAt":"2025-08-11T08:05:11.844Z"},"publishedAt":"2025-08-11T08:05:12.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:05:14] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:05:14
[webhook] [2025-08-11 08:05:14] [adwsapi_v2.php:36]  Provided signature: sha256=3d9e9febf2b7a7f8b09939dd35c3270ae406d29b719bb5af2199ad62500ec538
[webhook] [2025-08-11 08:05:14] [adwsapi_v2.php:37]  Calculated signature: sha256=3d9e9febf2b7a7f8b09939dd35c3270ae406d29b719bb5af2199ad62500ec538
[webhook] [2025-08-11 08:05:14] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 44bc006fe7a8538d\n    [X-B3-Traceid] => 6899a438fb3555cda7742fa7efb7b4d1\n    [B3] => 6899a438fb3555cda7742fa7efb7b4d1-44bc006fe7a8538d-1\n    [Traceparent] => 00-6899a438fb3555cda7742fa7efb7b4d1-44bc006fe7a8538d-01\n    [X-Amzn-Trace-Id] => Root=1-6899a438-fb3555cda7742fa7efb7b4d1;Parent=44bc006fe7a8538d;Sampled=1\n    [X-Adsk-Signature] => sha256=3d9e9febf2b7a7f8b09939dd35c3270ae406d29b719bb5af2199ad62500ec538\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 902a2482-bda7-471f-8c04-f751749d3382\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:05:14] [adwsapi_v2.php:57]  Received webhook data: {"id":"902a2482-bda7-471f-8c04-f751749d3382","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995212","transactionId":"dad4b5a5-084e-5c89-aee9-d4b6ae0fb016","quoteStatus":"Quoted","message":"Quote# Q-995212 status changed to Quoted.","modifiedAt":"2025-08-11T08:05:11.844Z"},"publishedAt":"2025-08-11T08:05:12.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:06:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:06:21
[webhook] [2025-08-11 08:06:21] [adwsapi_v2.php:36]  Provided signature: sha256=9fbdbabd24ff8cb4fc2b3eeb0d98320c991523fa1df16ae678f4c03f30e8973b
[webhook] [2025-08-11 08:06:21] [adwsapi_v2.php:37]  Calculated signature: sha256=ecee8441b0e68523b794867a866774603378dcf45554b2676e05e5052eb85694
[webhook] [2025-08-11 08:06:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 37e902e0ed61cf28\n    [X-B3-Traceid] => 6899a47a7327e40e5b00f535b59924e6\n    [B3] => 6899a47a7327e40e5b00f535b59924e6-37e902e0ed61cf28-1\n    [Traceparent] => 00-6899a47a7327e40e5b00f535b59924e6-37e902e0ed61cf28-01\n    [X-Amzn-Trace-Id] => Root=1-6899a47a-7327e40e5b00f535b59924e6;Parent=37e902e0ed61cf28;Sampled=1\n    [X-Adsk-Signature] => sha256=9fbdbabd24ff8cb4fc2b3eeb0d98320c991523fa1df16ae678f4c03f30e8973b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 77b18d9a-2a4a-4d74-8949-d0e4f1a70bc1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:06:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"77b18d9a-2a4a-4d74-8949-d0e4f1a70bc1","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995217","transactionId":"64c6354e-4b3f-546c-b756-545ded8d2aec","quoteStatus":"Draft","message":"Quote# Q-995217 status changed to Draft.","modifiedAt":"2025-08-11T08:06:18.507Z"},"publishedAt":"2025-08-11T08:06:18.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:06:21] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:06:21
[webhook] [2025-08-11 08:06:21] [adwsapi_v2.php:36]  Provided signature: sha256=ecee8441b0e68523b794867a866774603378dcf45554b2676e05e5052eb85694
[webhook] [2025-08-11 08:06:21] [adwsapi_v2.php:37]  Calculated signature: sha256=ecee8441b0e68523b794867a866774603378dcf45554b2676e05e5052eb85694
[webhook] [2025-08-11 08:06:21] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e5c4615038a6baa7\n    [X-B3-Traceid] => 6899a47a7327e40e5b00f535b59924e6\n    [B3] => 6899a47a7327e40e5b00f535b59924e6-e5c4615038a6baa7-1\n    [Traceparent] => 00-6899a47a7327e40e5b00f535b59924e6-e5c4615038a6baa7-01\n    [X-Amzn-Trace-Id] => Root=1-6899a47a-7327e40e5b00f535b59924e6;Parent=e5c4615038a6baa7;Sampled=1\n    [X-Adsk-Signature] => sha256=ecee8441b0e68523b794867a866774603378dcf45554b2676e05e5052eb85694\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 77b18d9a-2a4a-4d74-8949-d0e4f1a70bc1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:06:21] [adwsapi_v2.php:57]  Received webhook data: {"id":"77b18d9a-2a4a-4d74-8949-d0e4f1a70bc1","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995217","transactionId":"64c6354e-4b3f-546c-b756-545ded8d2aec","quoteStatus":"Draft","message":"Quote# Q-995217 status changed to Draft.","modifiedAt":"2025-08-11T08:06:18.507Z"},"publishedAt":"2025-08-11T08:06:18.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:06:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:06:58
[webhook] [2025-08-11 08:06:58] [adwsapi_v2.php:36]  Provided signature: sha256=7fb163e89e99d2e91cfb4dbd967dca0e626dad39cf56eb1fbabe7c7147854263
[webhook] [2025-08-11 08:06:58] [adwsapi_v2.php:37]  Calculated signature: sha256=7fb163e89e99d2e91cfb4dbd967dca0e626dad39cf56eb1fbabe7c7147854263
[webhook] [2025-08-11 08:06:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3f9e289cb0ad603a\n    [X-B3-Traceid] => 6899a4a022496e1eff2bb3efb31871c4\n    [B3] => 6899a4a022496e1eff2bb3efb31871c4-3f9e289cb0ad603a-1\n    [Traceparent] => 00-6899a4a022496e1eff2bb3efb31871c4-3f9e289cb0ad603a-01\n    [X-Amzn-Trace-Id] => Root=1-6899a4a0-22496e1eff2bb3efb31871c4;Parent=3f9e289cb0ad603a;Sampled=1\n    [X-Adsk-Signature] => sha256=7fb163e89e99d2e91cfb4dbd967dca0e626dad39cf56eb1fbabe7c7147854263\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 968e73e8-961f-45c5-943c-3003d508b681\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:06:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"968e73e8-961f-45c5-943c-3003d508b681","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995217","transactionId":"64c6354e-4b3f-546c-b756-545ded8d2aec","quoteStatus":"Quoted","message":"Quote# Q-995217 status changed to Quoted.","modifiedAt":"2025-08-11T08:06:55.947Z"},"publishedAt":"2025-08-11T08:06:56.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:06:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:06:58
[webhook] [2025-08-11 08:06:58] [adwsapi_v2.php:36]  Provided signature: sha256=dbc55463240b08ac2c0c317b656fe78031e698f4c7a1b1dac3146eeb0a3fc92e
[webhook] [2025-08-11 08:06:58] [adwsapi_v2.php:37]  Calculated signature: sha256=7fb163e89e99d2e91cfb4dbd967dca0e626dad39cf56eb1fbabe7c7147854263
[webhook] [2025-08-11 08:06:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 574cd22ee05a7187\n    [X-B3-Traceid] => 6899a4a022496e1eff2bb3efb31871c4\n    [B3] => 6899a4a022496e1eff2bb3efb31871c4-574cd22ee05a7187-1\n    [Traceparent] => 00-6899a4a022496e1eff2bb3efb31871c4-574cd22ee05a7187-01\n    [X-Amzn-Trace-Id] => Root=1-6899a4a0-22496e1eff2bb3efb31871c4;Parent=574cd22ee05a7187;Sampled=1\n    [X-Adsk-Signature] => sha256=dbc55463240b08ac2c0c317b656fe78031e698f4c7a1b1dac3146eeb0a3fc92e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 968e73e8-961f-45c5-943c-3003d508b681\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:06:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"968e73e8-961f-45c5-943c-3003d508b681","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995217","transactionId":"64c6354e-4b3f-546c-b756-545ded8d2aec","quoteStatus":"Quoted","message":"Quote# Q-995217 status changed to Quoted.","modifiedAt":"2025-08-11T08:06:55.947Z"},"publishedAt":"2025-08-11T08:06:56.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:07:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:07:48
[webhook] [2025-08-11 08:07:48] [adwsapi_v2.php:36]  Provided signature: sha256=57a319f4a2d5ed17ceb81bf7a963f5bd271b41cd69aece20503f1f163b7b9a4e
[webhook] [2025-08-11 08:07:48] [adwsapi_v2.php:37]  Calculated signature: sha256=57a319f4a2d5ed17ceb81bf7a963f5bd271b41cd69aece20503f1f163b7b9a4e
[webhook] [2025-08-11 08:07:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 040c54557343deee\n    [X-B3-Traceid] => 6899a4d256358d4da44cadb1cea53621\n    [B3] => 6899a4d256358d4da44cadb1cea53621-040c54557343deee-1\n    [Traceparent] => 00-6899a4d256358d4da44cadb1cea53621-040c54557343deee-01\n    [X-Amzn-Trace-Id] => Root=1-6899a4d2-56358d4da44cadb1cea53621;Parent=040c54557343deee;Sampled=1\n    [X-Adsk-Signature] => sha256=57a319f4a2d5ed17ceb81bf7a963f5bd271b41cd69aece20503f1f163b7b9a4e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 559abccc-b535-4a0c-b262-420825853211\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:07:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"559abccc-b535-4a0c-b262-420825853211","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995219","transactionId":"3b431047-3f8b-5095-93f1-71549975113b","quoteStatus":"Draft","message":"Quote# Q-995219 status changed to Draft.","modifiedAt":"2025-08-11T08:07:45.743Z"},"publishedAt":"2025-08-11T08:07:46.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:07:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:07:48
[webhook] [2025-08-11 08:07:48] [adwsapi_v2.php:36]  Provided signature: sha256=901a989acf69bd310844631cc664ed7129f2c0bdb1c5eaee25b199402c4cf740
[webhook] [2025-08-11 08:07:48] [adwsapi_v2.php:37]  Calculated signature: sha256=57a319f4a2d5ed17ceb81bf7a963f5bd271b41cd69aece20503f1f163b7b9a4e
[webhook] [2025-08-11 08:07:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 55c54c7b70de673c\n    [X-B3-Traceid] => 6899a4d256358d4da44cadb1cea53621\n    [B3] => 6899a4d256358d4da44cadb1cea53621-55c54c7b70de673c-1\n    [Traceparent] => 00-6899a4d256358d4da44cadb1cea53621-55c54c7b70de673c-01\n    [X-Amzn-Trace-Id] => Root=1-6899a4d2-56358d4da44cadb1cea53621;Parent=55c54c7b70de673c;Sampled=1\n    [X-Adsk-Signature] => sha256=901a989acf69bd310844631cc664ed7129f2c0bdb1c5eaee25b199402c4cf740\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 559abccc-b535-4a0c-b262-420825853211\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:07:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"559abccc-b535-4a0c-b262-420825853211","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995219","transactionId":"3b431047-3f8b-5095-93f1-71549975113b","quoteStatus":"Draft","message":"Quote# Q-995219 status changed to Draft.","modifiedAt":"2025-08-11T08:07:45.743Z"},"publishedAt":"2025-08-11T08:07:46.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:10:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:10:07
[webhook] [2025-08-11 08:10:07] [adwsapi_v2.php:36]  Provided signature: sha256=f0c0ef7c580592a1dae8ddf8f06f331a364cd63f88eddc1647ddcea3f8499beb
[webhook] [2025-08-11 08:10:07] [adwsapi_v2.php:37]  Calculated signature: sha256=01df3b9d984baa018b84fcce7e406ffba68f91dd20b82a70a24afcfa7d4eaeb8
[webhook] [2025-08-11 08:10:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 78b551a3748f43fd\n    [X-B3-Traceid] => 6899a55d2360e19a28d046c440c93461\n    [B3] => 6899a55d2360e19a28d046c440c93461-78b551a3748f43fd-1\n    [Traceparent] => 00-6899a55d2360e19a28d046c440c93461-78b551a3748f43fd-01\n    [X-Amzn-Trace-Id] => Root=1-6899a55d-2360e19a28d046c440c93461;Parent=78b551a3748f43fd;Sampled=1\n    [X-Adsk-Signature] => sha256=f0c0ef7c580592a1dae8ddf8f06f331a364cd63f88eddc1647ddcea3f8499beb\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d4a8e751-a533-4534-96af-6a17d0c46c8a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:10:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"d4a8e751-a533-4534-96af-6a17d0c46c8a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995219","transactionId":"3b431047-3f8b-5095-93f1-71549975113b","quoteStatus":"Quoted","message":"Quote# Q-995219 status changed to Quoted.","modifiedAt":"2025-08-11T08:10:04.801Z"},"publishedAt":"2025-08-11T08:10:05.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:10:07] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:10:07
[webhook] [2025-08-11 08:10:07] [adwsapi_v2.php:36]  Provided signature: sha256=01df3b9d984baa018b84fcce7e406ffba68f91dd20b82a70a24afcfa7d4eaeb8
[webhook] [2025-08-11 08:10:07] [adwsapi_v2.php:37]  Calculated signature: sha256=01df3b9d984baa018b84fcce7e406ffba68f91dd20b82a70a24afcfa7d4eaeb8
[webhook] [2025-08-11 08:10:07] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1363d66c3845d467\n    [X-B3-Traceid] => 6899a55d2360e19a28d046c440c93461\n    [B3] => 6899a55d2360e19a28d046c440c93461-1363d66c3845d467-1\n    [Traceparent] => 00-6899a55d2360e19a28d046c440c93461-1363d66c3845d467-01\n    [X-Amzn-Trace-Id] => Root=1-6899a55d-2360e19a28d046c440c93461;Parent=1363d66c3845d467;Sampled=1\n    [X-Adsk-Signature] => sha256=01df3b9d984baa018b84fcce7e406ffba68f91dd20b82a70a24afcfa7d4eaeb8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => d4a8e751-a533-4534-96af-6a17d0c46c8a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:10:07] [adwsapi_v2.php:57]  Received webhook data: {"id":"d4a8e751-a533-4534-96af-6a17d0c46c8a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995219","transactionId":"3b431047-3f8b-5095-93f1-71549975113b","quoteStatus":"Quoted","message":"Quote# Q-995219 status changed to Quoted.","modifiedAt":"2025-08-11T08:10:04.801Z"},"publishedAt":"2025-08-11T08:10:05.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:17:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:17:35
[webhook] [2025-08-11 08:17:35] [adwsapi_v2.php:36]  Provided signature: sha256=4b2671b46b7ef83fcd8fd1f15df1a6a30a1dc22d1f32bff7631ba57f20b3c822
[webhook] [2025-08-11 08:17:35] [adwsapi_v2.php:37]  Calculated signature: sha256=46dfaedf50228b4294d435570d3a5df80e4855b77ac592f275a7848f34d08d6d
[webhook] [2025-08-11 08:17:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 3998dae88cfd169a\n    [X-B3-Traceid] => 6899a71d2de566728f4d5f9291c4dbd9\n    [B3] => 6899a71d2de566728f4d5f9291c4dbd9-3998dae88cfd169a-1\n    [Traceparent] => 00-6899a71d2de566728f4d5f9291c4dbd9-3998dae88cfd169a-01\n    [X-Amzn-Trace-Id] => Root=1-6899a71d-2de566728f4d5f9291c4dbd9;Parent=3998dae88cfd169a;Sampled=1\n    [X-Adsk-Signature] => sha256=4b2671b46b7ef83fcd8fd1f15df1a6a30a1dc22d1f32bff7631ba57f20b3c822\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7c7f36e4-479a-450d-8c87-324a26b51116\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:17:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"7c7f36e4-479a-450d-8c87-324a26b51116","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995260","transactionId":"b4a3e69d-df50-52b5-8bc0-878e76068777","quoteStatus":"Draft","message":"Quote# Q-995260 status changed to Draft.","modifiedAt":"2025-08-11T08:17:32.878Z"},"publishedAt":"2025-08-11T08:17:33.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:17:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:17:35
[webhook] [2025-08-11 08:17:35] [adwsapi_v2.php:36]  Provided signature: sha256=46dfaedf50228b4294d435570d3a5df80e4855b77ac592f275a7848f34d08d6d
[webhook] [2025-08-11 08:17:35] [adwsapi_v2.php:37]  Calculated signature: sha256=46dfaedf50228b4294d435570d3a5df80e4855b77ac592f275a7848f34d08d6d
[webhook] [2025-08-11 08:17:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c27251134dba9eae\n    [X-B3-Traceid] => 6899a71d2de566728f4d5f9291c4dbd9\n    [B3] => 6899a71d2de566728f4d5f9291c4dbd9-c27251134dba9eae-1\n    [Traceparent] => 00-6899a71d2de566728f4d5f9291c4dbd9-c27251134dba9eae-01\n    [X-Amzn-Trace-Id] => Root=1-6899a71d-2de566728f4d5f9291c4dbd9;Parent=c27251134dba9eae;Sampled=1\n    [X-Adsk-Signature] => sha256=46dfaedf50228b4294d435570d3a5df80e4855b77ac592f275a7848f34d08d6d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 7c7f36e4-479a-450d-8c87-324a26b51116\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:17:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"7c7f36e4-479a-450d-8c87-324a26b51116","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995260","transactionId":"b4a3e69d-df50-52b5-8bc0-878e76068777","quoteStatus":"Draft","message":"Quote# Q-995260 status changed to Draft.","modifiedAt":"2025-08-11T08:17:32.878Z"},"publishedAt":"2025-08-11T08:17:33.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:18:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:18:08
[webhook] [2025-08-11 08:18:08] [adwsapi_v2.php:36]  Provided signature: sha256=6b29ce63d7931d000e52cc95f2ab209e57e7de3f78364f1a345af08004131dba
[webhook] [2025-08-11 08:18:08] [adwsapi_v2.php:37]  Calculated signature: sha256=5ba0392d2ec853c369b9ec46614a993dd9672272a3fe57a1e83d8efaaa6bef2a
[webhook] [2025-08-11 08:18:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f82899bf5399bc64\n    [X-B3-Traceid] => 6899a73db5e25c0b1a4153d8ac12cdbc\n    [B3] => 6899a73db5e25c0b1a4153d8ac12cdbc-f82899bf5399bc64-1\n    [Traceparent] => 00-6899a73db5e25c0b1a4153d8ac12cdbc-f82899bf5399bc64-01\n    [X-Amzn-Trace-Id] => Root=1-6899a73d-b5e25c0b1a4153d8ac12cdbc;Parent=f82899bf5399bc64;Sampled=1\n    [X-Adsk-Signature] => sha256=6b29ce63d7931d000e52cc95f2ab209e57e7de3f78364f1a345af08004131dba\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2623a7e8-ee57-4768-9d42-78290efb0bef\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:18:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"2623a7e8-ee57-4768-9d42-78290efb0bef","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995260","transactionId":"b4a3e69d-df50-52b5-8bc0-878e76068777","quoteStatus":"Quoted","message":"Quote# Q-995260 status changed to Quoted.","modifiedAt":"2025-08-11T08:18:05.497Z"},"publishedAt":"2025-08-11T08:18:05.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:18:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:18:08
[webhook] [2025-08-11 08:18:08] [adwsapi_v2.php:36]  Provided signature: sha256=5ba0392d2ec853c369b9ec46614a993dd9672272a3fe57a1e83d8efaaa6bef2a
[webhook] [2025-08-11 08:18:08] [adwsapi_v2.php:37]  Calculated signature: sha256=5ba0392d2ec853c369b9ec46614a993dd9672272a3fe57a1e83d8efaaa6bef2a
[webhook] [2025-08-11 08:18:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 82193294f3ddaea2\n    [X-B3-Traceid] => 6899a73db5e25c0b1a4153d8ac12cdbc\n    [B3] => 6899a73db5e25c0b1a4153d8ac12cdbc-82193294f3ddaea2-1\n    [Traceparent] => 00-6899a73db5e25c0b1a4153d8ac12cdbc-82193294f3ddaea2-01\n    [X-Amzn-Trace-Id] => Root=1-6899a73d-b5e25c0b1a4153d8ac12cdbc;Parent=82193294f3ddaea2;Sampled=1\n    [X-Adsk-Signature] => sha256=5ba0392d2ec853c369b9ec46614a993dd9672272a3fe57a1e83d8efaaa6bef2a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2623a7e8-ee57-4768-9d42-78290efb0bef\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:18:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"2623a7e8-ee57-4768-9d42-78290efb0bef","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995260","transactionId":"b4a3e69d-df50-52b5-8bc0-878e76068777","quoteStatus":"Quoted","message":"Quote# Q-995260 status changed to Quoted.","modifiedAt":"2025-08-11T08:18:05.497Z"},"publishedAt":"2025-08-11T08:18:05.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:19:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:19:04
[webhook] [2025-08-11 08:19:04] [adwsapi_v2.php:36]  Provided signature: sha256=24ab99ba220b09b023d3b94ece6b98ee8ab62e9edee6973236e3a6c56f6fb557
[webhook] [2025-08-11 08:19:04] [adwsapi_v2.php:37]  Calculated signature: sha256=24ab99ba220b09b023d3b94ece6b98ee8ab62e9edee6973236e3a6c56f6fb557
[webhook] [2025-08-11 08:19:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c3fd714455e384b7\n    [X-B3-Traceid] => 6899a776a083576a61a4570dc6f8d42c\n    [B3] => 6899a776a083576a61a4570dc6f8d42c-c3fd714455e384b7-1\n    [Traceparent] => 00-6899a776a083576a61a4570dc6f8d42c-c3fd714455e384b7-01\n    [X-Amzn-Trace-Id] => Root=1-6899a776-a083576a61a4570dc6f8d42c;Parent=c3fd714455e384b7;Sampled=1\n    [X-Adsk-Signature] => sha256=24ab99ba220b09b023d3b94ece6b98ee8ab62e9edee6973236e3a6c56f6fb557\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8167a87e-598e-4975-9186-e48ac3e23d28\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:19:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"8167a87e-598e-4975-9186-e48ac3e23d28","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995264","transactionId":"172f82ac-7f02-5e9c-a141-e3bfd6ac82db","quoteStatus":"Draft","message":"Quote# Q-995264 status changed to Draft.","modifiedAt":"2025-08-11T08:19:01.993Z"},"publishedAt":"2025-08-11T08:19:02.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:19:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:19:04
[webhook] [2025-08-11 08:19:04] [adwsapi_v2.php:36]  Provided signature: sha256=3e73ab9f3e20e67b525de34b0f97cb58429d71eb80846011b3bced5d01862288
[webhook] [2025-08-11 08:19:04] [adwsapi_v2.php:37]  Calculated signature: sha256=24ab99ba220b09b023d3b94ece6b98ee8ab62e9edee6973236e3a6c56f6fb557
[webhook] [2025-08-11 08:19:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 83c001b3b31de906\n    [X-B3-Traceid] => 6899a776a083576a61a4570dc6f8d42c\n    [B3] => 6899a776a083576a61a4570dc6f8d42c-83c001b3b31de906-1\n    [Traceparent] => 00-6899a776a083576a61a4570dc6f8d42c-83c001b3b31de906-01\n    [X-Amzn-Trace-Id] => Root=1-6899a776-a083576a61a4570dc6f8d42c;Parent=83c001b3b31de906;Sampled=1\n    [X-Adsk-Signature] => sha256=3e73ab9f3e20e67b525de34b0f97cb58429d71eb80846011b3bced5d01862288\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8167a87e-598e-4975-9186-e48ac3e23d28\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:19:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"8167a87e-598e-4975-9186-e48ac3e23d28","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995264","transactionId":"172f82ac-7f02-5e9c-a141-e3bfd6ac82db","quoteStatus":"Draft","message":"Quote# Q-995264 status changed to Draft.","modifiedAt":"2025-08-11T08:19:01.993Z"},"publishedAt":"2025-08-11T08:19:02.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:19:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:19:51
[webhook] [2025-08-11 08:19:51] [adwsapi_v2.php:36]  Provided signature: sha256=e07a4ca085105af15a86997be827063f1182516b95e00b7a826ff0cfa8fc943d
[webhook] [2025-08-11 08:19:51] [adwsapi_v2.php:37]  Calculated signature: sha256=e07a4ca085105af15a86997be827063f1182516b95e00b7a826ff0cfa8fc943d
[webhook] [2025-08-11 08:19:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8bf12b93465f95a4\n    [X-B3-Traceid] => 6899a7a588bc5c24e98997f1cd10119f\n    [B3] => 6899a7a588bc5c24e98997f1cd10119f-8bf12b93465f95a4-1\n    [Traceparent] => 00-6899a7a588bc5c24e98997f1cd10119f-8bf12b93465f95a4-01\n    [X-Amzn-Trace-Id] => Root=1-6899a7a5-88bc5c24e98997f1cd10119f;Parent=8bf12b93465f95a4;Sampled=1\n    [X-Adsk-Signature] => sha256=e07a4ca085105af15a86997be827063f1182516b95e00b7a826ff0cfa8fc943d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e5ee71ec-2422-4d79-9dd5-fce0c6162ed2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:19:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"e5ee71ec-2422-4d79-9dd5-fce0c6162ed2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995264","transactionId":"172f82ac-7f02-5e9c-a141-e3bfd6ac82db","quoteStatus":"Quoted","message":"Quote# Q-995264 status changed to Quoted.","modifiedAt":"2025-08-11T08:19:49.003Z"},"publishedAt":"2025-08-11T08:19:49.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:19:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:19:51
[webhook] [2025-08-11 08:19:51] [adwsapi_v2.php:36]  Provided signature: sha256=9b2c8673e0375e37212b1fdbcdbfdbab4547de795f540bc8d16cec254c1082ca
[webhook] [2025-08-11 08:19:51] [adwsapi_v2.php:37]  Calculated signature: sha256=e07a4ca085105af15a86997be827063f1182516b95e00b7a826ff0cfa8fc943d
[webhook] [2025-08-11 08:19:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0cd4c4dfcf9b8efc\n    [X-B3-Traceid] => 6899a7a588bc5c24e98997f1cd10119f\n    [B3] => 6899a7a588bc5c24e98997f1cd10119f-0cd4c4dfcf9b8efc-1\n    [Traceparent] => 00-6899a7a588bc5c24e98997f1cd10119f-0cd4c4dfcf9b8efc-01\n    [X-Amzn-Trace-Id] => Root=1-6899a7a5-88bc5c24e98997f1cd10119f;Parent=0cd4c4dfcf9b8efc;Sampled=1\n    [X-Adsk-Signature] => sha256=9b2c8673e0375e37212b1fdbcdbfdbab4547de795f540bc8d16cec254c1082ca\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => e5ee71ec-2422-4d79-9dd5-fce0c6162ed2\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:19:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"e5ee71ec-2422-4d79-9dd5-fce0c6162ed2","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995264","transactionId":"172f82ac-7f02-5e9c-a141-e3bfd6ac82db","quoteStatus":"Quoted","message":"Quote# Q-995264 status changed to Quoted.","modifiedAt":"2025-08-11T08:19:49.003Z"},"publishedAt":"2025-08-11T08:19:49.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:20:33] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:20:33
[webhook] [2025-08-11 08:20:33] [adwsapi_v2.php:36]  Provided signature: sha256=f3b29f19d46e44b74425b0dc5ec516aa0d9181bc4a75828af334a4da7d225956
[webhook] [2025-08-11 08:20:33] [adwsapi_v2.php:37]  Calculated signature: sha256=f3b29f19d46e44b74425b0dc5ec516aa0d9181bc4a75828af334a4da7d225956
[webhook] [2025-08-11 08:20:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 9953b7be6f79e9dd\n    [X-B3-Traceid] => 6899a7cff709d22e37dd149382e1be51\n    [B3] => 6899a7cff709d22e37dd149382e1be51-9953b7be6f79e9dd-1\n    [Traceparent] => 00-6899a7cff709d22e37dd149382e1be51-9953b7be6f79e9dd-01\n    [X-Amzn-Trace-Id] => Root=1-6899a7cf-f709d22e37dd149382e1be51;Parent=9953b7be6f79e9dd;Sampled=1\n    [X-Adsk-Signature] => sha256=f3b29f19d46e44b74425b0dc5ec516aa0d9181bc4a75828af334a4da7d225956\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 94a752bb-5d27-4f48-998e-1bf67d375e9a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:20:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"94a752bb-5d27-4f48-998e-1bf67d375e9a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995272","transactionId":"436c791e-32d3-5356-b5df-180d98d0d04f","quoteStatus":"Draft","message":"Quote# Q-995272 status changed to Draft.","modifiedAt":"2025-08-11T08:20:31.013Z"},"publishedAt":"2025-08-11T08:20:31.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:20:33] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:20:33
[webhook] [2025-08-11 08:20:33] [adwsapi_v2.php:36]  Provided signature: sha256=effc57d2efa49cf16278822f08e94d05fd1ad9b8c08f1960df88dccd4774d42e
[webhook] [2025-08-11 08:20:33] [adwsapi_v2.php:37]  Calculated signature: sha256=f3b29f19d46e44b74425b0dc5ec516aa0d9181bc4a75828af334a4da7d225956
[webhook] [2025-08-11 08:20:33] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 6fce9df4712e7a60\n    [X-B3-Traceid] => 6899a7cff709d22e37dd149382e1be51\n    [B3] => 6899a7cff709d22e37dd149382e1be51-6fce9df4712e7a60-1\n    [Traceparent] => 00-6899a7cff709d22e37dd149382e1be51-6fce9df4712e7a60-01\n    [X-Amzn-Trace-Id] => Root=1-6899a7cf-f709d22e37dd149382e1be51;Parent=6fce9df4712e7a60;Sampled=1\n    [X-Adsk-Signature] => sha256=effc57d2efa49cf16278822f08e94d05fd1ad9b8c08f1960df88dccd4774d42e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 94a752bb-5d27-4f48-998e-1bf67d375e9a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:20:33] [adwsapi_v2.php:57]  Received webhook data: {"id":"94a752bb-5d27-4f48-998e-1bf67d375e9a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995272","transactionId":"436c791e-32d3-5356-b5df-180d98d0d04f","quoteStatus":"Draft","message":"Quote# Q-995272 status changed to Draft.","modifiedAt":"2025-08-11T08:20:31.013Z"},"publishedAt":"2025-08-11T08:20:31.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:21:40] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:21:40
[webhook] [2025-08-11 08:21:40] [adwsapi_v2.php:36]  Provided signature: sha256=bd484413c61501711a342f5bc969597eac30cbe5c57aa2f0995fd689bf74b00b
[webhook] [2025-08-11 08:21:40] [adwsapi_v2.php:37]  Calculated signature: sha256=6ad44b7e771127d30940c720501dc585bfca56cf4109de256335074705edfca1
[webhook] [2025-08-11 08:21:40] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f234116025b9aa0d\n    [X-B3-Traceid] => 6899a8124a2e107961ba3b4cf95309e6\n    [B3] => 6899a8124a2e107961ba3b4cf95309e6-f234116025b9aa0d-1\n    [Traceparent] => 00-6899a8124a2e107961ba3b4cf95309e6-f234116025b9aa0d-01\n    [X-Amzn-Trace-Id] => Root=1-6899a812-4a2e107961ba3b4cf95309e6;Parent=f234116025b9aa0d;Sampled=1\n    [X-Adsk-Signature] => sha256=bd484413c61501711a342f5bc969597eac30cbe5c57aa2f0995fd689bf74b00b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 59477164-faf1-4cc1-b356-4e65a1273c28\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:21:40] [adwsapi_v2.php:57]  Received webhook data: {"id":"59477164-faf1-4cc1-b356-4e65a1273c28","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995272","transactionId":"436c791e-32d3-5356-b5df-180d98d0d04f","quoteStatus":"Quoted","message":"Quote# Q-995272 status changed to Quoted.","modifiedAt":"2025-08-11T08:21:38.538Z"},"publishedAt":"2025-08-11T08:21:38.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:21:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:21:41
[webhook] [2025-08-11 08:21:41] [adwsapi_v2.php:36]  Provided signature: sha256=6ad44b7e771127d30940c720501dc585bfca56cf4109de256335074705edfca1
[webhook] [2025-08-11 08:21:41] [adwsapi_v2.php:37]  Calculated signature: sha256=6ad44b7e771127d30940c720501dc585bfca56cf4109de256335074705edfca1
[webhook] [2025-08-11 08:21:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => fce3d2381415919f\n    [X-B3-Traceid] => 6899a8124a2e107961ba3b4cf95309e6\n    [B3] => 6899a8124a2e107961ba3b4cf95309e6-fce3d2381415919f-1\n    [Traceparent] => 00-6899a8124a2e107961ba3b4cf95309e6-fce3d2381415919f-01\n    [X-Amzn-Trace-Id] => Root=1-6899a812-4a2e107961ba3b4cf95309e6;Parent=fce3d2381415919f;Sampled=1\n    [X-Adsk-Signature] => sha256=6ad44b7e771127d30940c720501dc585bfca56cf4109de256335074705edfca1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 59477164-faf1-4cc1-b356-4e65a1273c28\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:21:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"59477164-faf1-4cc1-b356-4e65a1273c28","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995272","transactionId":"436c791e-32d3-5356-b5df-180d98d0d04f","quoteStatus":"Quoted","message":"Quote# Q-995272 status changed to Quoted.","modifiedAt":"2025-08-11T08:21:38.538Z"},"publishedAt":"2025-08-11T08:21:38.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:48:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:48:43
[webhook] [2025-08-11 08:48:43] [adwsapi_v2.php:36]  Provided signature: sha256=18ca3c039b430e18ad587227726899b462a01dffdc1ff118d2cfd359c4f559f8
[webhook] [2025-08-11 08:48:43] [adwsapi_v2.php:37]  Calculated signature: sha256=9e29846ab18de5ad5369704a427cf3a0d6419b37aaffc477031ddb44fd51fdfa
[webhook] [2025-08-11 08:48:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ef3758cbc5774ccd\n    [X-B3-Traceid] => 6899ae699d10a531395139f400b20528\n    [B3] => 6899ae699d10a531395139f400b20528-ef3758cbc5774ccd-1\n    [Traceparent] => 00-6899ae699d10a531395139f400b20528-ef3758cbc5774ccd-01\n    [X-Amzn-Trace-Id] => Root=1-6899ae69-9d10a531395139f400b20528;Parent=ef3758cbc5774ccd;Sampled=1\n    [X-Adsk-Signature] => sha256=18ca3c039b430e18ad587227726899b462a01dffdc1ff118d2cfd359c4f559f8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9d09a821-c7fc-4b76-8852-3eca4670b6e3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:48:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"9d09a821-c7fc-4b76-8852-3eca4670b6e3","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995368","transactionId":"1d862d2f-3516-502d-883f-4c3c85b12e2c","quoteStatus":"Draft","message":"Quote# Q-995368 status changed to Draft.","modifiedAt":"2025-08-11T08:48:41.182Z"},"publishedAt":"2025-08-11T08:48:41.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:48:43] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:48:43
[webhook] [2025-08-11 08:48:43] [adwsapi_v2.php:36]  Provided signature: sha256=9e29846ab18de5ad5369704a427cf3a0d6419b37aaffc477031ddb44fd51fdfa
[webhook] [2025-08-11 08:48:43] [adwsapi_v2.php:37]  Calculated signature: sha256=9e29846ab18de5ad5369704a427cf3a0d6419b37aaffc477031ddb44fd51fdfa
[webhook] [2025-08-11 08:48:43] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0d077e2718b08360\n    [X-B3-Traceid] => 6899ae699d10a531395139f400b20528\n    [B3] => 6899ae699d10a531395139f400b20528-0d077e2718b08360-1\n    [Traceparent] => 00-6899ae699d10a531395139f400b20528-0d077e2718b08360-01\n    [X-Amzn-Trace-Id] => Root=1-6899ae69-9d10a531395139f400b20528;Parent=0d077e2718b08360;Sampled=1\n    [X-Adsk-Signature] => sha256=9e29846ab18de5ad5369704a427cf3a0d6419b37aaffc477031ddb44fd51fdfa\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9d09a821-c7fc-4b76-8852-3eca4670b6e3\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:48:43] [adwsapi_v2.php:57]  Received webhook data: {"id":"9d09a821-c7fc-4b76-8852-3eca4670b6e3","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995368","transactionId":"1d862d2f-3516-502d-883f-4c3c85b12e2c","quoteStatus":"Draft","message":"Quote# Q-995368 status changed to Draft.","modifiedAt":"2025-08-11T08:48:41.182Z"},"publishedAt":"2025-08-11T08:48:41.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:58:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:58:08
[webhook] [2025-08-11 08:58:08] [adwsapi_v2.php:36]  Provided signature: sha256=9b1f23cdcf34eeb83b9f5314a71886d801a0db18ed844d0247d9c09959200cbc
[webhook] [2025-08-11 08:58:08] [adwsapi_v2.php:37]  Calculated signature: sha256=62e148ace7b3e5cf307bc8c0da97e6282cc029213c9de5a7056cbbbd9eda717a
[webhook] [2025-08-11 08:58:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a41c92023c7222ee\n    [X-B3-Traceid] => 6899b09d328a2bbeb2ee81608d3cf374\n    [B3] => 6899b09d328a2bbeb2ee81608d3cf374-a41c92023c7222ee-1\n    [Traceparent] => 00-6899b09d328a2bbeb2ee81608d3cf374-a41c92023c7222ee-01\n    [X-Amzn-Trace-Id] => Root=1-6899b09d-328a2bbeb2ee81608d3cf374;Parent=a41c92023c7222ee;Sampled=1\n    [X-Adsk-Signature] => sha256=9b1f23cdcf34eeb83b9f5314a71886d801a0db18ed844d0247d9c09959200cbc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2c0de1e2-6868-4960-bdf5-075d7b312023\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:58:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"2c0de1e2-6868-4960-bdf5-075d7b312023","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995368","transactionId":"1d862d2f-3516-502d-883f-4c3c85b12e2c","quoteStatus":"Quoted","message":"Quote# Q-995368 status changed to Quoted.","modifiedAt":"2025-08-11T08:58:05.586Z"},"publishedAt":"2025-08-11T08:58:05.000Z","csn":"5103159758"}
[webhook] [2025-08-11 08:58:08] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 08:58:08
[webhook] [2025-08-11 08:58:08] [adwsapi_v2.php:36]  Provided signature: sha256=62e148ace7b3e5cf307bc8c0da97e6282cc029213c9de5a7056cbbbd9eda717a
[webhook] [2025-08-11 08:58:08] [adwsapi_v2.php:37]  Calculated signature: sha256=62e148ace7b3e5cf307bc8c0da97e6282cc029213c9de5a7056cbbbd9eda717a
[webhook] [2025-08-11 08:58:08] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8a43297b15d674d9\n    [X-B3-Traceid] => 6899b09d328a2bbeb2ee81608d3cf374\n    [B3] => 6899b09d328a2bbeb2ee81608d3cf374-8a43297b15d674d9-1\n    [Traceparent] => 00-6899b09d328a2bbeb2ee81608d3cf374-8a43297b15d674d9-01\n    [X-Amzn-Trace-Id] => Root=1-6899b09d-328a2bbeb2ee81608d3cf374;Parent=8a43297b15d674d9;Sampled=1\n    [X-Adsk-Signature] => sha256=62e148ace7b3e5cf307bc8c0da97e6282cc029213c9de5a7056cbbbd9eda717a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 2c0de1e2-6868-4960-bdf5-075d7b312023\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 08:58:08] [adwsapi_v2.php:57]  Received webhook data: {"id":"2c0de1e2-6868-4960-bdf5-075d7b312023","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995368","transactionId":"1d862d2f-3516-502d-883f-4c3c85b12e2c","quoteStatus":"Quoted","message":"Quote# Q-995368 status changed to Quoted.","modifiedAt":"2025-08-11T08:58:05.586Z"},"publishedAt":"2025-08-11T08:58:05.000Z","csn":"5103159758"}
[webhook] [2025-08-11 09:22:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 09:22:47
[webhook] [2025-08-11 09:22:47] [adwsapi_v2.php:36]  Provided signature: sha256=19a8f6f586f7d7e57eb96330b59172e55c3b668bf88eed4174e2872f124a9645
[webhook] [2025-08-11 09:22:47] [adwsapi_v2.php:37]  Calculated signature: sha256=7b37b21318e61028b00d0b64bf5853a777f8a19ffa68ea5e39ad3cfe331d74a1
[webhook] [2025-08-11 09:22:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8316a67005fc33b1\n    [X-B3-Traceid] => 6899b66423fa04bbbe4f5199a50ca5eb\n    [B3] => 6899b66423fa04bbbe4f5199a50ca5eb-8316a67005fc33b1-1\n    [Traceparent] => 00-6899b66423fa04bbbe4f5199a50ca5eb-8316a67005fc33b1-01\n    [X-Amzn-Trace-Id] => Root=1-6899b664-23fa04bbbe4f5199a50ca5eb;Parent=8316a67005fc33b1;Sampled=1\n    [X-Adsk-Signature] => sha256=19a8f6f586f7d7e57eb96330b59172e55c3b668bf88eed4174e2872f124a9645\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 54f625f3-4e54-49dc-a2be-fd7958dd5d97\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 09:22:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"54f625f3-4e54-49dc-a2be-fd7958dd5d97","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995460","transactionId":"c0894c4c-dae9-5350-af28-2d5f6272ab73","quoteStatus":"Draft","message":"Quote# Q-995460 status changed to Draft.","modifiedAt":"2025-08-11T09:22:44.591Z"},"publishedAt":"2025-08-11T09:22:45.000Z","csn":"5103159758"}
[webhook] [2025-08-11 09:22:47] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 09:22:47
[webhook] [2025-08-11 09:22:47] [adwsapi_v2.php:36]  Provided signature: sha256=7b37b21318e61028b00d0b64bf5853a777f8a19ffa68ea5e39ad3cfe331d74a1
[webhook] [2025-08-11 09:22:47] [adwsapi_v2.php:37]  Calculated signature: sha256=7b37b21318e61028b00d0b64bf5853a777f8a19ffa68ea5e39ad3cfe331d74a1
[webhook] [2025-08-11 09:22:47] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 94d9dfb7655cc3b7\n    [X-B3-Traceid] => 6899b66423fa04bbbe4f5199a50ca5eb\n    [B3] => 6899b66423fa04bbbe4f5199a50ca5eb-94d9dfb7655cc3b7-1\n    [Traceparent] => 00-6899b66423fa04bbbe4f5199a50ca5eb-94d9dfb7655cc3b7-01\n    [X-Amzn-Trace-Id] => Root=1-6899b664-23fa04bbbe4f5199a50ca5eb;Parent=94d9dfb7655cc3b7;Sampled=1\n    [X-Adsk-Signature] => sha256=7b37b21318e61028b00d0b64bf5853a777f8a19ffa68ea5e39ad3cfe331d74a1\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 54f625f3-4e54-49dc-a2be-fd7958dd5d97\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 09:22:47] [adwsapi_v2.php:57]  Received webhook data: {"id":"54f625f3-4e54-49dc-a2be-fd7958dd5d97","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995460","transactionId":"c0894c4c-dae9-5350-af28-2d5f6272ab73","quoteStatus":"Draft","message":"Quote# Q-995460 status changed to Draft.","modifiedAt":"2025-08-11T09:22:44.591Z"},"publishedAt":"2025-08-11T09:22:45.000Z","csn":"5103159758"}
[webhook] [2025-08-11 10:44:10] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 10:44:10
[webhook] [2025-08-11 10:44:10] [adwsapi_v2.php:36]  Provided signature: sha256=0e332fe5ab85737c94e050f3b8cd7b4e40349191ae2514e39689e8eb273b6d2a
[webhook] [2025-08-11 10:44:10] [adwsapi_v2.php:37]  Calculated signature: sha256=69f75b9eeb9b9b7be5ac92ed0ffc8ec2aa30a534b27a555012cd00fb14bc8cd8
[webhook] [2025-08-11 10:44:10] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 0decc37a631564cb\n    [X-B3-Traceid] => 6899c9776603177f18d3c4a93eeb1865\n    [B3] => 6899c9776603177f18d3c4a93eeb1865-0decc37a631564cb-1\n    [Traceparent] => 00-6899c9776603177f18d3c4a93eeb1865-0decc37a631564cb-01\n    [X-Amzn-Trace-Id] => Root=1-6899c977-6603177f18d3c4a93eeb1865;Parent=0decc37a631564cb;Sampled=1\n    [X-Adsk-Signature] => sha256=0e332fe5ab85737c94e050f3b8cd7b4e40349191ae2514e39689e8eb273b6d2a\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 85c2508c-fa32-484b-819c-bf166072b6b9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 10:44:10] [adwsapi_v2.php:57]  Received webhook data: {"id":"85c2508c-fa32-484b-819c-bf166072b6b9","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72371865859320","status":"Active","quantity":1,"endDate":"2026-08-14","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-11T09:23:17.000+0000"},"publishedAt":"2025-08-11T10:44:07.000Z","csn":"5103159758"}
[webhook] [2025-08-11 10:44:10] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 10:44:10
[webhook] [2025-08-11 10:44:10] [adwsapi_v2.php:36]  Provided signature: sha256=69f75b9eeb9b9b7be5ac92ed0ffc8ec2aa30a534b27a555012cd00fb14bc8cd8
[webhook] [2025-08-11 10:44:10] [adwsapi_v2.php:37]  Calculated signature: sha256=69f75b9eeb9b9b7be5ac92ed0ffc8ec2aa30a534b27a555012cd00fb14bc8cd8
[webhook] [2025-08-11 10:44:10] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8978c87555b1e6f3\n    [X-B3-Traceid] => 6899c9776603177f18d3c4a93eeb1865\n    [B3] => 6899c9776603177f18d3c4a93eeb1865-8978c87555b1e6f3-1\n    [Traceparent] => 00-6899c9776603177f18d3c4a93eeb1865-8978c87555b1e6f3-01\n    [X-Amzn-Trace-Id] => Root=1-6899c977-6603177f18d3c4a93eeb1865;Parent=8978c87555b1e6f3;Sampled=1\n    [X-Adsk-Signature] => sha256=69f75b9eeb9b9b7be5ac92ed0ffc8ec2aa30a534b27a555012cd00fb14bc8cd8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 85c2508c-fa32-484b-819c-bf166072b6b9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 10:44:10] [adwsapi_v2.php:57]  Received webhook data: {"id":"85c2508c-fa32-484b-819c-bf166072b6b9","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72371865859320","status":"Active","quantity":1,"endDate":"2026-08-14","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-11T09:23:17.000+0000"},"publishedAt":"2025-08-11T10:44:07.000Z","csn":"5103159758"}
[webhook] [2025-08-11 10:51:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 10:51:20
[webhook] [2025-08-11 10:51:20] [adwsapi_v2.php:36]  Provided signature: sha256=55f45dad0da2c9bce7e20c6c8ee8786dbe23f01f7271425a1fbd24ca0dea4a05
[webhook] [2025-08-11 10:51:20] [adwsapi_v2.php:37]  Calculated signature: sha256=5621595b9f8353c8821fd6e729c0ddba6325cd4a6bb3538ab2ee906f7fed98ac
[webhook] [2025-08-11 10:51:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 7f8f951b85e8fec5\n    [X-B3-Traceid] => 6899cb2511903fb46f04df00408c2a9c\n    [B3] => 6899cb2511903fb46f04df00408c2a9c-7f8f951b85e8fec5-1\n    [Traceparent] => 00-6899cb2511903fb46f04df00408c2a9c-7f8f951b85e8fec5-01\n    [X-Amzn-Trace-Id] => Root=1-6899cb25-11903fb46f04df00408c2a9c;Parent=7f8f951b85e8fec5;Sampled=1\n    [X-Adsk-Signature] => sha256=55f45dad0da2c9bce7e20c6c8ee8786dbe23f01f7271425a1fbd24ca0dea4a05\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 93c40c42-94f5-469a-8f28-01484c9bcac9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 10:51:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"93c40c42-94f5-469a-8f28-01484c9bcac9","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995659","transactionId":"68e0f623-77f1-5c14-b23d-9730baa61934","quoteStatus":"Draft","message":"Quote# Q-995659 status changed to Draft.","modifiedAt":"2025-08-11T10:51:17.778Z"},"publishedAt":"2025-08-11T10:51:18.000Z","csn":"5103159758"}
[webhook] [2025-08-11 10:51:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 10:51:20
[webhook] [2025-08-11 10:51:20] [adwsapi_v2.php:36]  Provided signature: sha256=5621595b9f8353c8821fd6e729c0ddba6325cd4a6bb3538ab2ee906f7fed98ac
[webhook] [2025-08-11 10:51:20] [adwsapi_v2.php:37]  Calculated signature: sha256=5621595b9f8353c8821fd6e729c0ddba6325cd4a6bb3538ab2ee906f7fed98ac
[webhook] [2025-08-11 10:51:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a03175ff3360dc4b\n    [X-B3-Traceid] => 6899cb2511903fb46f04df00408c2a9c\n    [B3] => 6899cb2511903fb46f04df00408c2a9c-a03175ff3360dc4b-1\n    [Traceparent] => 00-6899cb2511903fb46f04df00408c2a9c-a03175ff3360dc4b-01\n    [X-Amzn-Trace-Id] => Root=1-6899cb25-11903fb46f04df00408c2a9c;Parent=a03175ff3360dc4b;Sampled=1\n    [X-Adsk-Signature] => sha256=5621595b9f8353c8821fd6e729c0ddba6325cd4a6bb3538ab2ee906f7fed98ac\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 93c40c42-94f5-469a-8f28-01484c9bcac9\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 10:51:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"93c40c42-94f5-469a-8f28-01484c9bcac9","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995659","transactionId":"68e0f623-77f1-5c14-b23d-9730baa61934","quoteStatus":"Draft","message":"Quote# Q-995659 status changed to Draft.","modifiedAt":"2025-08-11T10:51:17.778Z"},"publishedAt":"2025-08-11T10:51:18.000Z","csn":"5103159758"}
[webhook] [2025-08-11 10:52:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 10:52:29
[webhook] [2025-08-11 10:52:29] [adwsapi_v2.php:36]  Provided signature: sha256=a90617cc477f14239c2d027c47213d5da5c48c5bb4a3ab2b90f0ba0eb11dabde
[webhook] [2025-08-11 10:52:29] [adwsapi_v2.php:37]  Calculated signature: sha256=a90617cc477f14239c2d027c47213d5da5c48c5bb4a3ab2b90f0ba0eb11dabde
[webhook] [2025-08-11 10:52:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cafaa39184aac084\n    [X-B3-Traceid] => 6899cb6b335d5efd0089294ae56f5e04\n    [B3] => 6899cb6b335d5efd0089294ae56f5e04-cafaa39184aac084-1\n    [Traceparent] => 00-6899cb6b335d5efd0089294ae56f5e04-cafaa39184aac084-01\n    [X-Amzn-Trace-Id] => Root=1-6899cb6b-335d5efd0089294ae56f5e04;Parent=cafaa39184aac084;Sampled=1\n    [X-Adsk-Signature] => sha256=a90617cc477f14239c2d027c47213d5da5c48c5bb4a3ab2b90f0ba0eb11dabde\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8565ffac-3133-4d5a-817c-6c75b0ee2f17\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 10:52:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"8565ffac-3133-4d5a-817c-6c75b0ee2f17","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995659","transactionId":"68e0f623-77f1-5c14-b23d-9730baa61934","quoteStatus":"Quoted","message":"Quote# Q-995659 status changed to Quoted.","modifiedAt":"2025-08-11T10:52:26.745Z"},"publishedAt":"2025-08-11T10:52:27.000Z","csn":"5103159758"}
[webhook] [2025-08-11 10:52:29] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 10:52:29
[webhook] [2025-08-11 10:52:29] [adwsapi_v2.php:36]  Provided signature: sha256=e383e1328bf42abe76309baf5885541e2a91789ee7d4416562cc0685e37ef506
[webhook] [2025-08-11 10:52:29] [adwsapi_v2.php:37]  Calculated signature: sha256=a90617cc477f14239c2d027c47213d5da5c48c5bb4a3ab2b90f0ba0eb11dabde
[webhook] [2025-08-11 10:52:29] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8caea8350bf415f4\n    [X-B3-Traceid] => 6899cb6b335d5efd0089294ae56f5e04\n    [B3] => 6899cb6b335d5efd0089294ae56f5e04-8caea8350bf415f4-1\n    [Traceparent] => 00-6899cb6b335d5efd0089294ae56f5e04-8caea8350bf415f4-01\n    [X-Amzn-Trace-Id] => Root=1-6899cb6b-335d5efd0089294ae56f5e04;Parent=8caea8350bf415f4;Sampled=1\n    [X-Adsk-Signature] => sha256=e383e1328bf42abe76309baf5885541e2a91789ee7d4416562cc0685e37ef506\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8565ffac-3133-4d5a-817c-6c75b0ee2f17\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 10:52:29] [adwsapi_v2.php:57]  Received webhook data: {"id":"8565ffac-3133-4d5a-817c-6c75b0ee2f17","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995659","transactionId":"68e0f623-77f1-5c14-b23d-9730baa61934","quoteStatus":"Quoted","message":"Quote# Q-995659 status changed to Quoted.","modifiedAt":"2025-08-11T10:52:26.745Z"},"publishedAt":"2025-08-11T10:52:27.000Z","csn":"5103159758"}
[webhook] [2025-08-11 10:52:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 10:52:48
[webhook] [2025-08-11 10:52:48] [adwsapi_v2.php:36]  Provided signature: sha256=aa034a532dacd4b29c2e677e983f9b34ecad773d8a207326d4c094ee0c823a9b
[webhook] [2025-08-11 10:52:48] [adwsapi_v2.php:37]  Calculated signature: sha256=1f7972648404730c5a081fda4bb3ea2f3ea423283c4ced7a597cbc711c02d41b
[webhook] [2025-08-11 10:52:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5b67689f6f1f0464\n    [X-B3-Traceid] => 6899cb7e2caafa281c7e55a8ac1550e3\n    [B3] => 6899cb7e2caafa281c7e55a8ac1550e3-5b67689f6f1f0464-1\n    [Traceparent] => 00-6899cb7e2caafa281c7e55a8ac1550e3-5b67689f6f1f0464-01\n    [X-Amzn-Trace-Id] => Root=1-6899cb7e-2caafa281c7e55a8ac1550e3;Parent=5b67689f6f1f0464;Sampled=1\n    [X-Adsk-Signature] => sha256=aa034a532dacd4b29c2e677e983f9b34ecad773d8a207326d4c094ee0c823a9b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9988795c-fa02-4e5d-9514-7b64356b75e8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 10:52:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"9988795c-fa02-4e5d-9514-7b64356b75e8","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995669","transactionId":"dec5934c-7e7e-5c66-aa46-f25fb3c6a9a3","quoteStatus":"Draft","message":"Quote# Q-995669 status changed to Draft.","modifiedAt":"2025-08-11T10:52:45.676Z"},"publishedAt":"2025-08-11T10:52:46.000Z","csn":"5103159758"}
[webhook] [2025-08-11 10:52:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 10:52:48
[webhook] [2025-08-11 10:52:48] [adwsapi_v2.php:36]  Provided signature: sha256=1f7972648404730c5a081fda4bb3ea2f3ea423283c4ced7a597cbc711c02d41b
[webhook] [2025-08-11 10:52:48] [adwsapi_v2.php:37]  Calculated signature: sha256=1f7972648404730c5a081fda4bb3ea2f3ea423283c4ced7a597cbc711c02d41b
[webhook] [2025-08-11 10:52:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 96198b8970c1273d\n    [X-B3-Traceid] => 6899cb7e2caafa281c7e55a8ac1550e3\n    [B3] => 6899cb7e2caafa281c7e55a8ac1550e3-96198b8970c1273d-1\n    [Traceparent] => 00-6899cb7e2caafa281c7e55a8ac1550e3-96198b8970c1273d-01\n    [X-Amzn-Trace-Id] => Root=1-6899cb7e-2caafa281c7e55a8ac1550e3;Parent=96198b8970c1273d;Sampled=1\n    [X-Adsk-Signature] => sha256=1f7972648404730c5a081fda4bb3ea2f3ea423283c4ced7a597cbc711c02d41b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 9988795c-fa02-4e5d-9514-7b64356b75e8\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 10:52:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"9988795c-fa02-4e5d-9514-7b64356b75e8","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995669","transactionId":"dec5934c-7e7e-5c66-aa46-f25fb3c6a9a3","quoteStatus":"Draft","message":"Quote# Q-995669 status changed to Draft.","modifiedAt":"2025-08-11T10:52:45.676Z"},"publishedAt":"2025-08-11T10:52:46.000Z","csn":"5103159758"}
[webhook] [2025-08-11 10:53:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 10:53:58
[webhook] [2025-08-11 10:53:58] [adwsapi_v2.php:36]  Provided signature: sha256=888621a462c8a53352971443529ddb315023272797e74d4b418ddedf16fefc0e
[webhook] [2025-08-11 10:53:58] [adwsapi_v2.php:37]  Calculated signature: sha256=05861197fd7dab301b981ed930ce584d6ec899833a494e788178d9aa8080af0f
[webhook] [2025-08-11 10:53:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 8fe50befd317c37e\n    [X-B3-Traceid] => 6899cbc407066682efc75d52fd4f955e\n    [B3] => 6899cbc407066682efc75d52fd4f955e-8fe50befd317c37e-1\n    [Traceparent] => 00-6899cbc407066682efc75d52fd4f955e-8fe50befd317c37e-01\n    [X-Amzn-Trace-Id] => Root=1-6899cbc4-07066682efc75d52fd4f955e;Parent=8fe50befd317c37e;Sampled=1\n    [X-Adsk-Signature] => sha256=888621a462c8a53352971443529ddb315023272797e74d4b418ddedf16fefc0e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8a93599c-dab4-4d5b-8cf0-489327029379\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 10:53:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"8a93599c-dab4-4d5b-8cf0-489327029379","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995669","transactionId":"dec5934c-7e7e-5c66-aa46-f25fb3c6a9a3","quoteStatus":"Quoted","message":"Quote# Q-995669 status changed to Quoted.","modifiedAt":"2025-08-11T10:53:55.588Z"},"publishedAt":"2025-08-11T10:53:56.000Z","csn":"5103159758"}
[webhook] [2025-08-11 10:53:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 10:53:58
[webhook] [2025-08-11 10:53:58] [adwsapi_v2.php:36]  Provided signature: sha256=05861197fd7dab301b981ed930ce584d6ec899833a494e788178d9aa8080af0f
[webhook] [2025-08-11 10:53:58] [adwsapi_v2.php:37]  Calculated signature: sha256=05861197fd7dab301b981ed930ce584d6ec899833a494e788178d9aa8080af0f
[webhook] [2025-08-11 10:53:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5f5135a542e04b23\n    [X-B3-Traceid] => 6899cbc407066682efc75d52fd4f955e\n    [B3] => 6899cbc407066682efc75d52fd4f955e-5f5135a542e04b23-1\n    [Traceparent] => 00-6899cbc407066682efc75d52fd4f955e-5f5135a542e04b23-01\n    [X-Amzn-Trace-Id] => Root=1-6899cbc4-07066682efc75d52fd4f955e;Parent=5f5135a542e04b23;Sampled=1\n    [X-Adsk-Signature] => sha256=05861197fd7dab301b981ed930ce584d6ec899833a494e788178d9aa8080af0f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8a93599c-dab4-4d5b-8cf0-489327029379\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 10:53:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"8a93599c-dab4-4d5b-8cf0-489327029379","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995669","transactionId":"dec5934c-7e7e-5c66-aa46-f25fb3c6a9a3","quoteStatus":"Quoted","message":"Quote# Q-995669 status changed to Quoted.","modifiedAt":"2025-08-11T10:53:55.588Z"},"publishedAt":"2025-08-11T10:53:56.000Z","csn":"5103159758"}
[webhook] [2025-08-11 12:03:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 12:03:49
[webhook] [2025-08-11 12:03:49] [adwsapi_v2.php:36]  Provided signature: sha256=9c2b8151428cc44f78666d92df112f2513f8c101894231179c014e4c575d35f4
[webhook] [2025-08-11 12:03:49] [adwsapi_v2.php:37]  Calculated signature: sha256=3cca8845265ff85ab903268b08c9823784545626222858217dd77a84a7cc38ff
[webhook] [2025-08-11 12:03:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 326ddd608db88dd2\n    [X-B3-Traceid] => 6899dc23bc819576c555c0eafc4f99a2\n    [B3] => 6899dc23bc819576c555c0eafc4f99a2-326ddd608db88dd2-1\n    [Traceparent] => 00-6899dc23bc819576c555c0eafc4f99a2-326ddd608db88dd2-01\n    [X-Amzn-Trace-Id] => Root=1-6899dc23-bc819576c555c0eafc4f99a2;Parent=326ddd608db88dd2;Sampled=1\n    [X-Adsk-Signature] => sha256=9c2b8151428cc44f78666d92df112f2513f8c101894231179c014e4c575d35f4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 23c27420-10ed-4305-aa38-589151379e18\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 12:03:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"23c27420-10ed-4305-aa38-589151379e18","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995862","transactionId":"1d338cb2-a9f8-5a9c-a396-eda17798a6a2","quoteStatus":"Draft","message":"Quote# Q-995862 status changed to Draft.","modifiedAt":"2025-08-11T12:03:46.999Z"},"publishedAt":"2025-08-11T12:03:47.000Z","csn":"5103159758"}
[webhook] [2025-08-11 12:03:49] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 12:03:49
[webhook] [2025-08-11 12:03:49] [adwsapi_v2.php:36]  Provided signature: sha256=3cca8845265ff85ab903268b08c9823784545626222858217dd77a84a7cc38ff
[webhook] [2025-08-11 12:03:49] [adwsapi_v2.php:37]  Calculated signature: sha256=3cca8845265ff85ab903268b08c9823784545626222858217dd77a84a7cc38ff
[webhook] [2025-08-11 12:03:49] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e6a53e9d4bc8db5f\n    [X-B3-Traceid] => 6899dc23bc819576c555c0eafc4f99a2\n    [B3] => 6899dc23bc819576c555c0eafc4f99a2-e6a53e9d4bc8db5f-1\n    [Traceparent] => 00-6899dc23bc819576c555c0eafc4f99a2-e6a53e9d4bc8db5f-01\n    [X-Amzn-Trace-Id] => Root=1-6899dc23-bc819576c555c0eafc4f99a2;Parent=e6a53e9d4bc8db5f;Sampled=1\n    [X-Adsk-Signature] => sha256=3cca8845265ff85ab903268b08c9823784545626222858217dd77a84a7cc38ff\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 23c27420-10ed-4305-aa38-589151379e18\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 12:03:49] [adwsapi_v2.php:57]  Received webhook data: {"id":"23c27420-10ed-4305-aa38-589151379e18","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995862","transactionId":"1d338cb2-a9f8-5a9c-a396-eda17798a6a2","quoteStatus":"Draft","message":"Quote# Q-995862 status changed to Draft.","modifiedAt":"2025-08-11T12:03:46.999Z"},"publishedAt":"2025-08-11T12:03:47.000Z","csn":"5103159758"}
[webhook] [2025-08-11 12:13:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 12:13:54
[webhook] [2025-08-11 12:13:54] [adwsapi_v2.php:36]  Provided signature: sha256=c26b8bab46a75d0fac022c36421cb4cf4248e06088ec9b4607478fe5afb2341e
[webhook] [2025-08-11 12:13:54] [adwsapi_v2.php:37]  Calculated signature: sha256=c26b8bab46a75d0fac022c36421cb4cf4248e06088ec9b4607478fe5afb2341e
[webhook] [2025-08-11 12:13:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c0fc124a19a83eba\n    [X-B3-Traceid] => 6899de803c56d598557da1ec74f2bd76\n    [B3] => 6899de803c56d598557da1ec74f2bd76-c0fc124a19a83eba-1\n    [Traceparent] => 00-6899de803c56d598557da1ec74f2bd76-c0fc124a19a83eba-01\n    [X-Amzn-Trace-Id] => Root=1-6899de80-3c56d598557da1ec74f2bd76;Parent=c0fc124a19a83eba;Sampled=1\n    [X-Adsk-Signature] => sha256=c26b8bab46a75d0fac022c36421cb4cf4248e06088ec9b4607478fe5afb2341e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 881e625a-18cf-44aa-be63-5627b13ae50a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 12:13:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"881e625a-18cf-44aa-be63-5627b13ae50a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995894","transactionId":"2f51d254-9ac5-5f87-85c5-b3477a6b0207","quoteStatus":"Draft","message":"Quote# Q-995894 status changed to Draft.","modifiedAt":"2025-08-11T12:13:51.697Z"},"publishedAt":"2025-08-11T12:13:52.000Z","csn":"5103159758"}
[webhook] [2025-08-11 12:13:54] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 12:13:54
[webhook] [2025-08-11 12:13:54] [adwsapi_v2.php:36]  Provided signature: sha256=23ff5052117d2d254abae047d061d6e2f7b0c4a825aa7a9a0ca0f175fdba566f
[webhook] [2025-08-11 12:13:54] [adwsapi_v2.php:37]  Calculated signature: sha256=c26b8bab46a75d0fac022c36421cb4cf4248e06088ec9b4607478fe5afb2341e
[webhook] [2025-08-11 12:13:54] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 547c2f9110c7f87d\n    [X-B3-Traceid] => 6899de803c56d598557da1ec74f2bd76\n    [B3] => 6899de803c56d598557da1ec74f2bd76-547c2f9110c7f87d-1\n    [Traceparent] => 00-6899de803c56d598557da1ec74f2bd76-547c2f9110c7f87d-01\n    [X-Amzn-Trace-Id] => Root=1-6899de80-3c56d598557da1ec74f2bd76;Parent=547c2f9110c7f87d;Sampled=1\n    [X-Adsk-Signature] => sha256=23ff5052117d2d254abae047d061d6e2f7b0c4a825aa7a9a0ca0f175fdba566f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 881e625a-18cf-44aa-be63-5627b13ae50a\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 12:13:54] [adwsapi_v2.php:57]  Received webhook data: {"id":"881e625a-18cf-44aa-be63-5627b13ae50a","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995894","transactionId":"2f51d254-9ac5-5f87-85c5-b3477a6b0207","quoteStatus":"Draft","message":"Quote# Q-995894 status changed to Draft.","modifiedAt":"2025-08-11T12:13:51.697Z"},"publishedAt":"2025-08-11T12:13:52.000Z","csn":"5103159758"}
[webhook] [2025-08-11 12:57:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 12:57:15
[webhook] [2025-08-11 12:57:15] [adwsapi_v2.php:36]  Provided signature: sha256=15ee9cb45ef4b86474ff238d73529176c650bfc962f69772868409ddb6b0c453
[webhook] [2025-08-11 12:57:15] [adwsapi_v2.php:37]  Calculated signature: sha256=d5b680437e456eb5a69e2e128e6786f77c731835321a44368c914cb0383574b9
[webhook] [2025-08-11 12:57:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d386b08d2db7dc35\n    [X-B3-Traceid] => 6899e8a84ab9834a2e1872ec2a89e203\n    [B3] => 6899e8a84ab9834a2e1872ec2a89e203-d386b08d2db7dc35-1\n    [Traceparent] => 00-6899e8a84ab9834a2e1872ec2a89e203-d386b08d2db7dc35-01\n    [X-Amzn-Trace-Id] => Root=1-6899e8a8-4ab9834a2e1872ec2a89e203;Parent=d386b08d2db7dc35;Sampled=1\n    [X-Adsk-Signature] => sha256=15ee9cb45ef4b86474ff238d73529176c650bfc962f69772868409ddb6b0c453\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f014dcac-8b29-49dc-880e-5864e86eb00c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 12:57:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"f014dcac-8b29-49dc-880e-5864e86eb00c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72371865859320","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-11T11:20:48.000+0000"},"publishedAt":"2025-08-11T12:57:12.000Z","csn":"5103159758"}
[webhook] [2025-08-11 12:57:15] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 12:57:15
[webhook] [2025-08-11 12:57:15] [adwsapi_v2.php:36]  Provided signature: sha256=d5b680437e456eb5a69e2e128e6786f77c731835321a44368c914cb0383574b9
[webhook] [2025-08-11 12:57:15] [adwsapi_v2.php:37]  Calculated signature: sha256=d5b680437e456eb5a69e2e128e6786f77c731835321a44368c914cb0383574b9
[webhook] [2025-08-11 12:57:15] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 05bf888eb295b7b8\n    [X-B3-Traceid] => 6899e8a84ab9834a2e1872ec2a89e203\n    [B3] => 6899e8a84ab9834a2e1872ec2a89e203-05bf888eb295b7b8-1\n    [Traceparent] => 00-6899e8a84ab9834a2e1872ec2a89e203-05bf888eb295b7b8-01\n    [X-Amzn-Trace-Id] => Root=1-6899e8a8-4ab9834a2e1872ec2a89e203;Parent=05bf888eb295b7b8;Sampled=1\n    [X-Adsk-Signature] => sha256=d5b680437e456eb5a69e2e128e6786f77c731835321a44368c914cb0383574b9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => f014dcac-8b29-49dc-880e-5864e86eb00c\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 12:57:15] [adwsapi_v2.php:57]  Received webhook data: {"id":"f014dcac-8b29-49dc-880e-5864e86eb00c","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72371865859320","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-11T11:20:48.000+0000"},"publishedAt":"2025-08-11T12:57:12.000Z","csn":"5103159758"}
[webhook] [2025-08-11 13:01:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 13:01:51
[webhook] [2025-08-11 13:01:51] [adwsapi_v2.php:36]  Provided signature: sha256=4307834dcefa7ffabf2828ffc3b9bdd52d566a0df643c1635fa453d291d5fd64
[webhook] [2025-08-11 13:01:51] [adwsapi_v2.php:37]  Calculated signature: sha256=333228b5ac63e823907fc9f22987bec1d226e6a7df38b3bf0679b680f12dd111
[webhook] [2025-08-11 13:01:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 67f3b1d119ff1261\n    [X-B3-Traceid] => 6899e9bd53276df9dca1f6876e15fb74\n    [B3] => 6899e9bd53276df9dca1f6876e15fb74-67f3b1d119ff1261-1\n    [Traceparent] => 00-6899e9bd53276df9dca1f6876e15fb74-67f3b1d119ff1261-01\n    [X-Amzn-Trace-Id] => Root=1-6899e9bd-53276df9dca1f6876e15fb74;Parent=67f3b1d119ff1261;Sampled=1\n    [X-Adsk-Signature] => sha256=4307834dcefa7ffabf2828ffc3b9bdd52d566a0df643c1635fa453d291d5fd64\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ff02652a-3401-49b4-a10e-47e6f15a978e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 13:01:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"ff02652a-3401-49b4-a10e-47e6f15a978e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-996066","transactionId":"e37097b2-c81f-5cb4-8a01-f6a123f3cc5e","quoteStatus":"Draft","message":"Quote# Q-996066 status changed to Draft.","modifiedAt":"2025-08-11T13:01:48.954Z"},"publishedAt":"2025-08-11T13:01:49.000Z","csn":"5103159758"}
[webhook] [2025-08-11 13:01:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 13:01:51
[webhook] [2025-08-11 13:01:51] [adwsapi_v2.php:36]  Provided signature: sha256=333228b5ac63e823907fc9f22987bec1d226e6a7df38b3bf0679b680f12dd111
[webhook] [2025-08-11 13:01:51] [adwsapi_v2.php:37]  Calculated signature: sha256=333228b5ac63e823907fc9f22987bec1d226e6a7df38b3bf0679b680f12dd111
[webhook] [2025-08-11 13:01:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d452a4f8364f27eb\n    [X-B3-Traceid] => 6899e9bd53276df9dca1f6876e15fb74\n    [B3] => 6899e9bd53276df9dca1f6876e15fb74-d452a4f8364f27eb-1\n    [Traceparent] => 00-6899e9bd53276df9dca1f6876e15fb74-d452a4f8364f27eb-01\n    [X-Amzn-Trace-Id] => Root=1-6899e9bd-53276df9dca1f6876e15fb74;Parent=d452a4f8364f27eb;Sampled=1\n    [X-Adsk-Signature] => sha256=333228b5ac63e823907fc9f22987bec1d226e6a7df38b3bf0679b680f12dd111\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => ff02652a-3401-49b4-a10e-47e6f15a978e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 13:01:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"ff02652a-3401-49b4-a10e-47e6f15a978e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-996066","transactionId":"e37097b2-c81f-5cb4-8a01-f6a123f3cc5e","quoteStatus":"Draft","message":"Quote# Q-996066 status changed to Draft.","modifiedAt":"2025-08-11T13:01:48.954Z"},"publishedAt":"2025-08-11T13:01:49.000Z","csn":"5103159758"}
[webhook] [2025-08-11 13:03:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 13:03:02
[webhook] [2025-08-11 13:03:02] [adwsapi_v2.php:36]  Provided signature: sha256=21ab11ce282d07fea3e96a4c60d77e2d77776b18e990d5795f0851c2f55a9f2f
[webhook] [2025-08-11 13:03:02] [adwsapi_v2.php:37]  Calculated signature: sha256=21ab11ce282d07fea3e96a4c60d77e2d77776b18e990d5795f0851c2f55a9f2f
[webhook] [2025-08-11 13:03:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 235ba6e5801875dc\n    [X-B3-Traceid] => 6899ea0412d8a3784d88f473a463b899\n    [B3] => 6899ea0412d8a3784d88f473a463b899-235ba6e5801875dc-1\n    [Traceparent] => 00-6899ea0412d8a3784d88f473a463b899-235ba6e5801875dc-01\n    [X-Amzn-Trace-Id] => Root=1-6899ea04-12d8a3784d88f473a463b899;Parent=235ba6e5801875dc;Sampled=1\n    [X-Adsk-Signature] => sha256=21ab11ce282d07fea3e96a4c60d77e2d77776b18e990d5795f0851c2f55a9f2f\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 20fc231d-f902-45c4-a735-bf8540c828c1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 13:03:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"20fc231d-f902-45c4-a735-bf8540c828c1","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-996066","transactionId":"e37097b2-c81f-5cb4-8a01-f6a123f3cc5e","quoteStatus":"Quoted","message":"Quote# Q-996066 status changed to Quoted.","modifiedAt":"2025-08-11T13:03:00.153Z"},"publishedAt":"2025-08-11T13:03:00.000Z","csn":"5103159758"}
[webhook] [2025-08-11 13:03:02] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 13:03:02
[webhook] [2025-08-11 13:03:02] [adwsapi_v2.php:36]  Provided signature: sha256=bd39ce0bfb0e697ee7d374a825261f945d7b8e0ba9524dda2b4227c1b93739fe
[webhook] [2025-08-11 13:03:02] [adwsapi_v2.php:37]  Calculated signature: sha256=21ab11ce282d07fea3e96a4c60d77e2d77776b18e990d5795f0851c2f55a9f2f
[webhook] [2025-08-11 13:03:02] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c23b43e2caa97cdf\n    [X-B3-Traceid] => 6899ea0412d8a3784d88f473a463b899\n    [B3] => 6899ea0412d8a3784d88f473a463b899-c23b43e2caa97cdf-1\n    [Traceparent] => 00-6899ea0412d8a3784d88f473a463b899-c23b43e2caa97cdf-01\n    [X-Amzn-Trace-Id] => Root=1-6899ea04-12d8a3784d88f473a463b899;Parent=c23b43e2caa97cdf;Sampled=1\n    [X-Adsk-Signature] => sha256=bd39ce0bfb0e697ee7d374a825261f945d7b8e0ba9524dda2b4227c1b93739fe\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 20fc231d-f902-45c4-a735-bf8540c828c1\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 13:03:02] [adwsapi_v2.php:57]  Received webhook data: {"id":"20fc231d-f902-45c4-a735-bf8540c828c1","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-996066","transactionId":"e37097b2-c81f-5cb4-8a01-f6a123f3cc5e","quoteStatus":"Quoted","message":"Quote# Q-996066 status changed to Quoted.","modifiedAt":"2025-08-11T13:03:00.153Z"},"publishedAt":"2025-08-11T13:03:00.000Z","csn":"5103159758"}
[webhook] [2025-08-11 15:14:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 15:14:58
[webhook] [2025-08-11 15:14:58] [adwsapi_v2.php:36]  Provided signature: sha256=e62acb7607438a975cc4036a9510bde18f0bf44e6b38278cf1525c273ef5899e
[webhook] [2025-08-11 15:14:58] [adwsapi_v2.php:37]  Calculated signature: sha256=fec8c4826226b7b67389b6d11baf84e221f531bff3487103a8c2ec2b6873cf0d
[webhook] [2025-08-11 15:14:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a7d890a1790e910a\n    [X-B3-Traceid] => 689a08ef48be830d44ee63abebb72e57\n    [B3] => 689a08ef48be830d44ee63abebb72e57-a7d890a1790e910a-1\n    [Traceparent] => 00-689a08ef48be830d44ee63abebb72e57-a7d890a1790e910a-01\n    [X-Amzn-Trace-Id] => Root=1-689a08ef-48be830d44ee63abebb72e57;Parent=a7d890a1790e910a;Sampled=1\n    [X-Adsk-Signature] => sha256=e62acb7607438a975cc4036a9510bde18f0bf44e6b38278cf1525c273ef5899e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bd91c689-45f3-47b8-957a-38a7988bee13\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 15:14:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"bd91c689-45f3-47b8-957a-38a7988bee13","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-996534","transactionId":"0ccab044-0a90-5bed-8b5b-d150018ff67c","quoteStatus":"Draft","message":"Quote# Q-996534 status changed to Draft.","modifiedAt":"2025-08-11T15:14:55.093Z"},"publishedAt":"2025-08-11T15:14:55.000Z","csn":"5103159758"}
[webhook] [2025-08-11 15:14:58] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 15:14:58
[webhook] [2025-08-11 15:14:58] [adwsapi_v2.php:36]  Provided signature: sha256=fec8c4826226b7b67389b6d11baf84e221f531bff3487103a8c2ec2b6873cf0d
[webhook] [2025-08-11 15:14:58] [adwsapi_v2.php:37]  Calculated signature: sha256=fec8c4826226b7b67389b6d11baf84e221f531bff3487103a8c2ec2b6873cf0d
[webhook] [2025-08-11 15:14:58] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b7527a480516d0f1\n    [X-B3-Traceid] => 689a08ef48be830d44ee63abebb72e57\n    [B3] => 689a08ef48be830d44ee63abebb72e57-b7527a480516d0f1-1\n    [Traceparent] => 00-689a08ef48be830d44ee63abebb72e57-b7527a480516d0f1-01\n    [X-Amzn-Trace-Id] => Root=1-689a08ef-48be830d44ee63abebb72e57;Parent=b7527a480516d0f1;Sampled=1\n    [X-Adsk-Signature] => sha256=fec8c4826226b7b67389b6d11baf84e221f531bff3487103a8c2ec2b6873cf0d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => bd91c689-45f3-47b8-957a-38a7988bee13\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 15:14:58] [adwsapi_v2.php:57]  Received webhook data: {"id":"bd91c689-45f3-47b8-957a-38a7988bee13","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-996534","transactionId":"0ccab044-0a90-5bed-8b5b-d150018ff67c","quoteStatus":"Draft","message":"Quote# Q-996534 status changed to Draft.","modifiedAt":"2025-08-11T15:14:55.093Z"},"publishedAt":"2025-08-11T15:14:55.000Z","csn":"5103159758"}
[webhook] [2025-08-11 15:15:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 15:15:50
[webhook] [2025-08-11 15:15:50] [adwsapi_v2.php:36]  Provided signature: sha256=20c566621f5e9154966b73cb5746b6afcada263261e554a516d5d7a60cf35d3b
[webhook] [2025-08-11 15:15:50] [adwsapi_v2.php:37]  Calculated signature: sha256=0fdad3c11425dfde168f8a1dad1c73d1c13c87ae2ba9bf0b32b5050e9e6057f9
[webhook] [2025-08-11 15:15:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => ba2b18455e57b851\n    [X-B3-Traceid] => 689a09235a42ad4784cca608f4542343\n    [B3] => 689a09235a42ad4784cca608f4542343-ba2b18455e57b851-1\n    [Traceparent] => 00-689a09235a42ad4784cca608f4542343-ba2b18455e57b851-01\n    [X-Amzn-Trace-Id] => Root=1-689a0923-5a42ad4784cca608f4542343;Parent=ba2b18455e57b851;Sampled=1\n    [X-Adsk-Signature] => sha256=20c566621f5e9154966b73cb5746b6afcada263261e554a516d5d7a60cf35d3b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cf6bad92-4c88-4d8f-8dc2-0cadcc79f758\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 15:15:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"cf6bad92-4c88-4d8f-8dc2-0cadcc79f758","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-996534","transactionId":"0ccab044-0a90-5bed-8b5b-d150018ff67c","quoteStatus":"Quoted","message":"Quote# Q-996534 status changed to Quoted.","modifiedAt":"2025-08-11T15:15:47.551Z"},"publishedAt":"2025-08-11T15:15:47.000Z","csn":"5103159758"}
[webhook] [2025-08-11 15:15:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 15:15:50
[webhook] [2025-08-11 15:15:50] [adwsapi_v2.php:36]  Provided signature: sha256=0fdad3c11425dfde168f8a1dad1c73d1c13c87ae2ba9bf0b32b5050e9e6057f9
[webhook] [2025-08-11 15:15:50] [adwsapi_v2.php:37]  Calculated signature: sha256=0fdad3c11425dfde168f8a1dad1c73d1c13c87ae2ba9bf0b32b5050e9e6057f9
[webhook] [2025-08-11 15:15:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b5e260587bf4187e\n    [X-B3-Traceid] => 689a09235a42ad4784cca608f4542343\n    [B3] => 689a09235a42ad4784cca608f4542343-b5e260587bf4187e-1\n    [Traceparent] => 00-689a09235a42ad4784cca608f4542343-b5e260587bf4187e-01\n    [X-Amzn-Trace-Id] => Root=1-689a0923-5a42ad4784cca608f4542343;Parent=b5e260587bf4187e;Sampled=1\n    [X-Adsk-Signature] => sha256=0fdad3c11425dfde168f8a1dad1c73d1c13c87ae2ba9bf0b32b5050e9e6057f9\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => cf6bad92-4c88-4d8f-8dc2-0cadcc79f758\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 15:15:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"cf6bad92-4c88-4d8f-8dc2-0cadcc79f758","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-996534","transactionId":"0ccab044-0a90-5bed-8b5b-d150018ff67c","quoteStatus":"Quoted","message":"Quote# Q-996534 status changed to Quoted.","modifiedAt":"2025-08-11T15:15:47.551Z"},"publishedAt":"2025-08-11T15:15:47.000Z","csn":"5103159758"}
[webhook] [2025-08-11 16:07:05] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 16:07:05
[webhook] [2025-08-11 16:07:05] [adwsapi_v2.php:36]  Provided signature: sha256=b3e23ef3f96c1d8c563b8e3bd291874dc9e41f24009a853db129ed4a7b1d5cfc
[webhook] [2025-08-11 16:07:05] [adwsapi_v2.php:37]  Calculated signature: sha256=ebba16658d3c71c63b7ab6ed3a097d8bbc9cbb39f4574615e2df5f7be19a010d
[webhook] [2025-08-11 16:07:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 27053b1ade0a35b9\n    [X-B3-Traceid] => 689a15275c55ab130716b3c350e2b140\n    [B3] => 689a15275c55ab130716b3c350e2b140-27053b1ade0a35b9-1\n    [Traceparent] => 00-689a15275c55ab130716b3c350e2b140-27053b1ade0a35b9-01\n    [X-Amzn-Trace-Id] => Root=1-689a1527-5c55ab130716b3c350e2b140;Parent=27053b1ade0a35b9;Sampled=1\n    [X-Adsk-Signature] => sha256=b3e23ef3f96c1d8c563b8e3bd291874dc9e41f24009a853db129ed4a7b1d5cfc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754928423196-72371865859320\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 16:07:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754928423196-72371865859320","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72371865859320","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-11T16:07:03.196Z"},"publishedAt":"2025-08-11T16:07:03.000Z","csn":"5103159758"}
[webhook] [2025-08-11 16:07:05] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 16:07:05
[webhook] [2025-08-11 16:07:05] [adwsapi_v2.php:36]  Provided signature: sha256=ebba16658d3c71c63b7ab6ed3a097d8bbc9cbb39f4574615e2df5f7be19a010d
[webhook] [2025-08-11 16:07:05] [adwsapi_v2.php:37]  Calculated signature: sha256=ebba16658d3c71c63b7ab6ed3a097d8bbc9cbb39f4574615e2df5f7be19a010d
[webhook] [2025-08-11 16:07:05] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d2bf32010f733019\n    [X-B3-Traceid] => 689a15275c55ab130716b3c350e2b140\n    [B3] => 689a15275c55ab130716b3c350e2b140-d2bf32010f733019-1\n    [Traceparent] => 00-689a15275c55ab130716b3c350e2b140-d2bf32010f733019-01\n    [X-Amzn-Trace-Id] => Root=1-689a1527-5c55ab130716b3c350e2b140;Parent=d2bf32010f733019;Sampled=1\n    [X-Adsk-Signature] => sha256=ebba16658d3c71c63b7ab6ed3a097d8bbc9cbb39f4574615e2df5f7be19a010d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754928423196-72371865859320\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 16:07:05] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754928423196-72371865859320","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72371865859320","paymentStatus":"UNPAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-11T16:07:03.196Z"},"publishedAt":"2025-08-11T16:07:03.000Z","csn":"5103159758"}
[webhook] [2025-08-11 17:28:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 17:28:51
[webhook] [2025-08-11 17:28:51] [adwsapi_v2.php:36]  Provided signature: sha256=1f55bdd14ac271afb8814cf4129d471ab6caef108a9ca9d22a15d3a570c02fb2
[webhook] [2025-08-11 17:28:51] [adwsapi_v2.php:37]  Calculated signature: sha256=1f55bdd14ac271afb8814cf4129d471ab6caef108a9ca9d22a15d3a570c02fb2
[webhook] [2025-08-11 17:28:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b5683ae4a0fe9e49\n    [X-B3-Traceid] => 689a28509a9cbc355b04e9ca8fa09686\n    [B3] => 689a28509a9cbc355b04e9ca8fa09686-b5683ae4a0fe9e49-1\n    [Traceparent] => 00-689a28509a9cbc355b04e9ca8fa09686-b5683ae4a0fe9e49-01\n    [X-Amzn-Trace-Id] => Root=1-689a2850-9a9cbc355b04e9ca8fa09686;Parent=b5683ae4a0fe9e49;Sampled=1\n    [X-Adsk-Signature] => sha256=1f55bdd14ac271afb8814cf4129d471ab6caef108a9ca9d22a15d3a570c02fb2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 10c71e38-c787-4d5a-98c0-01095fe1d859\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 17:28:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"10c71e38-c787-4d5a-98c0-01095fe1d859","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995260","transactionId":"b4a3e69d-df50-52b5-8bc0-878e76068777","quoteStatus":"Order Submitted","message":"Quote# Q-995260 status changed to Order Submitted.","modifiedAt":"2025-08-11T17:28:48.531Z"},"publishedAt":"2025-08-11T17:28:49.000Z","csn":"5103159758"}
[webhook] [2025-08-11 17:28:51] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 17:28:51
[webhook] [2025-08-11 17:28:51] [adwsapi_v2.php:36]  Provided signature: sha256=d576f5f3c4653780620c658ba148fda55331380a487d3df1674a20af87eadcf3
[webhook] [2025-08-11 17:28:51] [adwsapi_v2.php:37]  Calculated signature: sha256=1f55bdd14ac271afb8814cf4129d471ab6caef108a9ca9d22a15d3a570c02fb2
[webhook] [2025-08-11 17:28:51] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2ddf080178a36833\n    [X-B3-Traceid] => 689a28509a9cbc355b04e9ca8fa09686\n    [B3] => 689a28509a9cbc355b04e9ca8fa09686-2ddf080178a36833-1\n    [Traceparent] => 00-689a28509a9cbc355b04e9ca8fa09686-2ddf080178a36833-01\n    [X-Amzn-Trace-Id] => Root=1-689a2850-9a9cbc355b04e9ca8fa09686;Parent=2ddf080178a36833;Sampled=1\n    [X-Adsk-Signature] => sha256=d576f5f3c4653780620c658ba148fda55331380a487d3df1674a20af87eadcf3\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 10c71e38-c787-4d5a-98c0-01095fe1d859\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 17:28:51] [adwsapi_v2.php:57]  Received webhook data: {"id":"10c71e38-c787-4d5a-98c0-01095fe1d859","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995260","transactionId":"b4a3e69d-df50-52b5-8bc0-878e76068777","quoteStatus":"Order Submitted","message":"Quote# Q-995260 status changed to Order Submitted.","modifiedAt":"2025-08-11T17:28:48.531Z"},"publishedAt":"2025-08-11T17:28:49.000Z","csn":"5103159758"}
[webhook] [2025-08-11 17:28:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 17:28:53
[webhook] [2025-08-11 17:28:53] [adwsapi_v2.php:36]  Provided signature: sha256=fb77e69761e1729dee82723d1b9635954f2cea22f278bb04ddd787b4e795ce9d
[webhook] [2025-08-11 17:28:53] [adwsapi_v2.php:37]  Calculated signature: sha256=fb77e69761e1729dee82723d1b9635954f2cea22f278bb04ddd787b4e795ce9d
[webhook] [2025-08-11 17:28:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c842b8cf02f52f8b\n    [X-B3-Traceid] => 689a285251ce8af95c34f8dbe7b1cfbf\n    [B3] => 689a285251ce8af95c34f8dbe7b1cfbf-c842b8cf02f52f8b-1\n    [Traceparent] => 00-689a285251ce8af95c34f8dbe7b1cfbf-c842b8cf02f52f8b-01\n    [X-Amzn-Trace-Id] => Root=1-689a2852-51ce8af95c34f8dbe7b1cfbf;Parent=c842b8cf02f52f8b;Sampled=1\n    [X-Adsk-Signature] => sha256=fb77e69761e1729dee82723d1b9635954f2cea22f278bb04ddd787b4e795ce9d\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5e89e7a9-d783-4c7e-9de8-2bce3a653812\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 17:28:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"5e89e7a9-d783-4c7e-9de8-2bce3a653812","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995260","transactionId":"b4a3e69d-df50-52b5-8bc0-878e76068777","quoteStatus":"Ordered","message":"Quote# Q-995260 status changed to Ordered.","modifiedAt":"2025-08-11T17:28:50.517Z"},"publishedAt":"2025-08-11T17:28:50.000Z","csn":"5103159758"}
[webhook] [2025-08-11 17:28:53] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 17:28:53
[webhook] [2025-08-11 17:28:53] [adwsapi_v2.php:36]  Provided signature: sha256=d350eac5edb865ecc1c9aadae9e140cce0529a36edfd373ea7b487dbb82fadb4
[webhook] [2025-08-11 17:28:53] [adwsapi_v2.php:37]  Calculated signature: sha256=fb77e69761e1729dee82723d1b9635954f2cea22f278bb04ddd787b4e795ce9d
[webhook] [2025-08-11 17:28:53] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 287db8bf45c0ca27\n    [X-B3-Traceid] => 689a285251ce8af95c34f8dbe7b1cfbf\n    [B3] => 689a285251ce8af95c34f8dbe7b1cfbf-287db8bf45c0ca27-1\n    [Traceparent] => 00-689a285251ce8af95c34f8dbe7b1cfbf-287db8bf45c0ca27-01\n    [X-Amzn-Trace-Id] => Root=1-689a2852-51ce8af95c34f8dbe7b1cfbf;Parent=287db8bf45c0ca27;Sampled=1\n    [X-Adsk-Signature] => sha256=d350eac5edb865ecc1c9aadae9e140cce0529a36edfd373ea7b487dbb82fadb4\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 5e89e7a9-d783-4c7e-9de8-2bce3a653812\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 17:28:53] [adwsapi_v2.php:57]  Received webhook data: {"id":"5e89e7a9-d783-4c7e-9de8-2bce3a653812","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-995260","transactionId":"b4a3e69d-df50-52b5-8bc0-878e76068777","quoteStatus":"Ordered","message":"Quote# Q-995260 status changed to Ordered.","modifiedAt":"2025-08-11T17:28:50.517Z"},"publishedAt":"2025-08-11T17:28:50.000Z","csn":"5103159758"}
[webhook] [2025-08-11 18:34:35] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 18:34:35
[webhook] [2025-08-11 18:34:35] [adwsapi_v2.php:36]  Provided signature: sha256=7958ad4dc6c45d1dfcd7b7b5691e93d6f14530ad4a05a7aed140c13f7945bb13
[webhook] [2025-08-11 18:34:35] [adwsapi_v2.php:37]  Calculated signature: sha256=7958ad4dc6c45d1dfcd7b7b5691e93d6f14530ad4a05a7aed140c13f7945bb13
[webhook] [2025-08-11 18:34:35] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a45fac0520e492f1\n    [X-B3-Traceid] => 689a37b900b610623b711fa26d95b5b7\n    [B3] => 689a37b900b610623b711fa26d95b5b7-a45fac0520e492f1-1\n    [Traceparent] => 00-689a37b900b610623b711fa26d95b5b7-a45fac0520e492f1-01\n    [X-Amzn-Trace-Id] => Root=1-689a37b9-00b610623b711fa26d95b5b7;Parent=a45fac0520e492f1;Sampled=1\n    [X-Adsk-Signature] => sha256=7958ad4dc6c45d1dfcd7b7b5691e93d6f14530ad4a05a7aed140c13f7945bb13\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1f606064-1a15-42e0-9fa7-14d356641c52\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 18:34:35] [adwsapi_v2.php:57]  Received webhook data: {"id":"1f606064-1a15-42e0-9fa7-14d356641c52","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69226925631631","status":"Active","quantity":1,"endDate":"2026-08-16","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-11T17:28:58.000+0000"},"publishedAt":"2025-08-11T18:34:33.000Z","csn":"5103159758"}
[webhook] [2025-08-11 18:34:36] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 18:34:36
[webhook] [2025-08-11 18:34:36] [adwsapi_v2.php:36]  Provided signature: sha256=08b6081a19674c44767040008c21851c9c682997db7b2cadac1f40c6a1115ff2
[webhook] [2025-08-11 18:34:36] [adwsapi_v2.php:37]  Calculated signature: sha256=7958ad4dc6c45d1dfcd7b7b5691e93d6f14530ad4a05a7aed140c13f7945bb13
[webhook] [2025-08-11 18:34:36] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cbd5c8f14739623c\n    [X-B3-Traceid] => 689a37b900b610623b711fa26d95b5b7\n    [B3] => 689a37b900b610623b711fa26d95b5b7-cbd5c8f14739623c-1\n    [Traceparent] => 00-689a37b900b610623b711fa26d95b5b7-cbd5c8f14739623c-01\n    [X-Amzn-Trace-Id] => Root=1-689a37b9-00b610623b711fa26d95b5b7;Parent=cbd5c8f14739623c;Sampled=1\n    [X-Adsk-Signature] => sha256=08b6081a19674c44767040008c21851c9c682997db7b2cadac1f40c6a1115ff2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1f606064-1a15-42e0-9fa7-14d356641c52\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 18:34:36] [adwsapi_v2.php:57]  Received webhook data: {"id":"1f606064-1a15-42e0-9fa7-14d356641c52","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69226925631631","status":"Active","quantity":1,"endDate":"2026-08-16","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-11T17:28:58.000+0000"},"publishedAt":"2025-08-11T18:34:33.000Z","csn":"5103159758"}
[webhook] [2025-08-11 19:51:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 19:51:41
[webhook] [2025-08-11 19:51:41] [adwsapi_v2.php:36]  Provided signature: sha256=bb80f8c483e2f427f854aaa11674661b957e192280c59117a3544b4c63da1512
[webhook] [2025-08-11 19:51:41] [adwsapi_v2.php:37]  Calculated signature: sha256=e9869bdaa4863dbd47f293f55958041c5ba30a05c33c989b6ae16eb16b96cb83
[webhook] [2025-08-11 19:51:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4b26b3c3b7aa3eec\n    [X-B3-Traceid] => 689a49cb594edfa46814fc501c803c5a\n    [B3] => 689a49cb594edfa46814fc501c803c5a-4b26b3c3b7aa3eec-1\n    [Traceparent] => 00-689a49cb594edfa46814fc501c803c5a-4b26b3c3b7aa3eec-01\n    [X-Amzn-Trace-Id] => Root=1-689a49cb-594edfa46814fc501c803c5a;Parent=4b26b3c3b7aa3eec;Sampled=1\n    [X-Adsk-Signature] => sha256=bb80f8c483e2f427f854aaa11674661b957e192280c59117a3544b4c63da1512\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8d16e00c-35f6-4321-aa13-3c56b7b97511\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 19:51:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"8d16e00c-35f6-4321-aa13-3c56b7b97511","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75492703551740","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-11T19:14:02.000+0000"},"publishedAt":"2025-08-11T19:51:39.000Z","csn":"5103159758"}
[webhook] [2025-08-11 19:51:41] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 19:51:41
[webhook] [2025-08-11 19:51:41] [adwsapi_v2.php:36]  Provided signature: sha256=e9869bdaa4863dbd47f293f55958041c5ba30a05c33c989b6ae16eb16b96cb83
[webhook] [2025-08-11 19:51:41] [adwsapi_v2.php:37]  Calculated signature: sha256=e9869bdaa4863dbd47f293f55958041c5ba30a05c33c989b6ae16eb16b96cb83
[webhook] [2025-08-11 19:51:41] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => cb8a60b5cc851fb3\n    [X-B3-Traceid] => 689a49cb594edfa46814fc501c803c5a\n    [B3] => 689a49cb594edfa46814fc501c803c5a-cb8a60b5cc851fb3-1\n    [Traceparent] => 00-689a49cb594edfa46814fc501c803c5a-cb8a60b5cc851fb3-01\n    [X-Amzn-Trace-Id] => Root=1-689a49cb-594edfa46814fc501c803c5a;Parent=cb8a60b5cc851fb3;Sampled=1\n    [X-Adsk-Signature] => sha256=e9869bdaa4863dbd47f293f55958041c5ba30a05c33c989b6ae16eb16b96cb83\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 8d16e00c-35f6-4321-aa13-3c56b7b97511\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 19:51:41] [adwsapi_v2.php:57]  Received webhook data: {"id":"8d16e00c-35f6-4321-aa13-3c56b7b97511","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75492703551740","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-11T19:14:02.000+0000"},"publishedAt":"2025-08-11T19:51:39.000Z","csn":"5103159758"}
[webhook] [2025-08-11 20:12:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 20:12:04
[webhook] [2025-08-11 20:12:04] [adwsapi_v2.php:36]  Provided signature: sha256=d98ba15c0494899cf3dc3ba092010973adb7ccfb14d81e3042145456c4f15c24
[webhook] [2025-08-11 20:12:04] [adwsapi_v2.php:37]  Calculated signature: sha256=14fb26d46fafec182a699bbdf3f9b7346810e0e916d471a2387fc4d44d4e89ff
[webhook] [2025-08-11 20:12:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5c277c493a5d787a\n    [X-B3-Traceid] => 689a4e92476349bc0f2cbfdc5b4b3471\n    [B3] => 689a4e92476349bc0f2cbfdc5b4b3471-5c277c493a5d787a-1\n    [Traceparent] => 00-689a4e92476349bc0f2cbfdc5b4b3471-5c277c493a5d787a-01\n    [X-Amzn-Trace-Id] => Root=1-689a4e92-476349bc0f2cbfdc5b4b3471;Parent=5c277c493a5d787a;Sampled=1\n    [X-Adsk-Signature] => sha256=d98ba15c0494899cf3dc3ba092010973adb7ccfb14d81e3042145456c4f15c24\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 57d01d3b-80e3-4929-bee0-b9db299a2450\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 20:12:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"57d01d3b-80e3-4929-bee0-b9db299a2450","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69226925631631","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-11T19:11:29.000+0000"},"publishedAt":"2025-08-11T20:12:02.000Z","csn":"5103159758"}
[webhook] [2025-08-11 20:12:04] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 20:12:04
[webhook] [2025-08-11 20:12:04] [adwsapi_v2.php:36]  Provided signature: sha256=14fb26d46fafec182a699bbdf3f9b7346810e0e916d471a2387fc4d44d4e89ff
[webhook] [2025-08-11 20:12:04] [adwsapi_v2.php:37]  Calculated signature: sha256=14fb26d46fafec182a699bbdf3f9b7346810e0e916d471a2387fc4d44d4e89ff
[webhook] [2025-08-11 20:12:04] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => de870ae41781df04\n    [X-B3-Traceid] => 689a4e92476349bc0f2cbfdc5b4b3471\n    [B3] => 689a4e92476349bc0f2cbfdc5b4b3471-de870ae41781df04-1\n    [Traceparent] => 00-689a4e92476349bc0f2cbfdc5b4b3471-de870ae41781df04-01\n    [X-Amzn-Trace-Id] => Root=1-689a4e92-476349bc0f2cbfdc5b4b3471;Parent=de870ae41781df04;Sampled=1\n    [X-Adsk-Signature] => sha256=14fb26d46fafec182a699bbdf3f9b7346810e0e916d471a2387fc4d44d4e89ff\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 57d01d3b-80e3-4929-bee0-b9db299a2450\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 20:12:04] [adwsapi_v2.php:57]  Received webhook data: {"id":"57d01d3b-80e3-4929-bee0-b9db299a2450","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69226925631631","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-11T19:11:29.000+0000"},"publishedAt":"2025-08-11T20:12:02.000Z","csn":"5103159758"}
[webhook] [2025-08-11 20:16:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 20:16:00
[webhook] [2025-08-11 20:16:00] [adwsapi_v2.php:36]  Provided signature: sha256=b5616a22f9f7b37389d5b897b3b1c13fd6824b1bcec80c391ea0f12030c80e5e
[webhook] [2025-08-11 20:16:00] [adwsapi_v2.php:37]  Calculated signature: sha256=b5616a22f9f7b37389d5b897b3b1c13fd6824b1bcec80c391ea0f12030c80e5e
[webhook] [2025-08-11 20:16:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 89233848a51f7fe7\n    [X-B3-Traceid] => 689a4f7e0b3242db18fca76c4568c0e0\n    [B3] => 689a4f7e0b3242db18fca76c4568c0e0-89233848a51f7fe7-1\n    [Traceparent] => 00-689a4f7e0b3242db18fca76c4568c0e0-89233848a51f7fe7-01\n    [X-Amzn-Trace-Id] => Root=1-689a4f7e-0b3242db18fca76c4568c0e0;Parent=89233848a51f7fe7;Sampled=1\n    [X-Adsk-Signature] => sha256=b5616a22f9f7b37389d5b897b3b1c13fd6824b1bcec80c391ea0f12030c80e5e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754943358487-72371865859320\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 20:16:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754943358487-72371865859320","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72371865859320","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-11T20:15:58.487Z"},"publishedAt":"2025-08-11T20:15:58.000Z","csn":"5103159758"}
[webhook] [2025-08-11 20:16:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 20:16:00
[webhook] [2025-08-11 20:16:00] [adwsapi_v2.php:36]  Provided signature: sha256=f59ecc9dff1dcc716840bbb0c0fa84bfa18a2b6d4f669686cd733da13d95ed48
[webhook] [2025-08-11 20:16:00] [adwsapi_v2.php:37]  Calculated signature: sha256=b5616a22f9f7b37389d5b897b3b1c13fd6824b1bcec80c391ea0f12030c80e5e
[webhook] [2025-08-11 20:16:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => b5b766327ad1857c\n    [X-B3-Traceid] => 689a4f7e0b3242db18fca76c4568c0e0\n    [B3] => 689a4f7e0b3242db18fca76c4568c0e0-b5b766327ad1857c-1\n    [Traceparent] => 00-689a4f7e0b3242db18fca76c4568c0e0-b5b766327ad1857c-01\n    [X-Amzn-Trace-Id] => Root=1-689a4f7e-0b3242db18fca76c4568c0e0;Parent=b5b766327ad1857c;Sampled=1\n    [X-Adsk-Signature] => sha256=f59ecc9dff1dcc716840bbb0c0fa84bfa18a2b6d4f669686cd733da13d95ed48\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754943358487-72371865859320\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 20:16:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754943358487-72371865859320","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"72371865859320","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-11T20:15:58.487Z"},"publishedAt":"2025-08-11T20:15:58.000Z","csn":"5103159758"}
[webhook] [2025-08-11 20:16:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 20:16:30
[webhook] [2025-08-11 20:16:30] [adwsapi_v2.php:36]  Provided signature: sha256=ba96e47cb2e8ba6fe2aa492f7e2b19b4558275b22afcb2aec75fa01dcea6a516
[webhook] [2025-08-11 20:16:30] [adwsapi_v2.php:37]  Calculated signature: sha256=d6302e76162b0fdad999daae75622a00327921c32cdab55bf4eba202aa82d334
[webhook] [2025-08-11 20:16:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c01b41e4e745a120\n    [X-B3-Traceid] => 689a4f9b6d67613022e412007fc76b6e\n    [B3] => 689a4f9b6d67613022e412007fc76b6e-c01b41e4e745a120-1\n    [Traceparent] => 00-689a4f9b6d67613022e412007fc76b6e-c01b41e4e745a120-01\n    [X-Amzn-Trace-Id] => Root=1-689a4f9b-6d67613022e412007fc76b6e;Parent=c01b41e4e745a120;Sampled=1\n    [X-Adsk-Signature] => sha256=ba96e47cb2e8ba6fe2aa492f7e2b19b4558275b22afcb2aec75fa01dcea6a516\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 86526cb0-7150-4e4d-a636-3ca86348be83\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 20:16:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"86526cb0-7150-4e4d-a636-3ca86348be83","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75492745675479","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-11T19:14:02.000+0000"},"publishedAt":"2025-08-11T20:16:28.000Z","csn":"5103159758"}
[webhook] [2025-08-11 20:16:30] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 20:16:30
[webhook] [2025-08-11 20:16:30] [adwsapi_v2.php:36]  Provided signature: sha256=d6302e76162b0fdad999daae75622a00327921c32cdab55bf4eba202aa82d334
[webhook] [2025-08-11 20:16:30] [adwsapi_v2.php:37]  Calculated signature: sha256=d6302e76162b0fdad999daae75622a00327921c32cdab55bf4eba202aa82d334
[webhook] [2025-08-11 20:16:30] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c26c0c2e7aca488c\n    [X-B3-Traceid] => 689a4f9b6d67613022e412007fc76b6e\n    [B3] => 689a4f9b6d67613022e412007fc76b6e-c26c0c2e7aca488c-1\n    [Traceparent] => 00-689a4f9b6d67613022e412007fc76b6e-c26c0c2e7aca488c-01\n    [X-Amzn-Trace-Id] => Root=1-689a4f9b-6d67613022e412007fc76b6e;Parent=c26c0c2e7aca488c;Sampled=1\n    [X-Adsk-Signature] => sha256=d6302e76162b0fdad999daae75622a00327921c32cdab55bf4eba202aa82d334\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 86526cb0-7150-4e4d-a636-3ca86348be83\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 20:16:30] [adwsapi_v2.php:57]  Received webhook data: {"id":"86526cb0-7150-4e4d-a636-3ca86348be83","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75492745675479","quantity":1,"message":"subscription quantity changed.","modifiedAt":"2025-08-11T19:14:02.000+0000"},"publishedAt":"2025-08-11T20:16:28.000Z","csn":"5103159758"}
[webhook] [2025-08-11 22:02:19] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 22:02:19
[webhook] [2025-08-11 22:02:19] [adwsapi_v2.php:36]  Provided signature: sha256=4a9e749db81010ce829a8f737be3714dce543e845b2a0081006ec2a6bcd643ea
[webhook] [2025-08-11 22:02:19] [adwsapi_v2.php:37]  Calculated signature: sha256=687e627c5fbdcd1fb9922119dd570d82ad3ebae2ff8ad75b72ca0280a3ae60e8
[webhook] [2025-08-11 22:02:19] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => a03c26a185b88528\n    [X-B3-Traceid] => 689a68690dd9a7a1633a944c216bb44d\n    [B3] => 689a68690dd9a7a1633a944c216bb44d-a03c26a185b88528-1\n    [Traceparent] => 00-689a68690dd9a7a1633a944c216bb44d-a03c26a185b88528-01\n    [X-Amzn-Trace-Id] => Root=1-689a6869-0dd9a7a1633a944c216bb44d;Parent=a03c26a185b88528;Sampled=1\n    [X-Adsk-Signature] => sha256=4a9e749db81010ce829a8f737be3714dce543e845b2a0081006ec2a6bcd643ea\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754949737523-69226925631631\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 22:02:19] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754949737523-69226925631631","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69226925631631","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-11T22:02:17.523Z"},"publishedAt":"2025-08-11T22:02:17.000Z","csn":"5103159758"}
[webhook] [2025-08-11 22:02:20] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 22:02:20
[webhook] [2025-08-11 22:02:20] [adwsapi_v2.php:36]  Provided signature: sha256=687e627c5fbdcd1fb9922119dd570d82ad3ebae2ff8ad75b72ca0280a3ae60e8
[webhook] [2025-08-11 22:02:20] [adwsapi_v2.php:37]  Calculated signature: sha256=687e627c5fbdcd1fb9922119dd570d82ad3ebae2ff8ad75b72ca0280a3ae60e8
[webhook] [2025-08-11 22:02:20] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 1d7dabd9d9c40e62\n    [X-B3-Traceid] => 689a68690dd9a7a1633a944c216bb44d\n    [B3] => 689a68690dd9a7a1633a944c216bb44d-1d7dabd9d9c40e62-1\n    [Traceparent] => 00-689a68690dd9a7a1633a944c216bb44d-1d7dabd9d9c40e62-01\n    [X-Amzn-Trace-Id] => Root=1-689a6869-0dd9a7a1633a944c216bb44d;Parent=1d7dabd9d9c40e62;Sampled=1\n    [X-Adsk-Signature] => sha256=687e627c5fbdcd1fb9922119dd570d82ad3ebae2ff8ad75b72ca0280a3ae60e8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754949737523-69226925631631\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 22:02:20] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754949737523-69226925631631","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69226925631631","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-11T22:02:17.523Z"},"publishedAt":"2025-08-11T22:02:17.000Z","csn":"5103159758"}
[webhook] [2025-08-11 22:03:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 22:03:25
[webhook] [2025-08-11 22:03:25] [adwsapi_v2.php:36]  Provided signature: sha256=36babf7f8aa13bd0d533dbaa4369610622fe4321981da273cb6b7052f9baf490
[webhook] [2025-08-11 22:03:25] [adwsapi_v2.php:37]  Calculated signature: sha256=4e97c1a097367f7415d9124458a1c4f395815291b3d2bda972a073468cf71c9e
[webhook] [2025-08-11 22:03:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f6be51eca5cfde13\n    [X-B3-Traceid] => 689a68ab45184ef62f4d4cd226f26dad\n    [B3] => 689a68ab45184ef62f4d4cd226f26dad-f6be51eca5cfde13-1\n    [Traceparent] => 00-689a68ab45184ef62f4d4cd226f26dad-f6be51eca5cfde13-01\n    [X-Amzn-Trace-Id] => Root=1-689a68ab-45184ef62f4d4cd226f26dad;Parent=f6be51eca5cfde13;Sampled=1\n    [X-Adsk-Signature] => sha256=36babf7f8aa13bd0d533dbaa4369610622fe4321981da273cb6b7052f9baf490\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754949803185-75492703551740\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 22:03:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754949803185-75492703551740","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75492703551740","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-11T22:03:23.185Z"},"publishedAt":"2025-08-11T22:03:23.000Z","csn":"5103159758"}
[webhook] [2025-08-11 22:03:25] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 22:03:25
[webhook] [2025-08-11 22:03:25] [adwsapi_v2.php:36]  Provided signature: sha256=4e97c1a097367f7415d9124458a1c4f395815291b3d2bda972a073468cf71c9e
[webhook] [2025-08-11 22:03:25] [adwsapi_v2.php:37]  Calculated signature: sha256=4e97c1a097367f7415d9124458a1c4f395815291b3d2bda972a073468cf71c9e
[webhook] [2025-08-11 22:03:25] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 2007e90a58b4b0d6\n    [X-B3-Traceid] => 689a68ab45184ef62f4d4cd226f26dad\n    [B3] => 689a68ab45184ef62f4d4cd226f26dad-2007e90a58b4b0d6-1\n    [Traceparent] => 00-689a68ab45184ef62f4d4cd226f26dad-2007e90a58b4b0d6-01\n    [X-Amzn-Trace-Id] => Root=1-689a68ab-45184ef62f4d4cd226f26dad;Parent=2007e90a58b4b0d6;Sampled=1\n    [X-Adsk-Signature] => sha256=4e97c1a097367f7415d9124458a1c4f395815291b3d2bda972a073468cf71c9e\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754949803185-75492703551740\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 22:03:25] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754949803185-75492703551740","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75492703551740","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-11T22:03:23.185Z"},"publishedAt":"2025-08-11T22:03:23.000Z","csn":"5103159758"}
[webhook] [2025-08-11 22:03:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 22:03:56
[webhook] [2025-08-11 22:03:56] [adwsapi_v2.php:36]  Provided signature: sha256=06f57c3029f6ce16baa43dc7dcbc90cfd46fad68a71ac378e1f8ebd7aac52a7b
[webhook] [2025-08-11 22:03:56] [adwsapi_v2.php:37]  Calculated signature: sha256=06f57c3029f6ce16baa43dc7dcbc90cfd46fad68a71ac378e1f8ebd7aac52a7b
[webhook] [2025-08-11 22:03:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => d307a214a7b077dc\n    [X-B3-Traceid] => 689a68ca447b735714234d0619889e08\n    [B3] => 689a68ca447b735714234d0619889e08-d307a214a7b077dc-1\n    [Traceparent] => 00-689a68ca447b735714234d0619889e08-d307a214a7b077dc-01\n    [X-Amzn-Trace-Id] => Root=1-689a68ca-447b735714234d0619889e08;Parent=d307a214a7b077dc;Sampled=1\n    [X-Adsk-Signature] => sha256=06f57c3029f6ce16baa43dc7dcbc90cfd46fad68a71ac378e1f8ebd7aac52a7b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754949834264-75492745675479\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 22:03:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754949834264-75492745675479","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75492745675479","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-11T22:03:54.264Z"},"publishedAt":"2025-08-11T22:03:54.000Z","csn":"5103159758"}
[webhook] [2025-08-11 22:03:56] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 22:03:56
[webhook] [2025-08-11 22:03:56] [adwsapi_v2.php:36]  Provided signature: sha256=ce6d5fe5240b376452fbdd117f9dc15ca6abc8d02666e5103970d9d12f032d03
[webhook] [2025-08-11 22:03:56] [adwsapi_v2.php:37]  Calculated signature: sha256=06f57c3029f6ce16baa43dc7dcbc90cfd46fad68a71ac378e1f8ebd7aac52a7b
[webhook] [2025-08-11 22:03:56] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bd8f22b1bd8e0981\n    [X-B3-Traceid] => 689a68ca447b735714234d0619889e08\n    [B3] => 689a68ca447b735714234d0619889e08-bd8f22b1bd8e0981-1\n    [Traceparent] => 00-689a68ca447b735714234d0619889e08-bd8f22b1bd8e0981-01\n    [X-Amzn-Trace-Id] => Root=1-689a68ca-447b735714234d0619889e08;Parent=bd8f22b1bd8e0981;Sampled=1\n    [X-Adsk-Signature] => sha256=ce6d5fe5240b376452fbdd117f9dc15ca6abc8d02666e5103970d9d12f032d03\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754949834264-75492745675479\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 22:03:56] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754949834264-75492745675479","topic":"subscription-payment-status-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"75492745675479","paymentStatus":"PAID","message":"Following fields are updated - PAYMENT STATUS","modifiedAt":"2025-08-11T22:03:54.264Z"},"publishedAt":"2025-08-11T22:03:54.000Z","csn":"5103159758"}
[webhook] [2025-08-11 23:04:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 23:04:00
[webhook] [2025-08-11 23:04:00] [adwsapi_v2.php:36]  Provided signature: sha256=5dbc948be86e6148630a6a3c18b17c8c8a74c46e25e23fd0b4f24e686be88e98
[webhook] [2025-08-11 23:04:00] [adwsapi_v2.php:37]  Calculated signature: sha256=5dbc948be86e6148630a6a3c18b17c8c8a74c46e25e23fd0b4f24e686be88e98
[webhook] [2025-08-11 23:04:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => bf51b1adc4f307cc\n    [X-B3-Traceid] => 689a76de1f04d5264535dd537dff4007\n    [B3] => 689a76de1f04d5264535dd537dff4007-bf51b1adc4f307cc-1\n    [Traceparent] => 00-689a76de1f04d5264535dd537dff4007-bf51b1adc4f307cc-01\n    [X-Amzn-Trace-Id] => Root=1-689a76de-1f04d5264535dd537dff4007;Parent=bf51b1adc4f307cc;Sampled=1\n    [X-Adsk-Signature] => sha256=5dbc948be86e6148630a6a3c18b17c8c8a74c46e25e23fd0b4f24e686be88e98\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754953438572-561-63146984\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-11 23:04:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754953438572-561-63146984","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"561-63146984","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-11T23:03:58.572Z"},"publishedAt":"2025-08-11T23:03:58.000Z","csn":"5103159758"}
[webhook] [2025-08-11 23:04:00] [adwsapi_v2.php:20]  Webhook request received at 2025-08-11 23:04:00
[webhook] [2025-08-11 23:04:00] [adwsapi_v2.php:36]  Provided signature: sha256=5f6cb9f66e50251e973645e8bf0c5327bcf4f34e41b541d945b05c45984fee31
[webhook] [2025-08-11 23:04:00] [adwsapi_v2.php:37]  Calculated signature: sha256=5dbc948be86e6148630a6a3c18b17c8c8a74c46e25e23fd0b4f24e686be88e98
[webhook] [2025-08-11 23:04:00] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f3539672747c7b2f\n    [X-B3-Traceid] => 689a76de1f04d5264535dd537dff4007\n    [B3] => 689a76de1f04d5264535dd537dff4007-f3539672747c7b2f-1\n    [Traceparent] => 00-689a76de1f04d5264535dd537dff4007-f3539672747c7b2f-01\n    [X-Amzn-Trace-Id] => Root=1-689a76de-1f04d5264535dd537dff4007;Parent=f3539672747c7b2f;Sampled=1\n    [X-Adsk-Signature] => sha256=5f6cb9f66e50251e973645e8bf0c5327bcf4f34e41b541d945b05c45984fee31\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 1754953438572-561-63146984\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-11 23:04:00] [adwsapi_v2.php:57]  Received webhook data: {"id":"1754953438572-561-63146984","topic":"subscription-change-sku","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionReferenceNumber":"561-63146984","status":"Expired","message":"subscription status changed.","modifiedAt":"2025-08-11T23:03:58.572Z"},"publishedAt":"2025-08-11T23:03:58.000Z","csn":"5103159758"}
[webhook] [2025-08-12 00:00:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-12 00:00:50
[webhook] [2025-08-12 00:00:50] [adwsapi_v2.php:20]  Webhook request received at 2025-08-12 00:00:50
[webhook] [2025-08-12 00:00:50] [adwsapi_v2.php:36]  Provided signature: sha256=b52da71b6c01cac9b0da6efcb70565f9329574dcae60864b2bae0d72bef6cf86
[webhook] [2025-08-12 00:00:50] [adwsapi_v2.php:37]  Calculated signature: sha256=b52da71b6c01cac9b0da6efcb70565f9329574dcae60864b2bae0d72bef6cf86
[webhook] [2025-08-12 00:00:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => e07620c905c0cdf5\n    [X-B3-Traceid] => 689a842f05c70f2f0285437155b9bd12\n    [B3] => 689a842f05c70f2f0285437155b9bd12-e07620c905c0cdf5-1\n    [Traceparent] => 00-689a842f05c70f2f0285437155b9bd12-e07620c905c0cdf5-01\n    [X-Amzn-Trace-Id] => Root=1-689a842f-05c70f2f0285437155b9bd12;Parent=e07620c905c0cdf5;Sampled=1\n    [X-Adsk-Signature] => sha256=b52da71b6c01cac9b0da6efcb70565f9329574dcae60864b2bae0d72bef6cf86\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 41669255-3496-4ee3-9134-a2cb4777e9bb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-12 00:00:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"41669255-3496-4ee3-9134-a2cb4777e9bb","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-12T00:00:47Z"},"publishedAt":"2025-08-12T00:00:48.000Z","country":"GB"}
[webhook] [2025-08-12 00:00:50] [adwsapi_v2.php:36]  Provided signature: sha256=83416783c07530e13b7cbf39bb98e6d54a241a739920ac502dcad4b70b3181a7
[webhook] [2025-08-12 00:00:50] [adwsapi_v2.php:37]  Calculated signature: sha256=b52da71b6c01cac9b0da6efcb70565f9329574dcae60864b2bae0d72bef6cf86
[webhook] [2025-08-12 00:00:50] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 730c03942f116ade\n    [X-B3-Traceid] => 689a842f05c70f2f0285437155b9bd12\n    [B3] => 689a842f05c70f2f0285437155b9bd12-730c03942f116ade-1\n    [Traceparent] => 00-689a842f05c70f2f0285437155b9bd12-730c03942f116ade-01\n    [X-Amzn-Trace-Id] => Root=1-689a842f-05c70f2f0285437155b9bd12;Parent=730c03942f116ade;Sampled=1\n    [X-Adsk-Signature] => sha256=83416783c07530e13b7cbf39bb98e6d54a241a739920ac502dcad4b70b3181a7\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 41669255-3496-4ee3-9134-a2cb4777e9bb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-12 00:00:50] [adwsapi_v2.php:57]  Received webhook data: {"id":"41669255-3496-4ee3-9134-a2cb4777e9bb","topic":"product-catalog","event":"changed","sender":"Autodesk Catalog","environment":"prd","payload":{"message":"Catalog is updated.","changeType":"Product_Catalog_Change","modifiedAt":"2025-08-12T00:00:47Z"},"publishedAt":"2025-08-12T00:00:48.000Z","country":"GB"}
[webhook] [2025-08-12 08:09:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-12 08:09:12
[webhook] [2025-08-12 08:09:12] [adwsapi_v2.php:36]  Provided signature: sha256=0856077611a7a631d485faf6e95eaed4a0660a306fbb1f03be9bd9d40c2551b2
[webhook] [2025-08-12 08:09:12] [adwsapi_v2.php:37]  Calculated signature: sha256=0856077611a7a631d485faf6e95eaed4a0660a306fbb1f03be9bd9d40c2551b2
[webhook] [2025-08-12 08:09:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 4081931b0357e2ce\n    [X-B3-Traceid] => 689af6a50df7d78edac7cfa4d43841f3\n    [B3] => 689af6a50df7d78edac7cfa4d43841f3-4081931b0357e2ce-1\n    [Traceparent] => 00-689af6a50df7d78edac7cfa4d43841f3-4081931b0357e2ce-01\n    [X-Amzn-Trace-Id] => Root=1-689af6a5-0df7d78edac7cfa4d43841f3;Parent=4081931b0357e2ce;Sampled=1\n    [X-Adsk-Signature] => sha256=0856077611a7a631d485faf6e95eaed4a0660a306fbb1f03be9bd9d40c2551b2\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 421bb94f-841c-4884-af8f-9c6ef331d95e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-12 08:09:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"421bb94f-841c-4884-af8f-9c6ef331d95e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-998113","transactionId":"2a49818f-fc12-52a6-9ee6-e09b04c736e5","quoteStatus":"Draft","message":"Quote# Q-998113 status changed to Draft.","modifiedAt":"2025-08-12T08:09:09.435Z"},"publishedAt":"2025-08-12T08:09:09.000Z","csn":"5103159758"}
[webhook] [2025-08-12 08:09:12] [adwsapi_v2.php:20]  Webhook request received at 2025-08-12 08:09:12
[webhook] [2025-08-12 08:09:12] [adwsapi_v2.php:36]  Provided signature: sha256=0a64c877226b65d10d88bdf1782711ab857bfacb87f4b19331dd8bf3742d020b
[webhook] [2025-08-12 08:09:12] [adwsapi_v2.php:37]  Calculated signature: sha256=0856077611a7a631d485faf6e95eaed4a0660a306fbb1f03be9bd9d40c2551b2
[webhook] [2025-08-12 08:09:12] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 66152e588acaf307\n    [X-B3-Traceid] => 689af6a50df7d78edac7cfa4d43841f3\n    [B3] => 689af6a50df7d78edac7cfa4d43841f3-66152e588acaf307-1\n    [Traceparent] => 00-689af6a50df7d78edac7cfa4d43841f3-66152e588acaf307-01\n    [X-Amzn-Trace-Id] => Root=1-689af6a5-0df7d78edac7cfa4d43841f3;Parent=66152e588acaf307;Sampled=1\n    [X-Adsk-Signature] => sha256=0a64c877226b65d10d88bdf1782711ab857bfacb87f4b19331dd8bf3742d020b\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 421bb94f-841c-4884-af8f-9c6ef331d95e\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-12 08:09:12] [adwsapi_v2.php:57]  Received webhook data: {"id":"421bb94f-841c-4884-af8f-9c6ef331d95e","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-998113","transactionId":"2a49818f-fc12-52a6-9ee6-e09b04c736e5","quoteStatus":"Draft","message":"Quote# Q-998113 status changed to Draft.","modifiedAt":"2025-08-12T08:09:09.435Z"},"publishedAt":"2025-08-12T08:09:09.000Z","csn":"5103159758"}
[webhook] [2025-08-12 08:10:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-12 08:10:17
[webhook] [2025-08-12 08:10:17] [adwsapi_v2.php:36]  Provided signature: sha256=77f3c3164043369f211d798bd798200fcb3cd01feaf45babf212316e8eb835d8
[webhook] [2025-08-12 08:10:17] [adwsapi_v2.php:37]  Calculated signature: sha256=77f3c3164043369f211d798bd798200fcb3cd01feaf45babf212316e8eb835d8
[webhook] [2025-08-12 08:10:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => f5237662adc86bf2\n    [X-B3-Traceid] => 689af6e7ebed67f2f1008186f2fade77\n    [B3] => 689af6e7ebed67f2f1008186f2fade77-f5237662adc86bf2-1\n    [Traceparent] => 00-689af6e7ebed67f2f1008186f2fade77-f5237662adc86bf2-01\n    [X-Amzn-Trace-Id] => Root=1-689af6e7-ebed67f2f1008186f2fade77;Parent=f5237662adc86bf2;Sampled=1\n    [X-Adsk-Signature] => sha256=77f3c3164043369f211d798bd798200fcb3cd01feaf45babf212316e8eb835d8\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 54e02fbe-62dc-41b6-916b-6a098fc02ceb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-12 08:10:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"54e02fbe-62dc-41b6-916b-6a098fc02ceb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-998113","transactionId":"2a49818f-fc12-52a6-9ee6-e09b04c736e5","quoteStatus":"Quoted","message":"Quote# Q-998113 status changed to Quoted.","modifiedAt":"2025-08-12T08:10:15.404Z"},"publishedAt":"2025-08-12T08:10:15.000Z","csn":"5103159758"}
[webhook] [2025-08-12 08:10:17] [adwsapi_v2.php:20]  Webhook request received at 2025-08-12 08:10:17
[webhook] [2025-08-12 08:10:17] [adwsapi_v2.php:36]  Provided signature: sha256=318aac64a445d8597ac9d81c0af3fde93f07518e996bad07cf0968339a149ebe
[webhook] [2025-08-12 08:10:17] [adwsapi_v2.php:37]  Calculated signature: sha256=77f3c3164043369f211d798bd798200fcb3cd01feaf45babf212316e8eb835d8
[webhook] [2025-08-12 08:10:17] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => deec58e053aea37c\n    [X-B3-Traceid] => 689af6e7ebed67f2f1008186f2fade77\n    [B3] => 689af6e7ebed67f2f1008186f2fade77-deec58e053aea37c-1\n    [Traceparent] => 00-689af6e7ebed67f2f1008186f2fade77-deec58e053aea37c-01\n    [X-Amzn-Trace-Id] => Root=1-689af6e7-ebed67f2f1008186f2fade77;Parent=deec58e053aea37c;Sampled=1\n    [X-Adsk-Signature] => sha256=318aac64a445d8597ac9d81c0af3fde93f07518e996bad07cf0968339a149ebe\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => 54e02fbe-62dc-41b6-916b-6a098fc02ceb\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-12 08:10:17] [adwsapi_v2.php:57]  Received webhook data: {"id":"54e02fbe-62dc-41b6-916b-6a098fc02ceb","topic":"quote-status","event":"changed","sender":"Autodesk Quote Status","environment":"prd","payload":{"quoteNumber":"Q-998113","transactionId":"2a49818f-fc12-52a6-9ee6-e09b04c736e5","quoteStatus":"Quoted","message":"Quote# Q-998113 status changed to Quoted.","modifiedAt":"2025-08-12T08:10:15.404Z"},"publishedAt":"2025-08-12T08:10:15.000Z","csn":"5103159758"}
[webhook] [2025-08-12 08:15:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-12 08:15:48
[webhook] [2025-08-12 08:15:48] [adwsapi_v2.php:36]  Provided signature: sha256=531b4e285f6d375f017e0c3794696aae641973d4d82fd5cafe87559beaf31c48
[webhook] [2025-08-12 08:15:48] [adwsapi_v2.php:37]  Calculated signature: sha256=7e9c171e7ec9fb4ad063fd2e734a71fabae437fc7eab24a5af23da4fbd772abc
[webhook] [2025-08-12 08:15:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => c13ec73370850885\n    [X-B3-Traceid] => 689af8320184e7a9429ffff701d3718a\n    [B3] => 689af8320184e7a9429ffff701d3718a-c13ec73370850885-1\n    [Traceparent] => 00-689af8320184e7a9429ffff701d3718a-c13ec73370850885-01\n    [X-Amzn-Trace-Id] => Root=1-689af832-0184e7a9429ffff701d3718a;Parent=c13ec73370850885;Sampled=1\n    [X-Adsk-Signature] => sha256=531b4e285f6d375f017e0c3794696aae641973d4d82fd5cafe87559beaf31c48\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dfe091f9-3975-41d6-989a-0aa1c0ce458d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => www.cadservices.co.uk\n)\n
[webhook] [2025-08-12 08:15:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"dfe091f9-3975-41d6-989a-0aa1c0ce458d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69201884897330","status":"Active","quantity":1,"endDate":"2026-08-13","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-12T08:00:43.000+0000"},"publishedAt":"2025-08-12T08:15:46.000Z","csn":"5103159758"}
[webhook] [2025-08-12 08:15:48] [adwsapi_v2.php:20]  Webhook request received at 2025-08-12 08:15:48
[webhook] [2025-08-12 08:15:48] [adwsapi_v2.php:36]  Provided signature: sha256=7e9c171e7ec9fb4ad063fd2e734a71fabae437fc7eab24a5af23da4fbd772abc
[webhook] [2025-08-12 08:15:48] [adwsapi_v2.php:37]  Calculated signature: sha256=7e9c171e7ec9fb4ad063fd2e734a71fabae437fc7eab24a5af23da4fbd772abc
[webhook] [2025-08-12 08:15:48] [adwsapi_v2.php:54]  Request headers: Array\n(\n    [X-B3-Sampled] => 1\n    [X-B3-Spanid] => 5bd3502511c03859\n    [X-B3-Traceid] => 689af8320184e7a9429ffff701d3718a\n    [B3] => 689af8320184e7a9429ffff701d3718a-5bd3502511c03859-1\n    [Traceparent] => 00-689af8320184e7a9429ffff701d3718a-5bd3502511c03859-01\n    [X-Amzn-Trace-Id] => Root=1-689af832-0184e7a9429ffff701d3718a;Parent=5bd3502511c03859;Sampled=1\n    [X-Adsk-Signature] => sha256=7e9c171e7ec9fb4ad063fd2e734a71fabae437fc7eab24a5af23da4fbd772abc\n    [X-Adsk-Region] => us-east-1\n    [Idempotency-Key] => dfe091f9-3975-41d6-989a-0aa1c0ce458d\n    [User-Agent] => PWS Notification Service\n    [Accept] => application/json, text/plain, */*\n    [Connection] => close\n    [X-Accel-Internal] => /internal-nginx-static-location\n    [X-Real-Ip] => **************\n    [Host] => test.cadservices.co.uk\n)\n
[webhook] [2025-08-12 08:15:48] [adwsapi_v2.php:57]  Received webhook data: {"id":"dfe091f9-3975-41d6-989a-0aa1c0ce458d","topic":"subscription-change","event":"changed","sender":"PWS Subscription Service","environment":"prd","payload":{"subscriptionId":"69201884897330","status":"Active","quantity":1,"endDate":"2026-08-13","autoRenew":"ON","term":"Annual","message":"subscription status,quantity,endDate,autoRenew,term changed.","modifiedAt":"2025-08-12T08:00:43.000+0000"},"publishedAt":"2025-08-12T08:15:46.000Z","csn":"5103159758"}
