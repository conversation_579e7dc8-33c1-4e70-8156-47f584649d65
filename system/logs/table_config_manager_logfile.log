[table_config_manager] [2025-08-11 13:16:16] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_bluebeam_data, route: enhanced_autobooks_bluebeam_data
[table_config_manager] [2025-08-11 13:16:16] [table_config_manager.class.php:181] Legacy config created for: autobooks_bluebeam_data
[table_config_manager] [2025-08-11 13:16:16] [table_config_manager.class.php:202] Unified config stored for: autobooks_bluebeam_data
[table_config_manager] [2025-08-11 13:20:38] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_sketchup_data, route: enhanced_autobooks_sketchup_data
[table_config_manager] [2025-08-11 13:20:38] [table_config_manager.class.php:181] Legacy config created for: autobooks_sketchup_data
[table_config_manager] [2025-08-11 13:20:38] [table_config_manager.class.php:202] Unified config stored for: autobooks_sketchup_data
[table_config_manager] [2025-08-11 13:29:54] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_Sketchup_data, route: enhanced_autobooks_Sketchup_data
[table_config_manager] [2025-08-11 13:29:54] [table_config_manager.class.php:181] Legacy config created for: autobooks_Sketchup_data
[table_config_manager] [2025-08-11 13:29:54] [table_config_manager.class.php:202] Unified config stored for: autobooks_Sketchup_data
[table_config_manager] [2025-08-11 13:35:58] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_sketchup_data, route: enhanced_autobooks_sketchup_data
[table_config_manager] [2025-08-11 13:35:58] [table_config_manager.class.php:181] Legacy config updated for: autobooks_sketchup_data
[table_config_manager] [2025-08-11 13:35:58] [table_config_manager.class.php:202] Unified config stored for: autobooks_sketchup_data
[table_config_manager] [2025-08-11 13:47:35] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_bluebeam_data, route: enhanced_autobooks_bluebeam_data
[table_config_manager] [2025-08-11 13:47:35] [table_config_manager.class.php:181] Legacy config updated for: autobooks_bluebeam_data
[table_config_manager] [2025-08-11 13:47:35] [table_config_manager.class.php:202] Unified config stored for: autobooks_bluebeam_data
[table_config_manager] [2025-08-11 13:49:45] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_sketchup_data, route: enhanced_autobooks_sketchup_data
[table_config_manager] [2025-08-11 13:49:45] [table_config_manager.class.php:181] Legacy config updated for: autobooks_sketchup_data
[table_config_manager] [2025-08-11 13:49:45] [table_config_manager.class.php:202] Unified config stored for: autobooks_sketchup_data
[table_config_manager] [2025-08-11 14:22:10] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_sketchup_data, route: enhanced_autobooks_import_sketchup_data
[table_config_manager] [2025-08-11 14:22:10] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-11 14:22:10] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_sketchup_data
[table_config_manager] [2025-08-11 14:22:53] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-11 14:22:53] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-11 14:22:53] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-11 21:50:34] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-11 21:50:34] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-11 21:50:34] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-11 21:59:30] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-11 21:59:30] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-11 21:59:30] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:27:28] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:27:28] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:27:28] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:33:10] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:33:10] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:33:10] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:39:05] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:39:05] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:39:05] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:45:39] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:45:39] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:45:39] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:54:40] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:54:40] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 08:54:40] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:01:04] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:01:04] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:01:04] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:09:09] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:09:09] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:09:09] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:19:58] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:19:58] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:19:58] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:42:21] [table_config_manager.class.php:63]  store_table_config called for table: autobooks_import_bluebeam_data, route: enhanced_autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:42:21] [table_config_manager.class.php:181] Legacy config created for: autobooks_import_bluebeam_data
[table_config_manager] [2025-08-12 09:42:21] [table_config_manager.class.php:202] Unified config stored for: autobooks_import_bluebeam_data
