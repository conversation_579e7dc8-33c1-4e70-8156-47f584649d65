
-- --------------------------------------------------------

--
-- Table structure for table `email_campaign_recipients`
--

CREATE TABLE `email_campaign_recipients` (
  `id` int(11) NOT NULL,
  `campaign_id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `recipient_type` enum('customer','subscription','manual','imported') DEFAULT 'manual',
  `reference_id` varchar(100) DEFAULT NULL COMMENT 'Reference to customer/subscription ID',
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Additional recipient data for personalization',
  `is_active` tinyint(1) DEFAULT 1,
  `unsubscribed` tinyint(1) DEFAULT 0,
  `unsubscribed_at` timestamp NULL DEFAULT NULL,
  `added_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Campaign recipient management';
