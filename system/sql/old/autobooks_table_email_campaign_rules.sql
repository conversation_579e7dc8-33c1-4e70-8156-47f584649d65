
-- --------------------------------------------------------

--
-- Table structure for table `email_campaign_rules`
--

CREATE TABLE `email_campaign_rules` (
  `id` int(11) NOT NULL,
  `campaign_id` int(11) NOT NULL,
  `rule_name` varchar(255) NOT NULL,
  `rule_type` enum('time_based','event_based','condition_based') NOT NULL,
  `rule_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Rule configuration parameters',
  `priority` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Campaign rule definitions';
