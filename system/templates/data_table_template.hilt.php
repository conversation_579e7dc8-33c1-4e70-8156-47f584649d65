@props([
    'name' => 'Data-Table with Data Source',
    'description' => 'A modern data table using data sources with automatic schema detection and typed columns',
    'type' => 'csv',
    'table_name' => '{{ $table_name }}',
    'route_key' => '{{ $route_key }}',
    'show_analysis' => false
])
{{-- @use system\data_source_manager;// no longer needed edge.class will auto insert it  --}}

@php
// Set default values
$table_name = $table_name ?? '';
$route_key = $route_key ?? '';
$show_analysis = $show_analysis ?? false;

// Get request parameters for pagination and sorting
$page = (int)($_GET['page'] ?? 1);
$per_page = (int)($_GET['per_page'] ?? 50);
$sort_column = $_GET['sort_column'] ?? '';
$sort_direction = $_GET['sort_direction'] ?? 'asc';

// Build criteria for data retrieval
$criteria = [
    'limit' => $per_page,
    'offset' => ($page - 1) * $per_page
];

if (!empty($sort_column)) {
    $criteria['order_by'] = $sort_column;
    $criteria['order_direction'] = $sort_direction;
}

// Check if table exists
$table_exists = false;
$analysis_data = null;
$error_message = null;
$data_source_id = null;
$table_result = null;

try {
    // UPDATED TEMPLATE VERSION - Check if table exists
    tcs_log("=== UPDATED TEMPLATE VERSION 2.0 STARTING ===", 'data_source_template');
    tcs_log("Processing table: $table_name", 'data_source_template');

    $table_exists = database::tableExists($table_name);

    if (!$table_exists) {
        $error_message = "Table '{$table_name}' does not exist. Please upload CSV data to create it.";
        tcs_log("Table does not exist: $table_name", 'data_source_template');
    } else {
        tcs_log("Table exists: $table_name", 'data_source_template');
        // Check if a data source exists for this table
        tcs_log("=== NEW TEMPLATE LOGIC: CHECKING DATA SOURCES ===", 'data_source_template');
        $all_sources = data_source_manager::get_data_sources();
        $existing_sources = [];

        tcs_log("Looking for data sources for table: $table_name", 'data_source_template');
        tcs_log("Found " . count($all_sources) . " total data sources", 'data_source_template');

        // Filter sources by exact table name match
        foreach ($all_sources as $source) {
            tcs_log("Checking data source ID {$source['id']}: '{$source['name']}' with table_name: '{$source['table_name']}'", 'data_source_template');
            if ($source['table_name'] === $table_name) {
                $existing_sources[] = $source;
                tcs_log("Found matching data source ID {$source['id']} for table: $table_name", 'data_source_template');
            }
        }

        if (empty($existing_sources)) {
            // Create a data source for this table automatically
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info) {
                $data_source_id = data_source_manager::create_data_source([
                        'name' => $name . ' - ' . $table_name,
                        'table_name' => $table_name,
                        'description' => 'Auto-created data source for ' . $table_name,
                        'category' => 'data_table',
                        'status' => 'active'
                    ]
                );
                tcs_log("Auto-created data source ID: $data_source_id for table: $table_name", 'data_source_template');
            } else {
                throw new Exception("Could not get table information for $table_name");
            }
        } else {
            // Use existing data source
            $data_source_id = $existing_sources[0]['id'];
            tcs_log("Using existing data source ID: $data_source_id for table: $table_name", 'data_source_template');
        }

        // Generate basic columns from table schema for fallback
        $table_info = data_source_manager::get_table_info($table_name);
        $fallback_columns = [];
        $available_fields = [];

        if ($table_info && isset($table_info['columns'])) {
            foreach ($table_info['columns'] as $column) {
                $column_name = $column['name'];
                $available_fields[] = $column_name;

                // Skip system columns
                if (in_array($column_name, ['id', 'created_at', 'updated_at', 'data_hash'])) {
                    continue;
                }

                $fallback_columns[] = [
                    'label' => ucfirst(str_replace('_', ' ', $column_name)),
                    'field' => $column_name,
                    'filter' => true
                ];
            }
        }

        // Get data from the data source
        $data_result = data_source_manager::get_data_source_data($data_source_id, $criteria);

        if (!$data_result['success']) {
            throw new Exception("Failed to retrieve data from data source: " . ($data_result['error'] ?? 'Unknown error'));
        }

        $table_data = $data_result['data'];
        $total_count = $data_result['total_count'] ?? count($table_data);

        tcs_log("Retrieved " . count($table_data) . " rows from data source ID: $data_source_id", 'data_source_template');

        // If showing analysis and we have CSV data, analyze it
        if ($show_analysis && isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
            $csv_file_path = $_FILES['csv_file']['tmp_name'];
            $analysis_result = data_importer::analyze_csv_structure($csv_file_path);
            if (isset($analysis_result['success'])) {
                $analysis_data = $analysis_result;
            }
        }
    }
} catch (Exception $e) {
    $error_message = "Error: " . $e->getMessage();
    tcs_log("Template error: " . $e->getMessage(), 'data_source_template');
}
@endphp

<div class="enhanced-data-table-container">
    {{-- Header Section --}}
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900">{{ $name }}</h2>
        <p class="text-gray-600 mt-1">{{ $description }}</p>
    </div>

    {{-- Error Display --}}
    @if($error_message)
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Error</h3>
                    <div class="mt-2 text-sm text-red-700">
                        {{ $error_message }}
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- CSV Upload Section (shown when table doesn't exist or analysis is requested) --}}
    @if(!$table_exists || $show_analysis)
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
            <h3 class="text-lg font-medium text-blue-900 mb-3">
                @if(!$table_exists)
                    Create Table from CSV Data
                @else
                    Analyze CSV Structure
                @endif
            </h3>
            
            <form hx-post="{{ APP_ROOT }}/api/enhanced_data/upload_csv" hx-target="#upload-result" hx-swap="innerHTML" enctype="multipart/form-data">
                <input type="hidden" name="table_name" value="{{ $table_name }}">
                <input type="hidden" name="route_key" value="{{ $route_key }}">

                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="csv_file" class="block text-sm font-medium text-gray-700">CSV File</label>
                        <input type="file" name="csv_file" id="csv_file" accept=".csv" 
                               class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    </div>
                    
                    <div class="flex items-end">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            @if(!$table_exists)
                                <i class="fas fa-upload mr-2"></i>Create Table & Import Data
                            @else
                                <i class="fas fa-upload mr-2"></i>Import Additional Data
                            @endif
                        </button>
                    </div>
                </div>
                
                <div class="mt-3">
                    <label class="flex items-center">
                        <input type="checkbox" name="replace_table" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-600">Replace existing table if it exists</span>
                    </label>
                </div>
            </form>
            
            <div id="upload-result" class="mt-4"></div>
        </div>
    @endif

    {{-- Analysis Results Display --}}
    @if($analysis_data)
        <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
            <h3 class="text-lg font-medium text-green-900 mb-3">CSV Analysis Results</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ count($analysis_data['headers']) }}</div>
                    <div class="text-sm text-gray-600">Columns</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ $analysis_data['total_rows'] }}</div>
                    <div class="text-sm text-gray-600">Total Rows</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ $analysis_data['analyzed_rows'] }}</div>
                    <div class="text-sm text-gray-600">Analyzed Rows</div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Detected Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sample Data</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($analysis_data['headers'] as $header)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $header }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $analysis_data['data_types'][$header] }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    {{ implode(', ', array_slice($analysis_data['sample_data'][$header], 0, 3)) }}
                                    @if(count($analysis_data['sample_data'][$header]) > 3)
                                        <span class="text-gray-400">...</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif

    {{-- Data Table Section --}}
    @if($table_exists && !$error_message && isset($table_data))
        <div class="bg-white shadow rounded-lg">
            {{-- Data Table using Data Source --}}
            <div id="data-table-container">
                <x-data-table
                    :title="$name"
                    :description="'Data from ' . $table_name"
                    :items="$table_data"
                    :columns="$fallback_columns"
                    :available_fields="$available_fields"
                    :rows="[
                        'id_prefix' => 'row_',
                        'id_field' => 'id',
                        'class_postfix' => '',
                        'extra_parameters' => ''
                    ]"
                    :just_body="false"
                    :just_rows="false"
                    :items_per_page="$per_page"
                    :current_page_num="$page"
                    :sort_column="$sort_column"
                    :sort_direction="$sort_direction"
                    :callback="''"
                    :class="'data-source-table'"
                    :table_name="$table_name"
                    :show_data_source_selector="true"
                    :data_source_id="$data_source_id"
                    :data_source_type="$data_source_type"
                    :total_count="$total_count"
                />
            </div>
        </div>
    @endif

    {{-- Help Section --}}
    @if(!$table_exists)
        <div class="mt-6 bg-gray-50 border border-gray-200 rounded-md p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Data Source Import</h3>
            <div class="text-sm text-gray-600 space-y-2">
                <p>1. Upload a CSV file using the form above</p>
                <p>2. The system automatically analyzes data types (strings, numbers, dates, booleans)</p>
                <p>3. A database table is created with individual typed columns (no JSON storage)</p>
                <p>4. Data is imported into properly typed columns for optimal performance</p>
                <p>5. A data source is automatically created for the table</p>
                <p>6. Use the column manager to configure display options and switch data sources</p>
            </div>
            <div class="mt-3 text-xs text-blue-600">
                <strong>Benefits:</strong> Data source flexibility, faster queries, better filtering, proper indexing, SQL compatibility
            </div>
        </div>
    @endif

    {{-- Data Source Information --}}
    @if($table_exists && $data_source_id)
        <div class="mt-6 bg-green-50 border border-green-200 rounded-md p-4">
            <h3 class="text-lg font-medium text-green-900 mb-2">Data Source Active</h3>
            <div class="text-sm text-green-600 space-y-1">
                <p><strong>Table:</strong> {{ $table_name }}</p>
                <p><strong>Data Source ID:</strong> {{ $data_source_id }}</p>
                <p>Use the column manager (gear icon) to configure display options or switch to different data sources.</p>
            </div>
        </div>
    @endif
</div>

{{-- JavaScript for data source functionality --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh functionality for data source updates
    const autoRefresh = document.querySelector('[data-auto-refresh]');
    if (autoRefresh) {
        setInterval(() => {
            htmx.trigger('#data-table-container', 'refresh');
        }, 30000); // Refresh every 30 seconds
    }

    // Handle data source changes
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'data-table-container') {
            console.log('Data table updated via data source');
        }
    });
});
</script>
