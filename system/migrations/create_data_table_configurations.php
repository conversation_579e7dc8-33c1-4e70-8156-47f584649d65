<?php
/**
 * Migration to create data_table_configurations table
 * This table stores column configurations for data tables with data sources
 */

// Simple database connection for migration
$config = include dirname(__DIR__) . '/config/db_config_local.php';

try {
    $pdo = new PDO(
        "mysql:host={$config['host']};dbname={$config['database']};charset=utf8mb4",
        $config['username'],
        $config['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

try {
    // Check if table already exists
    $check_query = "SHOW TABLES LIKE 'autobooks_data_table_configurations'";
    $stmt = $pdo->query($check_query);
    $exists = $stmt->fetch();

    if (!$exists) {
        $create_sql = "
        CREATE TABLE `autobooks_data_table_configurations` (
            `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `table_name` varchar(255) NOT NULL COMMENT 'Unique identifier for the data table instance',
            `data_source_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'Associated data source ID',
            `column_structure` longtext DEFAULT NULL COMMENT 'JSON array of column configurations',
            `hidden_columns` longtext DEFAULT NULL COMMENT 'JSON array of hidden column IDs',
            `settings` longtext DEFAULT NULL COMMENT 'JSON object for additional table settings',
            `created_by` int(10) UNSIGNED DEFAULT NULL COMMENT 'User who created this configuration',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_table_name` (`table_name`),
            KEY `idx_data_source_id` (`data_source_id`),
            KEY `idx_created_by` (`created_by`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores data table column configurations and preferences'
        ";

        $pdo->exec($create_sql);
        echo "✅ Created autobooks_data_table_configurations table successfully.\n";
    } else {
        echo "ℹ️ Table autobooks_data_table_configurations already exists.\n";
    }

    // Add foreign key constraints if they don't exist
    try {
        $add_fk_sql = "
        ALTER TABLE `autobooks_data_table_configurations` 
        ADD CONSTRAINT `fk_data_table_config_data_source` 
        FOREIGN KEY (`data_source_id`) REFERENCES `autobooks_data_sources` (`id`) 
        ON DELETE SET NULL ON UPDATE CASCADE
        ";
        $pdo->exec($add_fk_sql);
        echo "✅ Added foreign key constraint for data_source_id.\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') === false) {
            echo "⚠️ Could not add foreign key constraint: " . $e->getMessage() . "\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Error creating table: " . $e->getMessage() . "\n";
}
?>
